#
# Audit policies
#
audit:
  alerts:
    #
    # category: malicious packages (publicly known and unknown)
    #
    malicious:
      contains known malware:
        - reason: package is known to contain a dangerous malware
        - enabled: true
      typo-squatting or repo-jacking package:
        - reason: package impersonates another popular package to propagate malware
        - enabled: true

    #
    # alert category: suspicious packages (potentially malicious)
    #
    suspicious:
      inconsistent with repo source:
        - reason: package code inconsistent with the public repo source code
        - enabled: false # WIP
      overwrites system binaries:
        - reason: package code inconsistent with the public repo source code
        - enabled: false # WIP

    #
    # alert category: packages vulnerable to code exploits
    #
    vulnerable:
      contains known vulnerabilities:
        - reason: known vulnerabilities (CVEs) in package code could be exploited
        - enabled: true
      insecure network communication:
        - reason: package code uses insecure network communication (not https)
        - enabled: false # WIP

    #
    # packages with undesirable or "risky" attributes
    #
    undesirable:
      package is old or abandoned:
        - reason: old or abandoned packages receive no security updates and are risky
        - enabled: true

      invalid or no author email:
        - reason: a package with lack of or invalid author email suggests 2FA not enabled
        - enabled: true

      invalid or no homepage:
        - reason: a package with no or invalid homepage may not be preferable
        - enabled: false

      no source repo:
        - reason: lack of public source repo may suggest malicious intention
        - enabled: true

      fewer downloads:
        - reason: a package with few downloads may not be preferable
        - enabled: true

      no or insufficient readme:
        - reason: a package with lack of documentation may not be preferable
        - enabled: false

      fewer versions or releases:
        - reason: few versions suggest unstable or inactive project
        - enabled: true

      too many dependencies:
        - reason: too many dependencies increase attack surface
        - enabled: false

      version release after a long gap:
        - reason: a release after a long time may indicate account hijacking
        - enabled: false

      contains custom installation hooks:
        - reason: custom installation hooks may download or execute malicious code
        - enabled: false # WIP

      #
      # type: repo stats
      #
      few source repo stars:
        - reason: a package with few repo stars may not be preferable
        - enabled: false

      few source repo forks:
        - reason: a package with few repo forks may not be preferable
        - enabled: false

      forked source repo:
        - reason: a forked copy of a popular package may contain malicious code
        - enabled: true

      #
      # type: APIs and permissions
      #
      generates new code:
        - reason: package generates new code at runtime, which could be malicious
        - enabled: false
      forks or exits OS processes:
        - reason: package spawns new operating system processes, which could be malicious
        - enabled: false
      accesses obfuscated (hidden) code:
        - enabled: true
      accesses environment variables:
        - enabled: false
      changes system/environment variables:
        - enabled: false
      accesses files and dirs:
        - enabled: false
      communicates with external network:
        - enabled: false
      reads user input:
        - enabled: false

#
# Sandboxing policies
#
sandbox:
  rules:
    #
    # File system (allow or block accesses to file/dirs)
    #
    #   ~/ represents home dir
    #   . represents cwd dir
    #
    # NOTE: only ONE 'allow' and 'block' lines are allowed
    #
    fs:
      # TODO: customize as per your threat model

      # block access to home dir and all other locations (except the ones below)
      block: ~/, /
      allow: ., ~/.cache, ~/.npm, ~/.local, ~/.ruby, /tmp, /proc, /etc, /var, /bin, /usr/include, /usr/local, /usr/bin, /usr/lib, /usr/share, /lib

    #
    # Network (allow or block domains/ports)
    #
    # NOTE: only ONE 'allow' and 'block' lines are allowed
    #
    network:

      # TODO: customize as per your threat model

      # block all external network communication (except the ones below)
      block: 0.0.0.0

      # For NPM packages
      allow: registry.yarnpkg.com:0, npmjs.org:0, npmjs.com:0