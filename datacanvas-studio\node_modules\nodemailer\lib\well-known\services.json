{"1und1": {"description": "1&1 Mail (German hosting provider)", "host": "smtp.1und1.de", "port": 465, "secure": true, "authMethod": "LOGIN"}, "126": {"description": "126 Mail (NetEase)", "host": "smtp.126.com", "port": 465, "secure": true}, "163": {"description": "163 Mail (NetEase)", "host": "smtp.163.com", "port": 465, "secure": true}, "Aliyun": {"description": "Alibaba Cloud Mail", "domains": ["aliyun.com"], "host": "smtp.aliyun.com", "port": 465, "secure": true}, "AliyunQiye": {"description": "Alibaba Cloud Enterprise Mail", "host": "smtp.qiye.aliyun.com", "port": 465, "secure": true}, "AOL": {"description": "AOL Mail", "domains": ["aol.com"], "host": "smtp.aol.com", "port": 587}, "Bluewin": {"description": "Bluewin (Swiss email provider)", "host": "smtpauths.bluewin.ch", "domains": ["bluewin.ch"], "port": 465}, "DebugMail": {"description": "DebugMail (email testing service)", "host": "debugmail.io", "port": 25}, "DynectEmail": {"description": "<PERSON><PERSON>", "aliases": ["Dynect"], "host": "smtp.dynect.net", "port": 25}, "ElasticEmail": {"description": "Elastic Email", "aliases": ["Elastic Email"], "host": "smtp.elasticemail.com", "port": 465, "secure": true}, "Ethereal": {"description": "Ethereal Email (email testing service)", "aliases": ["ethereal.email"], "host": "smtp.ethereal.email", "port": 587}, "FastMail": {"description": "FastMail", "domains": ["fastmail.fm"], "host": "smtp.fastmail.com", "port": 465, "secure": true}, "Feishu Mail": {"description": "Feishu Mail (Lark)", "aliases": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "domains": ["www.feishu.cn"], "host": "smtp.feishu.cn", "port": 465, "secure": true}, "Forward Email": {"description": "Forward Email (email forwarding service)", "aliases": ["FE", "ForwardEmail"], "domains": ["forwardemail.net"], "host": "smtp.forwardemail.net", "port": 465, "secure": true}, "GandiMail": {"description": "Gandi Mail", "aliases": ["<PERSON><PERSON><PERSON>", "Gandi Mail"], "host": "mail.gandi.net", "port": 587}, "Gmail": {"description": "Gmail", "aliases": ["Google Mail"], "domains": ["gmail.com", "googlemail.com"], "host": "smtp.gmail.com", "port": 465, "secure": true}, "GMX": {"description": "GMX Mail", "domains": ["gmx.com", "gmx.net", "gmx.de"], "host": "mail.gmx.com", "port": 587}, "Godaddy": {"description": "<PERSON><PERSON><PERSON><PERSON> (US)", "host": "smtpout.secureserver.net", "port": 25}, "GodaddyAsia": {"description": "<PERSON><PERSON><PERSON><PERSON> (Asia)", "host": "smtp.asia.secureserver.net", "port": 25}, "GodaddyEurope": {"description": "<PERSON><PERSON><PERSON><PERSON> (Europe)", "host": "smtp.europe.secureserver.net", "port": 25}, "hot.ee": {"description": "Hot.ee (Estonian email provider)", "host": "mail.hot.ee"}, "Hotmail": {"description": "Outlook.com / Hotmail", "aliases": ["Outlook", "Outlook.com", "Hotmail.com"], "domains": ["hotmail.com", "outlook.com"], "host": "smtp-mail.outlook.com", "port": 587}, "iCloud": {"description": "iCloud Mail", "aliases": ["Me", "<PERSON>"], "domains": ["me.com", "mac.com"], "host": "smtp.mail.me.com", "port": 587}, "Infomaniak": {"description": "Infomaniak Mail (Swiss hosting provider)", "host": "mail.infomaniak.com", "domains": ["ik.me", "ikmail.com", "etik.com"], "port": 587}, "Loopia": {"description": "Loopia (Swedish hosting provider)", "host": "mailcluster.loopia.se", "port": 465}, "Loops": {"description": "Loops", "host": "smtp.loops.so", "port": 587}, "mail.ee": {"description": "Mail.ee (Estonian email provider)", "host": "smtp.mail.ee"}, "Mail.ru": {"description": "Mail.ru", "host": "smtp.mail.ru", "port": 465, "secure": true}, "Mailcatch.app": {"description": "Mailcatch (email testing service)", "host": "sandbox-smtp.mailcatch.app", "port": 2525}, "Maildev": {"description": "MailDev (local email testing)", "port": 1025, "ignoreTLS": true}, "MailerSend": {"description": "MailerSend", "host": "smtp.mailersend.net", "port": 587}, "Mailgun": {"description": "Mailgun", "host": "smtp.mailgun.org", "port": 465, "secure": true}, "Mailjet": {"description": "Mailjet", "host": "in.mailjet.com", "port": 587}, "Mailosaur": {"description": "Mailosaur (email testing service)", "host": "mailosaur.io", "port": 25}, "Mailtrap": {"description": "Mailtrap", "host": "live.smtp.mailtrap.io", "port": 587}, "Mandrill": {"description": "Mandrill (by Mailchimp)", "host": "smtp.mandrillapp.com", "port": 587}, "Naver": {"description": "Naver Mail (Korean email provider)", "host": "smtp.naver.com", "port": 587}, "OhMySMTP": {"description": "OhMySMTP (email delivery service)", "host": "smtp.ohmysmtp.com", "port": 587, "secure": false}, "One": {"description": "One.com Email", "host": "send.one.com", "port": 465, "secure": true}, "OpenMailBox": {"description": "OpenMailBox", "aliases": ["OMB", "openmailbox.org"], "host": "smtp.openmailbox.org", "port": 465, "secure": true}, "Outlook365": {"description": "Microsoft 365 / Office 365", "host": "smtp.office365.com", "port": 587, "secure": false}, "Postmark": {"description": "Postmark", "aliases": ["PostmarkApp"], "host": "smtp.postmarkapp.com", "port": 2525}, "Proton": {"description": "Proton Mail", "aliases": ["ProtonMail", "Proton.me", "Protonmail.com", "Protonmail.ch"], "domains": ["proton.me", "protonmail.com", "pm.me", "protonmail.ch"], "host": "smtp.protonmail.ch", "port": 587, "requireTLS": true}, "qiye.aliyun": {"description": "Alibaba Mail Enterprise Edition", "host": "smtp.mxhichina.com", "port": "465", "secure": true}, "QQ": {"description": "QQ Mail", "domains": ["qq.com"], "host": "smtp.qq.com", "port": 465, "secure": true}, "QQex": {"description": "QQ Enterprise Mail", "aliases": ["QQ Enterprise"], "domains": ["exmail.qq.com"], "host": "smtp.exmail.qq.com", "port": 465, "secure": true}, "Resend": {"description": "Resend", "host": "smtp.resend.com", "port": 465, "secure": true}, "SendCloud": {"description": "SendCloud (Chinese email delivery)", "host": "smtp.sendcloud.net", "port": 2525}, "SendGrid": {"description": "SendGrid", "host": "smtp.sendgrid.net", "port": 587}, "SendinBlue": {"description": "Brevo (formerly Sendinblue)", "aliases": ["Brevo"], "host": "smtp-relay.brevo.com", "port": 587}, "SendPulse": {"description": "SendPulse", "host": "smtp-pulse.com", "port": 465, "secure": true}, "SES": {"description": "AWS SES US East (N. Virginia)", "host": "email-smtp.us-east-1.amazonaws.com", "port": 465, "secure": true}, "SES-AP-NORTHEAST-1": {"description": "AWS SES Asia Pacific (Tokyo)", "host": "email-smtp.ap-northeast-1.amazonaws.com", "port": 465, "secure": true}, "SES-AP-NORTHEAST-2": {"description": "AWS SES Asia Pacific (Seoul)", "host": "email-smtp.ap-northeast-2.amazonaws.com", "port": 465, "secure": true}, "SES-AP-NORTHEAST-3": {"description": "AWS SES Asia Pacific (Osaka)", "host": "email-smtp.ap-northeast-3.amazonaws.com", "port": 465, "secure": true}, "SES-AP-SOUTH-1": {"description": "AWS SES Asia Pacific (Mumbai)", "host": "email-smtp.ap-south-1.amazonaws.com", "port": 465, "secure": true}, "SES-AP-SOUTHEAST-1": {"description": "AWS SES Asia Pacific (Singapore)", "host": "email-smtp.ap-southeast-1.amazonaws.com", "port": 465, "secure": true}, "SES-AP-SOUTHEAST-2": {"description": "AWS SES Asia Pacific (Sydney)", "host": "email-smtp.ap-southeast-2.amazonaws.com", "port": 465, "secure": true}, "SES-CA-CENTRAL-1": {"description": "AWS SES Canada (Central)", "host": "email-smtp.ca-central-1.amazonaws.com", "port": 465, "secure": true}, "SES-EU-CENTRAL-1": {"description": "AWS SES Europe (Frankfurt)", "host": "email-smtp.eu-central-1.amazonaws.com", "port": 465, "secure": true}, "SES-EU-NORTH-1": {"description": "AWS SES Europe (Stockholm)", "host": "email-smtp.eu-north-1.amazonaws.com", "port": 465, "secure": true}, "SES-EU-WEST-1": {"description": "AWS SES Europe (Ireland)", "host": "email-smtp.eu-west-1.amazonaws.com", "port": 465, "secure": true}, "SES-EU-WEST-2": {"description": "AWS SES Europe (London)", "host": "email-smtp.eu-west-2.amazonaws.com", "port": 465, "secure": true}, "SES-EU-WEST-3": {"description": "AWS SES Europe (Paris)", "host": "email-smtp.eu-west-3.amazonaws.com", "port": 465, "secure": true}, "SES-SA-EAST-1": {"description": "AWS SES South America (São Paulo)", "host": "email-smtp.sa-east-1.amazonaws.com", "port": 465, "secure": true}, "SES-US-EAST-1": {"description": "AWS SES US East (N. Virginia)", "host": "email-smtp.us-east-1.amazonaws.com", "port": 465, "secure": true}, "SES-US-EAST-2": {"description": "AWS SES US East (Ohio)", "host": "email-smtp.us-east-2.amazonaws.com", "port": 465, "secure": true}, "SES-US-GOV-EAST-1": {"description": "AWS SES GovCloud (US-East)", "host": "email-smtp.us-gov-east-1.amazonaws.com", "port": 465, "secure": true}, "SES-US-GOV-WEST-1": {"description": "AWS SES GovCloud (US-West)", "host": "email-smtp.us-gov-west-1.amazonaws.com", "port": 465, "secure": true}, "SES-US-WEST-1": {"description": "AWS SES US West (N. California)", "host": "email-smtp.us-west-1.amazonaws.com", "port": 465, "secure": true}, "SES-US-WEST-2": {"description": "AWS SES US West (Oregon)", "host": "email-smtp.us-west-2.amazonaws.com", "port": 465, "secure": true}, "Seznam": {"description": "Seznam Email (Czech email provider)", "aliases": ["Seznam Email"], "domains": ["seznam.cz", "email.cz", "post.cz", "spoluzaci.cz"], "host": "smtp.seznam.cz", "port": 465, "secure": true}, "SMTP2GO": {"description": "SMTP2GO", "host": "mail.smtp2go.com", "port": 2525}, "Sparkpost": {"description": "SparkPost", "aliases": ["SparkPost", "SparkPost Mail"], "domains": ["sparkpost.com"], "host": "smtp.sparkpostmail.com", "port": 587, "secure": false}, "Tipimail": {"description": "Tipimail (email delivery service)", "host": "smtp.tipimail.com", "port": 587}, "Tutanota": {"description": "Tutanota (Tuta Mail)", "domains": ["tutanota.com", "tuta.com", "tutanota.de", "tuta.io"], "host": "smtp.tutanota.com", "port": 465, "secure": true}, "Yahoo": {"description": "Yahoo Mail", "domains": ["yahoo.com"], "host": "smtp.mail.yahoo.com", "port": 465, "secure": true}, "Yandex": {"description": "Yandex Mail", "domains": ["yandex.ru"], "host": "smtp.yandex.ru", "port": 465, "secure": true}, "Zoho": {"description": "Zoho Mail", "host": "smtp.zoho.com", "port": 465, "secure": true, "authMethod": "LOGIN"}}