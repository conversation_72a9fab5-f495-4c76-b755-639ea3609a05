import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import { requireAuth } from '@/lib/auth/auth';
import { DataProcessingService } from '@/lib/services/data-processing';
import { DatabaseService } from '@/lib/services/database';

export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth();
    
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Validate file type
    if (!file.name.endsWith('.csv')) {
      return NextResponse.json({ error: 'Only CSV files are allowed' }, { status: 400 });
    }

    // Validate file size (10MB limit)
    const maxSize = parseInt(process.env.MAX_FILE_SIZE || '10485760');
    if (file.size > maxSize) {
      return NextResponse.json({ error: 'File size exceeds limit' }, { status: 400 });
    }

    // Create upload directory if it doesn't exist
    const uploadDir = process.env.UPLOAD_DIR || './uploads';
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true });
    }

    // Generate unique filename
    const timestamp = Date.now();
    const filename = `${timestamp}-${file.name}`;
    const filePath = path.join(uploadDir, filename);

    // Save file
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(filePath, buffer);

    // Process CSV data
    const csvConfig = {
      file_path: filePath,
      delimiter: ',',
      has_header: true,
    };

    const processedData = await DataProcessingService.processCSVData(filePath, csvConfig);

    // Create data source
    const dataSourceResult = await DatabaseService.createDataSource({
      name: file.name,
      type: 'csv',
      connection_config: csvConfig,
      organization_id: user.organization_id!,
      created_by: user.id,
      is_active: true,
    });

    if (dataSourceResult.error) {
      return NextResponse.json({ error: dataSourceResult.error }, { status: 500 });
    }

    // Create dataset
    const datasetResult = await DatabaseService.createDataset({
      name: file.name.replace('.csv', ''),
      description: `Dataset from ${file.name}`,
      data_source_id: dataSourceResult.data!.id,
      schema_info: processedData.schema,
      sample_data: processedData.sampleData,
      row_count: processedData.rowCount,
      organization_id: user.organization_id!,
      created_by: user.id,
    });

    if (datasetResult.error) {
      return NextResponse.json({ error: datasetResult.error }, { status: 500 });
    }

    // Create audit log
    await DatabaseService.createAuditLog({
      action: 'csv_upload',
      resource_type: 'dataset',
      resource_id: datasetResult.data!.id,
      user_id: user.id,
      metadata: {
        filename: file.name,
        file_size: file.size,
        row_count: processedData.rowCount,
      },
    });

    return NextResponse.json({
      data: {
        dataSource: dataSourceResult.data,
        dataset: datasetResult.data,
        preview: processedData.sampleData.slice(0, 10),
      },
      message: 'CSV file uploaded and processed successfully',
    });

  } catch (error) {
    console.error('CSV upload error:', error);
    return NextResponse.json(
      { error: 'Failed to upload and process CSV file' },
      { status: 500 }
    );
  }
}
