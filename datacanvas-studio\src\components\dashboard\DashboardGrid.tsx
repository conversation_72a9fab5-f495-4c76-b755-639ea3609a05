'use client';

import { useState, useEffect, useCallback } from 'react';
import { Responsive, WidthProvider, Layout } from 'react-grid-layout';
import { ChartRenderer } from '@/components/charts/ChartRenderer';
import { ChartConfig, ChartData } from '@/lib/charts/types';
import { Dashboard, Chart, Dataset } from '@/lib/types/database';
import { Settings, Trash2, Edit, Plus } from 'lucide-react';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

const ResponsiveGridLayout = WidthProvider(Responsive);

interface DashboardGridProps {
  dashboard: Dashboard;
  charts: Chart[];
  datasets: Dataset[];
  isEditing?: boolean;
  onLayoutChange?: (layout: Layout[]) => void;
  onChartEdit?: (chart: Chart) => void;
  onChartDelete?: (chart: Chart) => void;
  onAddChart?: () => void;
}

export function DashboardGrid({
  dashboard,
  charts,
  datasets,
  isEditing = false,
  onLayoutChange,
  onChartEdit,
  onChartDelete,
  onAddChart,
}: DashboardGridProps) {
  const [layouts, setLayouts] = useState<{ [key: string]: Layout[] }>({});
  const [chartData, setChartData] = useState<{ [key: string]: ChartData }>({});
  const [loading, setLoading] = useState<{ [key: string]: boolean }>({});

  // Convert charts to grid layout items
  const layoutItems: Layout[] = charts.map(chart => ({
    i: chart.id,
    x: chart.position.x,
    y: chart.position.y,
    w: chart.position.w,
    h: chart.position.h,
    minW: 2,
    minH: 2,
  }));

  // Load data for each chart
  useEffect(() => {
    const loadChartData = async (chart: Chart) => {
      const dataset = datasets.find(d => d.id === chart.dataset_id);
      if (!dataset) return;

      setLoading(prev => ({ ...prev, [chart.id]: true }));

      try {
        const response = await fetch(`/api/datasets/${chart.dataset_id}/data?limit=1000`);
        const result = await response.json();

        if (response.ok) {
          const data: ChartData = {
            fields: dataset.schema_info?.columns?.map(col => ({
              name: col.name,
              type: col.type as any,
              nullable: col.nullable,
            })) || [],
            data: result.data || [],
          };

          setChartData(prev => ({ ...prev, [chart.id]: data }));
        }
      } catch (error) {
        console.error('Failed to load chart data:', error);
        // Use sample data as fallback
        const data: ChartData = {
          fields: dataset.schema_info?.columns?.map(col => ({
            name: col.name,
            type: col.type as any,
            nullable: col.nullable,
          })) || [],
          data: dataset.sample_data || [],
        };
        setChartData(prev => ({ ...prev, [chart.id]: data }));
      } finally {
        setLoading(prev => ({ ...prev, [chart.id]: false }));
      }
    };

    charts.forEach(loadChartData);
  }, [charts, datasets]);

  const handleLayoutChange = useCallback((layout: Layout[], layouts: { [key: string]: Layout[] }) => {
    setLayouts(layouts);
    onLayoutChange?.(layout);
  }, [onLayoutChange]);

  const renderChart = (chart: Chart) => {
    const data = chartData[chart.id];
    const isLoading = loading[chart.id];

    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-full bg-gray-50 rounded-lg">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      );
    }

    if (!data) {
      return (
        <div className="flex items-center justify-center h-full bg-gray-50 rounded-lg">
          <div className="text-gray-500 text-center">
            <div className="text-2xl mb-2">📊</div>
            <div>No data available</div>
          </div>
        </div>
      );
    }

    const chartConfig: ChartConfig = {
      id: chart.id,
      type: chart.type as any,
      name: chart.name,
      datasetId: chart.dataset_id,
      mapping: chart.config.data_mapping || {},
      style: chart.config.styling || {},
      filters: chart.config.filters || [],
      position: chart.position,
    };

    return <ChartRenderer config={chartConfig} data={data} />;
  };

  return (
    <div className="w-full">
      {charts.length === 0 && !isEditing ? (
        <div className="flex items-center justify-center h-96 bg-gray-50 rounded-lg">
          <div className="text-center">
            <div className="text-4xl mb-4">📊</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No charts yet</h3>
            <p className="text-gray-600 mb-4">
              This dashboard doesn't have any charts yet.
            </p>
            {onAddChart && (
              <button
                onClick={onAddChart}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add First Chart
              </button>
            )}
          </div>
        </div>
      ) : (
        <ResponsiveGridLayout
          className="layout"
          layouts={layouts}
          onLayoutChange={handleLayoutChange}
          breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
          cols={{ lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 }}
          rowHeight={60}
          isDraggable={isEditing}
          isResizable={isEditing}
          margin={[16, 16]}
          containerPadding={[0, 0]}
        >
          {charts.map((chart) => (
            <div
              key={chart.id}
              className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
            >
              {/* Chart Header */}
              <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50">
                <h3 className="text-sm font-medium text-gray-900 truncate">
                  {chart.name}
                </h3>
                {isEditing && (
                  <div className="flex items-center space-x-1">
                    <button
                      onClick={() => onChartEdit?.(chart)}
                      className="p-1 text-gray-400 hover:text-gray-600 rounded"
                      title="Edit chart"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => onChartDelete?.(chart)}
                      className="p-1 text-gray-400 hover:text-red-600 rounded"
                      title="Delete chart"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                )}
              </div>

              {/* Chart Content */}
              <div className="p-3 h-full">
                {renderChart(chart)}
              </div>
            </div>
          ))}
        </ResponsiveGridLayout>
      )}

      {/* Add Chart Button (when editing) */}
      {isEditing && charts.length > 0 && onAddChart && (
        <div className="mt-6 text-center">
          <button
            onClick={onAddChart}
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <Plus className="h-5 w-5 mr-2" />
            Add Chart
          </button>
        </div>
      )}
    </div>
  );
}
