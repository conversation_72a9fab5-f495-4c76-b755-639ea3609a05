{"version": 3, "file": "fast-equals.mjs", "sources": ["../src/utils.ts", "../src/comparator.ts", "../src/arrays.ts", "../src/dates.ts", "../src/maps.ts", "../src/objects.ts", "../src/regexps.ts", "../src/sets.ts", "../src/index.ts"], "sourcesContent": ["import {\n  EqualityComparator,\n  InternalEqualityComparator,\n  TypeEqualityComparator,\n} from '../index.d';\n\n/**\n * Default equality comparator pass-through, used as the standard `isEqual` creator for\n * use inside the built comparator.\n */\nexport function createDefaultIsNestedEqual<Meta>(\n  comparator: EqualityComparator<Meta>,\n): InternalEqualityComparator<Meta> {\n  return function isEqual<A, B>(\n    a: A,\n    b: B,\n    _indexOrKeyA: any,\n    _indexOrKeyB: any,\n    _parentA: any,\n    _parentB: any,\n    meta: Meta,\n  ) {\n    return comparator(a, b, meta);\n  };\n}\n\n/**\n * Wrap the provided `areItemsEqual` method to manage the circular cache, allowing\n * for circular references to be safely included in the comparison without creating\n * stack overflows.\n */\nexport function createIsCircular<\n  AreItemsEqual extends TypeEqualityComparator<any, any>,\n>(areItemsEqual: AreItemsEqual): AreItemsEqual {\n  return function isCircular(\n    a: any,\n    b: any,\n    isEqual: InternalEqualityComparator<WeakMap<any, any>>,\n    cache: WeakMap<any, any>,\n  ) {\n    if (!a || !b || typeof a !== 'object' || typeof b !== 'object') {\n      return areItemsEqual(a, b, isEqual, cache);\n    }\n\n    const cachedA = cache.get(a);\n    const cachedB = cache.get(b);\n\n    if (cachedA && cachedB) {\n      return cachedA === b && cachedB === a;\n    }\n\n    cache.set(a, b);\n    cache.set(b, a);\n\n    const result = areItemsEqual(a, b, isEqual, cache);\n\n    cache.delete(a);\n    cache.delete(b);\n\n    return result;\n  } as AreItemsEqual;\n}\n\n/**\n * Targeted shallow merge of two objects.\n *\n * @NOTE\n * This exists as a tinier compiled version of the `__assign` helper that\n * `tsc` injects in case of `Object.assign` not being present.\n */\nexport function merge<A extends object, B extends object>(a: A, b: B): A & B {\n  const merged: Record<string, any> = {};\n\n  for (const key in a) {\n    merged[key] = a[key];\n  }\n\n  for (const key in b) {\n    merged[key] = b[key];\n  }\n\n  return merged as A & B;\n}\n\n/**\n * Whether the value is a plain object.\n *\n * @NOTE\n * This is a same-realm compariosn only.\n */\nexport function isPlainObject(value: any): boolean {\n  return value.constructor === Object || value.constructor == null;\n}\n\n/**\n * When the value is `Promise`-like, aka \"then-able\".\n */\nexport function isPromiseLike(value: any): boolean {\n  return typeof value.then === 'function';\n}\n\n/**\n * Whether the values passed are strictly equal or both NaN.\n */\nexport function sameValueZeroEqual(a: any, b: any): boolean {\n  return a === b || (a !== a && b !== b);\n}\n", "import { isPlainObject, isPromiseLike, sameValueZeroEqual } from './utils';\n\nimport type {\n  CreateComparatorCreatorOptions,\n  EqualityComparator,\n} from '../index.d';\n\nconst ARGUMENTS_TAG = '[object Arguments]';\nconst BOOLEAN_TAG = '[object Boolean]';\nconst DATE_TAG = '[object Date]';\nconst REG_EXP_TAG = '[object RegExp]';\nconst MAP_TAG = '[object Map]';\nconst NUMBER_TAG = '[object Number]';\nconst OBJECT_TAG = '[object Object]';\nconst SET_TAG = '[object Set]';\nconst STRING_TAG = '[object String]';\n\nconst { toString } = Object.prototype;\n\nexport function createComparator<Meta>({\n  areArraysEqual,\n  areDatesEqual,\n  areMapsEqual,\n  areObjectsEqual,\n  areRegExpsEqual,\n  areSetsEqual,\n  createIsNestedEqual,\n}: CreateComparatorCreatorOptions<Meta>): EqualityComparator<Meta> {\n  const isEqual = createIsNestedEqual(comparator as EqualityComparator<Meta>);\n\n  /**\n   * compare the value of the two objects and return true if they are equivalent in values\n   */\n  function comparator(a: any, b: any, meta: Meta): boolean {\n    // If the items are strictly equal, no need to do a value comparison.\n    if (a === b) {\n      return true;\n    }\n\n    // If the items are not non-nullish objects, then the only possibility\n    // of them being equal but not strictly is if they are both `NaN`. Since\n    // `NaN` is uniquely not equal to itself, we can use self-comparison of\n    // both objects, which is faster than `isNaN()`.\n    if (!a || !b || typeof a !== 'object' || typeof b !== 'object') {\n      return a !== a && b !== b;\n    }\n\n    // Checks are listed in order of commonality of use-case:\n    //   1. Common complex object types (plain object, array)\n    //   2. Common data values (date, regexp)\n    //   3. Less-common complex object types (map, set)\n    //   4. Less-common data values (promise, primitive wrappers)\n    // Inherently this is both subjective and assumptive, however\n    // when reviewing comparable libraries in the wild this order\n    // appears to be generally consistent.\n\n    // `isPlainObject` only checks against the object's own realm. Cross-realm\n    // comparisons are rare, and will be handled in the ultimate fallback, so\n    // we can avoid the `toString.call()` cost unless necessary.\n    if (isPlainObject(a) && isPlainObject(b)) {\n      return areObjectsEqual(a, b, isEqual, meta);\n    }\n\n    // `isArray()` works on subclasses and is cross-realm, so we can again avoid\n    // the `toString.call()` cost unless necessary by just checking if either\n    // and then both are arrays.\n    const aArray = Array.isArray(a);\n    const bArray = Array.isArray(b);\n\n    if (aArray || bArray) {\n      return aArray === bArray && areArraysEqual(a, b, isEqual, meta);\n    }\n\n    // Since this is a custom object, use the classic `toString.call()` to get its\n    // type. This is reasonably performant in modern environments like v8 and\n    // SpiderMonkey, and allows for cross-realm comparison when other checks like\n    // `instanceof` do not.\n    const aTag = toString.call(a);\n\n    if (aTag !== toString.call(b)) {\n      return false;\n    }\n\n    if (aTag === DATE_TAG) {\n      // `getTime()` showed better results compared to alternatives like `valueOf()`\n      // or the unary `+` operator.\n      return areDatesEqual(a, b, isEqual, meta);\n    }\n\n    if (aTag === REG_EXP_TAG) {\n      return areRegExpsEqual(a, b, isEqual, meta);\n    }\n\n    if (aTag === MAP_TAG) {\n      return areMapsEqual(a, b, isEqual, meta);\n    }\n\n    if (aTag === SET_TAG) {\n      return areSetsEqual(a, b, isEqual, meta);\n    }\n\n    // If a simple object tag, then we can prioritize a simple object comparison because\n    // it is likely a custom class. If an arguments tag, it should be treated as a standard\n    // object.\n    if (aTag === OBJECT_TAG || aTag === ARGUMENTS_TAG) {\n      // The exception for value comparison is `Promise`-like contracts. These should be\n      // treated the same as standard `Promise` objects, which means strict equality.\n      return isPromiseLike(a) || isPromiseLike(b)\n        ? false\n        : areObjectsEqual(a, b, isEqual, meta);\n    }\n\n    // As the penultimate fallback, check if the values passed are primitive wrappers. This\n    // is very rare in modern JS, which is why it is deprioritized compared to all other object\n    // types.\n    if (aTag === BOOLEAN_TAG || aTag === NUMBER_TAG || aTag === STRING_TAG) {\n      return sameValueZeroEqual(a.valueOf(), b.valueOf());\n    }\n\n    // If not matching any tags that require a specific type of comparison, then we hard-code false because\n    // the only thing remaining is strict equality, which has already been compared. This is for a few reasons:\n    //   - Certain types that cannot be introspected (e.g., `WeakMap`). For these types, this is the only\n    //     comparison that can be made.\n    //   - For types that can be introspected, but rarely have requirements to be compared\n    //     (`ArrayBuffer`, `DataView`, etc.), the cost is avoided to prioritize the common\n    //     use-cases (may be included in a future release, if requested enough).\n    //   - For types that can be introspected but do not have an objective definition of what\n    //     equality is (`Error`, etc.), the subjective decision is to be conservative and strictly compare.\n    // In all cases, these decisions should be reevaluated based on changes to the language and\n    // common development practices.\n    return false;\n  }\n\n  return comparator as EqualityComparator<Meta>;\n}\n", "import { createIsCircular } from './utils';\n\nimport type { InternalEqualityComparator } from '../index.d';\n\n/**\n * Whether the arrays are equal in value.\n */\nexport function areArraysEqual(\n  a: any[],\n  b: any[],\n  isEqual: InternalEqualityComparator<any>,\n  meta: any,\n): boolean {\n  let index = a.length;\n\n  if (b.length !== index) {\n    return false;\n  }\n\n  // Decrementing `while` showed faster results than either incrementing or\n  // decrementing `for` loop and than an incrementing `while` loop. Declarative\n  // methods like `some` / `every` were not used to avoid incurring the garbage\n  // cost of anonymous callbacks.\n  while (index-- > 0) {\n    if (!isEqual(a[index], b[index], index, index, a, b, meta)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Whether the arrays are equal in value, including circular references.\n */\nexport const areArraysEqualCircular = createIsCircular(areArraysEqual);\n", "import { sameValueZeroEqual } from './utils';\n\n/**\n * Whether the dates passed are equal in value.\n *\n * @NOTE\n * This is a standalone function instead of done inline in the comparator\n * to allow for overrides.\n */\nexport function areDatesEqual(a: Date, b: Date): boolean {\n  return sameValueZeroEqual(a.valueOf(), b.valueOf());\n}\n", "import { createIsCircular } from './utils';\n\nimport type { InternalEqualityComparator } from '../index.d';\n\n/**\n * Whether the `Map`s are equal in value.\n */\nexport function areMapsEqual(\n  a: Map<any, any>,\n  b: Map<any, any>,\n  isEqual: InternalEqualityComparator<any>,\n  meta: any,\n): boolean {\n  let isValueEqual = a.size === b.size;\n\n  if (!isValueEqual) {\n    return false;\n  }\n\n  if (!a.size) {\n    return true;\n  }\n\n  // The use of `forEach()` is to avoid the transpilation cost of `for...of` comparisons, and\n  // the inability to control the performance of the resulting code. It also avoids excessive\n  // iteration compared to doing comparisons of `keys()` and `values()`. As a result, though,\n  // we cannot short-circuit the iterations; bookkeeping must be done to short-circuit the\n  // equality checks themselves.\n\n  const matchedIndices: Record<number, true> = {};\n\n  let indexA = 0;\n\n  a.forEach((aValue, aKey) => {\n    if (!isValueEqual) {\n      return;\n    }\n\n    let hasMatch = false;\n    let matchIndexB = 0;\n\n    b.forEach((bValue, bKey) => {\n      if (\n        !hasMatch &&\n        !matchedIndices[matchIndexB] &&\n        (hasMatch =\n          isEqual(aKey, bKey, indexA, matchIndexB, a, b, meta) &&\n          isEqual(aValue, bValue, aKey, bKey, a, b, meta))\n      ) {\n        matchedIndices[matchIndexB] = true;\n      }\n\n      matchIndexB++;\n    });\n\n    indexA++;\n    isValueEqual = hasMatch;\n  });\n\n  return isValueEqual;\n}\n\n/**\n * Whether the `Map`s are equal in value, including circular references.\n */\nexport const areMapsEqualCircular = createIsCircular(areMapsEqual);\n", "import { createIsCircular } from './utils';\n\nimport type { InternalEqualityComparator } from '../index.d';\n\ninterface Dictionary<Value> {\n  [key: string]: Value;\n  $$typeof?: any;\n}\n\nconst OWNER = '_owner';\nconst { hasOwnProperty } = Object.prototype;\n\n/**\n * Whether the objects are equal in value.\n */\nexport function areObjectsEqual(\n  a: Dictionary<any>,\n  b: Dictionary<any>,\n  isEqual: InternalEqualityComparator<any>,\n  meta: any,\n): boolean {\n  const keysA = Object.keys(a);\n\n  let index = keysA.length;\n\n  if (Object.keys(b).length !== index) {\n    return false;\n  }\n\n  let key: string;\n\n  // Decrementing `while` showed faster results than either incrementing or\n  // decrementing `for` loop and than an incrementing `while` loop. Declarative\n  // methods like `some` / `every` were not used to avoid incurring the garbage\n  // cost of anonymous callbacks.\n  while (index-- > 0) {\n    key = keysA[index];\n\n    if (key === OWNER) {\n      const reactElementA = !!a.$$typeof;\n      const reactElementB = !!b.$$typeof;\n\n      if ((reactElementA || reactElementB) && reactElementA !== reactElementB) {\n        return false;\n      }\n    }\n\n    if (\n      !hasOwnProperty.call(b, key) ||\n      !isEqual(a[key], b[key], key, key, a, b, meta)\n    ) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Whether the objects are equal in value, including circular references.\n */\nexport const areObjectsEqualCircular = createIsCircular(areObjectsEqual);\n", "/**\n * Whether the regexps passed are equal in value.\n *\n * @NOTE\n * This is a standalone function instead of done inline in the comparator\n * to allow for overrides. An example of this would be supporting a\n * pre-ES2015 environment where the `flags` property is not available.\n */\nexport function areRegExpsEqual(a: RegExp, b: RegExp): boolean {\n  return a.source === b.source && a.flags === b.flags;\n}\n", "import { createIsCircular } from './utils';\n\nimport type { InternalEqualityComparator } from '../index.d';\n\n/**\n * Whether the `Set`s are equal in value.\n */\nexport function areSetsEqual(\n  a: Set<any>,\n  b: Set<any>,\n  isEqual: InternalEqualityComparator<any>,\n  meta: any,\n): boolean {\n  let isValueEqual = a.size === b.size;\n\n  if (!isValueEqual) {\n    return false;\n  }\n\n  if (!a.size) {\n    return true;\n  }\n\n  // The use of `forEach()` is to avoid the transpilation cost of `for...of` comparisons, and\n  // the inability to control the performance of the resulting code. It also avoids excessive\n  // iteration compared to doing comparisons of `keys()` and `values()`. As a result, though,\n  // we cannot short-circuit the iterations; bookkeeping must be done to short-circuit the\n  // equality checks themselves.\n\n  const matchedIndices: Record<number, true> = {};\n\n  a.forEach((aValue, aKey) => {\n    if (!isValueEqual) {\n      return;\n    }\n\n    let hasMatch = false;\n    let matchIndex = 0;\n\n    b.forEach((bValue, bKey) => {\n      if (\n        !hasMatch &&\n        !matchedIndices[matchIndex] &&\n        (hasMatch = isEqual(aValue, bValue, aKey, bKey, a, b, meta))\n      ) {\n        matchedIndices[matchIndex] = true;\n      }\n\n      matchIndex++;\n    });\n\n    isValueEqual = hasMatch;\n  });\n\n  return isValueEqual;\n}\n\n/**\n * Whether the `Set`s are equal in value, including circular references.\n */\nexport const areSetsEqualCircular = createIsCircular(areSetsEqual);\n", "import { createComparator } from './comparator';\nimport { areArraysEqual, areArraysEqualCircular } from './arrays';\nimport { areDatesEqual } from './dates';\nimport { areMapsEqual, areMapsEqualCircular } from './maps';\nimport { areObjectsEqual, areObjectsEqualCircular } from './objects';\nimport { areRegExpsEqual } from './regexps';\nimport { areSetsEqual, areSetsEqualCircular } from './sets';\nimport { createDefaultIsNestedEqual, merge, sameValueZeroEqual } from './utils';\n\nimport type {\n  BaseCircularMeta,\n  CreateComparatorCreatorOptions,\n  EqualityComparator,\n  GetComparatorOptions,\n} from '../index.d';\n\nexport { sameValueZeroEqual };\n\nconst DEFAULT_CONFIG: CreateComparatorCreatorOptions<undefined> = Object.freeze(\n  {\n    areArraysEqual,\n    areDatesEqual,\n    areMapsEqual,\n    areObjectsEqual,\n    areRegExpsEqual,\n    areSetsEqual,\n    createIsNestedEqual: createDefaultIsNestedEqual,\n  },\n);\nconst DEFAULT_CIRCULAR_CONFIG: CreateComparatorCreatorOptions<BaseCircularMeta> =\n  Object.freeze({\n    areArraysEqual: areArraysEqualCircular,\n    areDatesEqual,\n    areMapsEqual: areMapsEqualCircular,\n    areObjectsEqual: areObjectsEqualCircular,\n    areRegExpsEqual,\n    areSetsEqual: areSetsEqualCircular,\n    createIsNestedEqual: createDefaultIsNestedEqual,\n  });\n\nconst isDeepEqual = createComparator(DEFAULT_CONFIG);\n\n/**\n * Whether the items passed are deeply-equal in value.\n */\nexport function deepEqual<A, B>(a: A, b: B): boolean {\n  return isDeepEqual(a, b, undefined);\n}\n\nconst isShallowEqual = createComparator(\n  merge(DEFAULT_CONFIG, { createIsNestedEqual: () => sameValueZeroEqual }),\n);\n\n/**\n * Whether the items passed are shallowly-equal in value.\n */\nexport function shallowEqual<A, B>(a: A, b: B): boolean {\n  return isShallowEqual(a, b, undefined);\n}\n\nconst isCircularDeepEqual = createComparator(DEFAULT_CIRCULAR_CONFIG);\n\n/**\n * Whether the items passed are deeply-equal in value, including circular references.\n */\nexport function circularDeepEqual<A, B>(a: A, b: B): boolean {\n  return isCircularDeepEqual(a, b, new WeakMap());\n}\n\nconst isCircularShallowEqual = createComparator(\n  merge(DEFAULT_CIRCULAR_CONFIG, {\n    createIsNestedEqual: () => sameValueZeroEqual,\n  }),\n);\n\n/**\n * Whether the items passed are shallowly-equal in value, including circular references.\n */\nexport function circularShallowEqual<A, B>(a: A, b: B): boolean {\n  return isCircularShallowEqual(a, b, new WeakMap());\n}\n\n/**\n * Create a custom equality comparison method.\n *\n * This can be done to create very targeted comparisons in extreme hot-path scenarios\n * where the standard methods are not performant enough, but can also be used to provide\n * support for legacy environments that do not support expected features like\n * `RegExp.prototype.flags` out of the box.\n */\nexport function createCustomEqual<Meta = undefined>(\n  getComparatorOptions: GetComparatorOptions<Meta>,\n): EqualityComparator<Meta> {\n  return createComparator<Meta>(\n    merge(DEFAULT_CONFIG, getComparatorOptions(DEFAULT_CONFIG as any)),\n  );\n}\n\n/**\n * Create a custom equality comparison method that handles circular references. This is very\n * similar to `createCustomEqual`, with the only difference being that `meta` expects to be\n * populated with a `WeakMap`-like contract.\n *\n * This can be done to create very targeted comparisons in extreme hot-path scenarios\n * where the standard methods are not performant enough, but can also be used to provide\n * support for legacy environments that do not support expected features like\n * `WeakMap` out of the box.\n */\nexport function createCustomCircularEqual<\n  Meta extends BaseCircularMeta = WeakMap<any, any>,\n>(getComparatorOptions: GetComparatorOptions<Meta>): EqualityComparator<Meta> {\n  const comparator = createComparator<Meta>(\n    merge(\n      DEFAULT_CIRCULAR_CONFIG,\n      getComparatorOptions(DEFAULT_CIRCULAR_CONFIG as any),\n    ),\n  );\n\n  return ((a: any, b: any, meta: any = new WeakMap()) =>\n    comparator(a, b, meta)) as EqualityComparator<Meta>;\n}\n"], "names": [], "mappings": "AAMA;;;AAGG;AACG,SAAU,0BAA0B,CACxC,UAAoC,EAAA;AAEpC,IAAA,OAAO,SAAS,OAAO,CACrB,CAAI,EACJ,CAAI,EACJ,YAAiB,EACjB,YAAiB,EACjB,QAAa,EACb,QAAa,EACb,IAAU,EAAA;QAEV,OAAO,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AAChC,KAAC,CAAC;AACJ,CAAC;AAED;;;;AAIG;AACG,SAAU,gBAAgB,CAE9B,aAA4B,EAAA;IAC5B,OAAO,SAAS,UAAU,CACxB,CAAM,EACN,CAAM,EACN,OAAsD,EACtD,KAAwB,EAAA;AAExB,QAAA,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;YAC9D,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAC5C,SAAA;QAED,IAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC7B,IAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAE7B,IAAI,OAAO,IAAI,OAAO,EAAE;AACtB,YAAA,OAAO,OAAO,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,CAAC;AACvC,SAAA;AAED,QAAA,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAChB,QAAA,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAEhB,QAAA,IAAM,MAAM,GAAG,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAEnD,QAAA,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAChB,QAAA,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAEhB,QAAA,OAAO,MAAM,CAAC;AAChB,KAAkB,CAAC;AACrB,CAAC;AAED;;;;;;AAMG;AACa,SAAA,KAAK,CAAqC,CAAI,EAAE,CAAI,EAAA;IAClE,IAAM,MAAM,GAAwB,EAAE,CAAC;AAEvC,IAAA,KAAK,IAAM,GAAG,IAAI,CAAC,EAAE;QACnB,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACtB,KAAA;AAED,IAAA,KAAK,IAAM,GAAG,IAAI,CAAC,EAAE;QACnB,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACtB,KAAA;AAED,IAAA,OAAO,MAAe,CAAC;AACzB,CAAC;AAED;;;;;AAKG;AACG,SAAU,aAAa,CAAC,KAAU,EAAA;IACtC,OAAO,KAAK,CAAC,WAAW,KAAK,MAAM,IAAI,KAAK,CAAC,WAAW,IAAI,IAAI,CAAC;AACnE,CAAC;AAED;;AAEG;AACG,SAAU,aAAa,CAAC,KAAU,EAAA;AACtC,IAAA,OAAO,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC;AAC1C,CAAC;AAED;;AAEG;AACa,SAAA,kBAAkB,CAAC,CAAM,EAAE,CAAM,EAAA;AAC/C,IAAA,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACzC;;ACnGA,IAAM,aAAa,GAAG,oBAAoB,CAAC;AAC3C,IAAM,WAAW,GAAG,kBAAkB,CAAC;AACvC,IAAM,QAAQ,GAAG,eAAe,CAAC;AACjC,IAAM,WAAW,GAAG,iBAAiB,CAAC;AACtC,IAAM,OAAO,GAAG,cAAc,CAAC;AAC/B,IAAM,UAAU,GAAG,iBAAiB,CAAC;AACrC,IAAM,UAAU,GAAG,iBAAiB,CAAC;AACrC,IAAM,OAAO,GAAG,cAAc,CAAC;AAC/B,IAAM,UAAU,GAAG,iBAAiB,CAAC;AAE7B,IAAA,QAAQ,GAAK,MAAM,CAAC,SAAS,SAArB,CAAsB;AAEhC,SAAU,gBAAgB,CAAO,EAQA,EAAA;AAPrC,IAAA,IAAA,cAAc,oBAAA,EACd,aAAa,mBAAA,EACb,YAAY,kBAAA,EACZ,eAAe,GAAA,EAAA,CAAA,eAAA,EACf,eAAe,GAAA,EAAA,CAAA,eAAA,EACf,YAAY,GAAA,EAAA,CAAA,YAAA,EACZ,mBAAmB,GAAA,EAAA,CAAA,mBAAA,CAAA;AAEnB,IAAA,IAAM,OAAO,GAAG,mBAAmB,CAAC,UAAsC,CAAC,CAAC;AAE5E;;AAEG;AACH,IAAA,SAAS,UAAU,CAAC,CAAM,EAAE,CAAM,EAAE,IAAU,EAAA;;QAE5C,IAAI,CAAC,KAAK,CAAC,EAAE;AACX,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;;;;;AAMD,QAAA,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;AAC9D,YAAA,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC3B,SAAA;;;;;;;;;;;;QAcD,IAAI,aAAa,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC,EAAE;YACxC,OAAO,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AAC7C,SAAA;;;;QAKD,IAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChC,IAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,MAAM,IAAI,MAAM,EAAE;AACpB,YAAA,OAAO,MAAM,KAAK,MAAM,IAAI,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AACjE,SAAA;;;;;QAMD,IAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAE9B,IAAI,IAAI,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;AAC7B,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;QAED,IAAI,IAAI,KAAK,QAAQ,EAAE;;;YAGrB,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AAC3C,SAAA;QAED,IAAI,IAAI,KAAK,WAAW,EAAE;YACxB,OAAO,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AAC7C,SAAA;QAED,IAAI,IAAI,KAAK,OAAO,EAAE;YACpB,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AAC1C,SAAA;QAED,IAAI,IAAI,KAAK,OAAO,EAAE;YACpB,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AAC1C,SAAA;;;;AAKD,QAAA,IAAI,IAAI,KAAK,UAAU,IAAI,IAAI,KAAK,aAAa,EAAE;;;YAGjD,OAAO,aAAa,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC;AACzC,kBAAE,KAAK;kBACL,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AAC1C,SAAA;;;;QAKD,IAAI,IAAI,KAAK,WAAW,IAAI,IAAI,KAAK,UAAU,IAAI,IAAI,KAAK,UAAU,EAAE;AACtE,YAAA,OAAO,kBAAkB,CAAC,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;AACrD,SAAA;;;;;;;;;;;;AAaD,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,OAAO,UAAsC,CAAC;AAChD;;AClIA;;AAEG;AACG,SAAU,cAAc,CAC5B,CAAQ,EACR,CAAQ,EACR,OAAwC,EACxC,IAAS,EAAA;AAET,IAAA,IAAI,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC;AAErB,IAAA,IAAI,CAAC,CAAC,MAAM,KAAK,KAAK,EAAE;AACtB,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;;;;;AAMD,IAAA,OAAO,KAAK,EAAE,GAAG,CAAC,EAAE;QAClB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE;AAC1D,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AACF,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;AAEG;AACI,IAAM,sBAAsB,GAAG,gBAAgB,CAAC,cAAc,CAAC;;ACjCtE;;;;;;AAMG;AACa,SAAA,aAAa,CAAC,CAAO,EAAE,CAAO,EAAA;AAC5C,IAAA,OAAO,kBAAkB,CAAC,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;AACtD;;ACPA;;AAEG;AACG,SAAU,YAAY,CAC1B,CAAgB,EAChB,CAAgB,EAChB,OAAwC,EACxC,IAAS,EAAA;IAET,IAAI,YAAY,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;IAErC,IAAI,CAAC,YAAY,EAAE;AACjB,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AAED,IAAA,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;AACX,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;;;;;;IAQD,IAAM,cAAc,GAAyB,EAAE,CAAC;IAEhD,IAAI,MAAM,GAAG,CAAC,CAAC;AAEf,IAAA,CAAC,CAAC,OAAO,CAAC,UAAC,MAAM,EAAE,IAAI,EAAA;QACrB,IAAI,CAAC,YAAY,EAAE;YACjB,OAAO;AACR,SAAA;QAED,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,IAAI,WAAW,GAAG,CAAC,CAAC;AAEpB,QAAA,CAAC,CAAC,OAAO,CAAC,UAAC,MAAM,EAAE,IAAI,EAAA;AACrB,YAAA,IACE,CAAC,QAAQ;gBACT,CAAC,cAAc,CAAC,WAAW,CAAC;AAC5B,iBAAC,QAAQ;AACP,oBAAA,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;AACpD,wBAAA,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAClD;AACA,gBAAA,cAAc,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;AACpC,aAAA;AAED,YAAA,WAAW,EAAE,CAAC;AAChB,SAAC,CAAC,CAAC;AAEH,QAAA,MAAM,EAAE,CAAC;QACT,YAAY,GAAG,QAAQ,CAAC;AAC1B,KAAC,CAAC,CAAC;AAEH,IAAA,OAAO,YAAY,CAAC;AACtB,CAAC;AAED;;AAEG;AACI,IAAM,oBAAoB,GAAG,gBAAgB,CAAC,YAAY,CAAC;;ACxDlE,IAAM,KAAK,GAAG,QAAQ,CAAC;AACf,IAAA,cAAc,GAAK,MAAM,CAAC,SAAS,eAArB,CAAsB;AAE5C;;AAEG;AACG,SAAU,eAAe,CAC7B,CAAkB,EAClB,CAAkB,EAClB,OAAwC,EACxC,IAAS,EAAA;IAET,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAE7B,IAAA,IAAI,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC;IAEzB,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,KAAK,EAAE;AACnC,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AAED,IAAA,IAAI,GAAW,CAAC;;;;;AAMhB,IAAA,OAAO,KAAK,EAAE,GAAG,CAAC,EAAE;AAClB,QAAA,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;QAEnB,IAAI,GAAG,KAAK,KAAK,EAAE;AACjB,YAAA,IAAM,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;AACnC,YAAA,IAAM,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YAEnC,IAAI,CAAC,aAAa,IAAI,aAAa,KAAK,aAAa,KAAK,aAAa,EAAE;AACvE,gBAAA,OAAO,KAAK,CAAC;AACd,aAAA;AACF,SAAA;QAED,IACE,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC;YAC5B,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAC9C;AACA,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AACF,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;AAEG;AACI,IAAM,uBAAuB,GAAG,gBAAgB,CAAC,eAAe,CAAC;;AC7DxE;;;;;;;AAOG;AACa,SAAA,eAAe,CAAC,CAAS,EAAE,CAAS,EAAA;AAClD,IAAA,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK,CAAC;AACtD;;ACNA;;AAEG;AACG,SAAU,YAAY,CAC1B,CAAW,EACX,CAAW,EACX,OAAwC,EACxC,IAAS,EAAA;IAET,IAAI,YAAY,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;IAErC,IAAI,CAAC,YAAY,EAAE;AACjB,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AAED,IAAA,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;AACX,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;;;;;;IAQD,IAAM,cAAc,GAAyB,EAAE,CAAC;AAEhD,IAAA,CAAC,CAAC,OAAO,CAAC,UAAC,MAAM,EAAE,IAAI,EAAA;QACrB,IAAI,CAAC,YAAY,EAAE;YACjB,OAAO;AACR,SAAA;QAED,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,IAAI,UAAU,GAAG,CAAC,CAAC;AAEnB,QAAA,CAAC,CAAC,OAAO,CAAC,UAAC,MAAM,EAAE,IAAI,EAAA;AACrB,YAAA,IACE,CAAC,QAAQ;gBACT,CAAC,cAAc,CAAC,UAAU,CAAC;AAC3B,iBAAC,QAAQ,GAAG,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAC5D;AACA,gBAAA,cAAc,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;AACnC,aAAA;AAED,YAAA,UAAU,EAAE,CAAC;AACf,SAAC,CAAC,CAAC;QAEH,YAAY,GAAG,QAAQ,CAAC;AAC1B,KAAC,CAAC,CAAC;AAEH,IAAA,OAAO,YAAY,CAAC;AACtB,CAAC;AAED;;AAEG;AACI,IAAM,oBAAoB,GAAG,gBAAgB,CAAC,YAAY,CAAC;;AC1ClE,IAAM,cAAc,GAA8C,MAAM,CAAC,MAAM,CAC7E;AACE,IAAA,cAAc,EAAA,cAAA;AACd,IAAA,aAAa,EAAA,aAAA;AACb,IAAA,YAAY,EAAA,YAAA;AACZ,IAAA,eAAe,EAAA,eAAA;AACf,IAAA,eAAe,EAAA,eAAA;AACf,IAAA,YAAY,EAAA,YAAA;AACZ,IAAA,mBAAmB,EAAE,0BAA0B;AAChD,CAAA,CACF,CAAC;AACF,IAAM,uBAAuB,GAC3B,MAAM,CAAC,MAAM,CAAC;AACZ,IAAA,cAAc,EAAE,sBAAsB;AACtC,IAAA,aAAa,EAAA,aAAA;AACb,IAAA,YAAY,EAAE,oBAAoB;AAClC,IAAA,eAAe,EAAE,uBAAuB;AACxC,IAAA,eAAe,EAAA,eAAA;AACf,IAAA,YAAY,EAAE,oBAAoB;AAClC,IAAA,mBAAmB,EAAE,0BAA0B;AAChD,CAAA,CAAC,CAAC;AAEL,IAAM,WAAW,GAAG,gBAAgB,CAAC,cAAc,CAAC,CAAC;AAErD;;AAEG;AACa,SAAA,SAAS,CAAO,CAAI,EAAE,CAAI,EAAA;IACxC,OAAO,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;AACtC,CAAC;AAED,IAAM,cAAc,GAAG,gBAAgB,CACrC,KAAK,CAAC,cAAc,EAAE,EAAE,mBAAmB,EAAE,YAAA,EAAM,OAAA,kBAAkB,CAAA,EAAA,EAAE,CAAC,CACzE,CAAC;AAEF;;AAEG;AACa,SAAA,YAAY,CAAO,CAAI,EAAE,CAAI,EAAA;IAC3C,OAAO,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;AACzC,CAAC;AAED,IAAM,mBAAmB,GAAG,gBAAgB,CAAC,uBAAuB,CAAC,CAAC;AAEtE;;AAEG;AACa,SAAA,iBAAiB,CAAO,CAAI,EAAE,CAAI,EAAA;IAChD,OAAO,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,OAAO,EAAE,CAAC,CAAC;AAClD,CAAC;AAED,IAAM,sBAAsB,GAAG,gBAAgB,CAC7C,KAAK,CAAC,uBAAuB,EAAE;AAC7B,IAAA,mBAAmB,EAAE,YAAA,EAAM,OAAA,kBAAkB,GAAA;AAC9C,CAAA,CAAC,CACH,CAAC;AAEF;;AAEG;AACa,SAAA,oBAAoB,CAAO,CAAI,EAAE,CAAI,EAAA;IACnD,OAAO,sBAAsB,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,OAAO,EAAE,CAAC,CAAC;AACrD,CAAC;AAED;;;;;;;AAOG;AACG,SAAU,iBAAiB,CAC/B,oBAAgD,EAAA;AAEhD,IAAA,OAAO,gBAAgB,CACrB,KAAK,CAAC,cAAc,EAAE,oBAAoB,CAAC,cAAqB,CAAC,CAAC,CACnE,CAAC;AACJ,CAAC;AAED;;;;;;;;;AASG;AACG,SAAU,yBAAyB,CAEvC,oBAAgD,EAAA;AAChD,IAAA,IAAM,UAAU,GAAG,gBAAgB,CACjC,KAAK,CACH,uBAAuB,EACvB,oBAAoB,CAAC,uBAA8B,CAAC,CACrD,CACF,CAAC;AAEF,IAAA,QAAQ,UAAC,CAAM,EAAE,CAAM,EAAE,IAAyB,EAAA;QAAzB,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,EAAA,IAAgB,GAAA,IAAA,OAAO,EAAE,CAAA,EAAA;AAChD,QAAA,OAAA,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAA;AAAtB,KAAsB,EAA8B;AACxD;;;;"}