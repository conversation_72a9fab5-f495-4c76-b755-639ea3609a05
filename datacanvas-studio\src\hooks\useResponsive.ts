'use client';

import { useState, useEffect } from 'react';

interface BreakpointConfig {
  sm: number;
  md: number;
  lg: number;
  xl: number;
  '2xl': number;
}

const defaultBreakpoints: BreakpointConfig = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
};

export function useResponsive(breakpoints: Partial<BreakpointConfig> = {}) {
  const bp = { ...defaultBreakpoints, ...breakpoints };
  
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1024,
    height: typeof window !== 'undefined' ? window.innerHeight : 768,
  });

  useEffect(() => {
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    }

    window.addEventListener('resize', handleResize);
    handleResize(); // Set initial size

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const isMobile = windowSize.width < bp.md;
  const isTablet = windowSize.width >= bp.md && windowSize.width < bp.lg;
  const isDesktop = windowSize.width >= bp.lg;
  const isLargeDesktop = windowSize.width >= bp.xl;

  return {
    windowSize,
    isMobile,
    isTablet,
    isDesktop,
    isLargeDesktop,
    breakpoints: bp,
    // Specific breakpoint checks
    isSm: windowSize.width >= bp.sm,
    isMd: windowSize.width >= bp.md,
    isLg: windowSize.width >= bp.lg,
    isXl: windowSize.width >= bp.xl,
    is2Xl: windowSize.width >= bp['2xl'],
    // Utility functions
    isBelow: (breakpoint: keyof BreakpointConfig) => windowSize.width < bp[breakpoint],
    isAbove: (breakpoint: keyof BreakpointConfig) => windowSize.width >= bp[breakpoint],
    isBetween: (min: keyof BreakpointConfig, max: keyof BreakpointConfig) => 
      windowSize.width >= bp[min] && windowSize.width < bp[max],
  };
}

export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const media = window.matchMedia(query);
    setMatches(media.matches);

    const listener = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };

    media.addEventListener('change', listener);
    return () => media.removeEventListener('change', listener);
  }, [query]);

  return matches;
}

// Hook for detecting touch devices
export function useTouchDevice(): boolean {
  const [isTouch, setIsTouch] = useState(false);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const checkTouch = () => {
      setIsTouch(
        'ontouchstart' in window ||
        navigator.maxTouchPoints > 0 ||
        // @ts-ignore
        navigator.msMaxTouchPoints > 0
      );
    };

    checkTouch();
    window.addEventListener('touchstart', checkTouch, { once: true });

    return () => {
      window.removeEventListener('touchstart', checkTouch);
    };
  }, []);

  return isTouch;
}

// Hook for detecting device orientation
export function useOrientation() {
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('portrait');

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const updateOrientation = () => {
      setOrientation(window.innerHeight > window.innerWidth ? 'portrait' : 'landscape');
    };

    updateOrientation();
    window.addEventListener('resize', updateOrientation);
    window.addEventListener('orientationchange', updateOrientation);

    return () => {
      window.removeEventListener('resize', updateOrientation);
      window.removeEventListener('orientationchange', updateOrientation);
    };
  }, []);

  return orientation;
}

// Hook for safe area insets (iOS notch handling)
export function useSafeArea() {
  const [safeArea, setSafeArea] = useState({
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const updateSafeArea = () => {
      const computedStyle = getComputedStyle(document.documentElement);
      
      setSafeArea({
        top: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-top)') || '0'),
        right: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-right)') || '0'),
        bottom: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-bottom)') || '0'),
        left: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-left)') || '0'),
      });
    };

    updateSafeArea();
    window.addEventListener('resize', updateSafeArea);
    window.addEventListener('orientationchange', updateSafeArea);

    return () => {
      window.removeEventListener('resize', updateSafeArea);
      window.removeEventListener('orientationchange', updateSafeArea);
    };
  }, []);

  return safeArea;
}

// Hook for viewport height handling (mobile browser address bar)
export function useViewportHeight() {
  const [viewportHeight, setViewportHeight] = useState(
    typeof window !== 'undefined' ? window.innerHeight : 768
  );

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const updateHeight = () => {
      // Use visual viewport API if available (better for mobile)
      if (window.visualViewport) {
        setViewportHeight(window.visualViewport.height);
      } else {
        setViewportHeight(window.innerHeight);
      }
    };

    updateHeight();

    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', updateHeight);
      return () => window.visualViewport?.removeEventListener('resize', updateHeight);
    } else {
      window.addEventListener('resize', updateHeight);
      return () => window.removeEventListener('resize', updateHeight);
    }
  }, []);

  return viewportHeight;
}

// Utility function to get responsive grid columns
export function getResponsiveColumns(
  windowWidth: number,
  options: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
    largeDesktop?: number;
  } = {}
): number {
  const { mobile = 1, tablet = 2, desktop = 3, largeDesktop = 4 } = options;

  if (windowWidth < 768) return mobile;
  if (windowWidth < 1024) return tablet;
  if (windowWidth < 1280) return desktop;
  return largeDesktop;
}

// Utility function to get responsive spacing
export function getResponsiveSpacing(
  windowWidth: number,
  options: {
    mobile?: string;
    tablet?: string;
    desktop?: string;
  } = {}
): string {
  const { mobile = '1rem', tablet = '1.5rem', desktop = '2rem' } = options;

  if (windowWidth < 768) return mobile;
  if (windowWidth < 1024) return tablet;
  return desktop;
}

// Hook for responsive chart dimensions
export function useResponsiveChartSize(containerRef: React.RefObject<HTMLElement>) {
  const [dimensions, setDimensions] = useState({ width: 400, height: 300 });
  const { isMobile, isTablet } = useResponsive();

  useEffect(() => {
    if (!containerRef.current) return;

    const updateDimensions = () => {
      const container = containerRef.current;
      if (!container) return;

      const rect = container.getBoundingClientRect();
      const width = rect.width;
      
      // Responsive height calculation
      let height: number;
      if (isMobile) {
        height = Math.min(width * 0.75, 250); // 4:3 aspect ratio, max 250px
      } else if (isTablet) {
        height = Math.min(width * 0.6, 350); // 5:3 aspect ratio, max 350px
      } else {
        height = Math.min(width * 0.5, 400); // 2:1 aspect ratio, max 400px
      }

      setDimensions({ width, height });
    };

    updateDimensions();

    const resizeObserver = new ResizeObserver(updateDimensions);
    resizeObserver.observe(containerRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [containerRef, isMobile, isTablet]);

  return dimensions;
}
