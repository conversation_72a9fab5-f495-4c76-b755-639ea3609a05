{"author": "<EMAIL>", "browser": "dist/fast-equals.js", "bugs": {"url": "https://github.com/planttheidea/fast-equals/issues"}, "description": "A blazing fast equality comparison, either shallow or deep", "devDependencies": {"@rollup/plugin-node-resolve": "^13.1.3", "@types/jest": "^28.1.8", "@types/lodash": "^4.14.184", "@types/node": "^18.7.13", "@types/ramda": "^0.28.15", "@types/react": "^18.0.17", "@typescript-eslint/eslint-plugin": "^5.35.1", "@typescript-eslint/parser": "^5.35.1", "benchee": "^1.1.0", "cli-table3": "^0.6.1", "decircularize": "^1.0.0", "deep-eql": "^4.1.0", "deep-equal": "^2.0.5", "eslint": "^8.22.0", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.25.4", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-react": "^7.31.0", "eslint-webpack-plugin": "^3.2.0", "fast-deep-equal": "^3.1.3", "fs-extra": "^10.0.0", "html-webpack-plugin": "^5.5.0", "in-publish": "^2.0.0", "jest": "^28.1.3", "lodash": "^4.17.21", "nano-equal": "^2.0.2", "prettier": "^2.7.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-fast-compare": "^3.2.0", "release-it": "^15.4.0", "rollup": "^2.78.1", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.33.0", "shallow-equal-fuzzy": "^0.0.2", "ts-jest": "^28.0.8", "ts-loader": "^9.3.1", "typescript": "^4.7.4", "underscore": "^1.13.4", "webpack": "^5.74.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.10.0"}, "homepage": "https://github.com/planttheidea/fast-equals#readme", "keywords": ["fast", "equal", "equals", "deep-equal", "equivalent"], "license": "MIT", "main": "dist/fast-equals.cjs.js", "module": "dist/fast-equals.esm.js", "name": "fast-equals", "repository": {"type": "git", "url": "git+https://github.com/planttheidea/fast-equals.git"}, "scripts": {"benchmark": "npm run dist && node benchmark/index.js", "build": "NODE_ENV=production rollup -c", "dev": "NODE_ENV=development webpack serve --progress --config=webpack/webpack.config.js", "dist": "rimraf dist && npm run build", "format": "prettier **/*.ts --write", "lint": "eslint src/*.ts", "lint:fix": "npm run lint -- --fix", "start": "npm run dev", "prepublish": "if in-publish; then npm run prepublish:compile; fi", "prepublish:compile": "npm run typecheck && npm run lint && npm run test && npm run dist", "release": "release-it", "release:beta": "release-it --config=.release-it.beta.json", "test": "NODE_PATH=. jest", "test:coverage": "npm test -- --coverage", "test:watch": "npm test -- --watch", "typecheck": "tsc --noEmit"}, "sideEffects": false, "types": "index.d.ts", "version": "4.0.3"}