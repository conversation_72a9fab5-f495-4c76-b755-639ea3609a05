'use client';

import { useState, useEffect } from 'react';
import { DndContext, DragEndEvent, DragOverlay, DragStartEvent } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { ChartConfig, ChartData, ChartField, ChartMapping, ChartType, CHART_TYPES, getChartTypeDefinition, isValidMapping, generateDefaultStyle, generateDefaultPosition } from '@/lib/charts/types';
import { ChartRenderer } from './ChartRenderer';
import { Dataset } from '@/lib/types/database';

interface ChartBuilderProps {
  dataset: Dataset;
  onSave?: (config: ChartConfig) => void;
  onCancel?: () => void;
  initialConfig?: Partial<ChartConfig>;
}

interface DraggableFieldProps {
  field: ChartField;
  id: string;
}

function DraggableField({ field, id }: DraggableFieldProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const getFieldIcon = (type: string) => {
    switch (type) {
      case 'string': return '📝';
      case 'number': return '🔢';
      case 'date': return '📅';
      case 'boolean': return '✅';
      default: return '📄';
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className="flex items-center space-x-2 p-2 bg-white border border-gray-200 rounded-md cursor-grab hover:bg-gray-50 active:cursor-grabbing"
    >
      <span className="text-lg">{getFieldIcon(field.type)}</span>
      <div className="flex-1 min-w-0">
        <div className="text-sm font-medium text-gray-900 truncate">{field.name}</div>
        <div className="text-xs text-gray-500">{field.type}</div>
      </div>
    </div>
  );
}

interface DropZoneProps {
  label: string;
  mappingKey: keyof ChartMapping;
  value?: string;
  onDrop: (fieldName: string, mappingKey: keyof ChartMapping) => void;
  onRemove: (mappingKey: keyof ChartMapping) => void;
  isRequired?: boolean;
  supportedTypes?: ChartField['type'][];
}

function DropZone({ label, mappingKey, value, onDrop, onRemove, isRequired, supportedTypes }: DropZoneProps) {
  const [isOver, setIsOver] = useState(false);

  return (
    <div
      className={`
        min-h-[60px] p-3 border-2 border-dashed rounded-md transition-colors
        ${isOver ? 'border-blue-400 bg-blue-50' : 'border-gray-300'}
        ${value ? 'bg-blue-100 border-blue-300' : ''}
      `}
      onDragOver={(e) => {
        e.preventDefault();
        setIsOver(true);
      }}
      onDragLeave={() => setIsOver(false)}
      onDrop={(e) => {
        e.preventDefault();
        setIsOver(false);
        const fieldName = e.dataTransfer.getData('text/plain');
        if (fieldName) {
          onDrop(fieldName, mappingKey);
        }
      }}
    >
      <div className="flex items-center justify-between">
        <div>
          <div className="text-sm font-medium text-gray-700">
            {label} {isRequired && <span className="text-red-500">*</span>}
          </div>
          {supportedTypes && (
            <div className="text-xs text-gray-500 mt-1">
              Supports: {supportedTypes.join(', ')}
            </div>
          )}
        </div>
        {value && (
          <button
            onClick={() => onRemove(mappingKey)}
            className="text-red-500 hover:text-red-700 text-sm"
          >
            ✕
          </button>
        )}
      </div>
      {value && (
        <div className="mt-2 px-2 py-1 bg-white rounded border text-sm font-medium">
          {value}
        </div>
      )}
    </div>
  );
}

export function ChartBuilder({ dataset, onSave, onCancel, initialConfig }: ChartBuilderProps) {
  const [selectedChartType, setSelectedChartType] = useState<ChartType>(initialConfig?.type || 'bar');
  const [mapping, setMapping] = useState<ChartMapping>(initialConfig?.mapping || {});
  const [chartName, setChartName] = useState(initialConfig?.name || 'New Chart');
  const [activeId, setActiveId] = useState<string | null>(null);

  const fields: ChartField[] = dataset.schema_info?.columns?.map(col => ({
    name: col.name,
    type: col.type as ChartField['type'],
    nullable: col.nullable,
  })) || [];

  const chartData: ChartData = {
    fields,
    data: dataset.sample_data || [],
  };

  const chartDefinition = getChartTypeDefinition(selectedChartType);
  const validation = isValidMapping(selectedChartType, mapping, fields);

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    setActiveId(null);
  };

  const handleFieldDrop = (fieldName: string, mappingKey: keyof ChartMapping) => {
    setMapping(prev => ({
      ...prev,
      [mappingKey]: fieldName,
    }));
  };

  const handleMappingRemove = (mappingKey: keyof ChartMapping) => {
    setMapping(prev => {
      const newMapping = { ...prev };
      delete newMapping[mappingKey];
      return newMapping;
    });
  };

  const handleSave = () => {
    if (!validation.valid) return;

    const config: ChartConfig = {
      id: initialConfig?.id || `chart_${Date.now()}`,
      type: selectedChartType,
      name: chartName,
      datasetId: dataset.id,
      mapping,
      style: initialConfig?.style || generateDefaultStyle(selectedChartType),
      filters: initialConfig?.filters || [],
      position: initialConfig?.position || generateDefaultPosition(),
    };

    onSave?.(config);
  };

  return (
    <div className="flex h-full">
      <DndContext onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
        {/* Left Panel - Configuration */}
        <div className="w-80 bg-gray-50 border-r border-gray-200 p-4 overflow-y-auto">
          <div className="space-y-6">
            {/* Chart Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Chart Name
              </label>
              <input
                type="text"
                value={chartName}
                onChange={(e) => setChartName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Chart Type Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Chart Type
              </label>
              <div className="grid grid-cols-2 gap-2">
                {CHART_TYPES.map((type) => (
                  <button
                    key={type.type}
                    onClick={() => {
                      setSelectedChartType(type.type);
                      setMapping({}); // Reset mapping when chart type changes
                    }}
                    className={`
                      p-3 text-left border rounded-md transition-colors
                      ${selectedChartType === type.type
                        ? 'border-blue-500 bg-blue-50 text-blue-900'
                        : 'border-gray-200 hover:border-gray-300'
                      }
                    `}
                  >
                    <div className="text-lg mb-1">{type.icon}</div>
                    <div className="text-sm font-medium">{type.name}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* Field Mappings */}
            {chartDefinition && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Field Mappings
                </label>
                <div className="space-y-3">
                  {[...chartDefinition.requiredMappings, ...chartDefinition.optionalMappings].map((mappingKey) => (
                    <DropZone
                      key={mappingKey}
                      label={mappingKey.toUpperCase()}
                      mappingKey={mappingKey}
                      value={mapping[mappingKey]}
                      onDrop={handleFieldDrop}
                      onRemove={handleMappingRemove}
                      isRequired={chartDefinition.requiredMappings.includes(mappingKey)}
                      supportedTypes={chartDefinition.supportedDataTypes[mappingKey]}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Available Fields */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Available Fields
              </label>
              <SortableContext items={fields.map(f => f.name)} strategy={verticalListSortingStrategy}>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {fields.map((field) => (
                    <div
                      key={field.name}
                      draggable
                      onDragStart={(e) => {
                        e.dataTransfer.setData('text/plain', field.name);
                      }}
                    >
                      <DraggableField field={field} id={field.name} />
                    </div>
                  ))}
                </div>
              </SortableContext>
            </div>

            {/* Validation Errors */}
            {!validation.valid && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <div className="text-sm font-medium text-red-800 mb-1">Configuration Issues:</div>
                <ul className="text-sm text-red-700 space-y-1">
                  {validation.errors.map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* Actions */}
            <div className="flex space-x-2">
              <button
                onClick={handleSave}
                disabled={!validation.valid}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Save Chart
              </button>
              {onCancel && (
                <button
                  onClick={onCancel}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Right Panel - Preview */}
        <div className="flex-1 p-4">
          <div className="h-full bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-4 border-b border-gray-200">
              <h3 className="text-lg font-medium">Chart Preview</h3>
            </div>
            <div className="p-4 h-full">
              {validation.valid ? (
                <ChartRenderer
                  config={{
                    id: 'preview',
                    type: selectedChartType,
                    name: chartName,
                    datasetId: dataset.id,
                    mapping,
                    style: generateDefaultStyle(selectedChartType),
                    filters: [],
                    position: generateDefaultPosition(),
                  }}
                  data={chartData}
                />
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <div className="text-4xl mb-4">📊</div>
                    <div className="text-lg font-medium mb-2">Configure your chart</div>
                    <div className="text-sm">
                      Select a chart type and map your data fields to see a preview
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <DragOverlay>
          {activeId ? (
            <div className="p-2 bg-white border border-gray-200 rounded-md shadow-lg">
              {activeId}
            </div>
          ) : null}
        </DragOverlay>
      </DndContext>
    </div>
  );
}
