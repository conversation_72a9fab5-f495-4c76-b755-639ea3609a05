{"name": "@types/react-grid-layout", "version": "1.3.5", "description": "TypeScript definitions for react-grid-layout", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-grid-layout", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "abirkholz", "url": "https://github.com/abirkholz"}, {"name": "<PERSON>", "githubUsername": "al<PERSON><PERSON><PERSON>", "url": "https://github.com/alitaheri"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "ZheyangSong", "url": "https://github.com/Zheyang<PERSON>ong"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/andrewhathaway"}, {"name": "<PERSON><PERSON>", "githubUsername": "manav-m", "url": "https://github.com/manav-m"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/al-<PERSON>yo<PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-grid-layout"}, "scripts": {}, "dependencies": {"@types/react": "*"}, "typesPublisherContentHash": "887729039254ec34540874e890437be1d3a4d5fac2bd619a43b286a188f1418a", "typeScriptVersion": "4.5"}