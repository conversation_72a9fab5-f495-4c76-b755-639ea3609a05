{"name": "react-grid-layout", "version": "1.5.2", "description": "A draggable and resizable grid layout with responsive breakpoints, for React.", "main": "index.js", "scripts": {"lint": "make lint", "test": "make test", "build": "make build", "build-example": "make build-example", "view-example": "make view-example", "dev": "make dev", "prepublishOnly": "make build", "validate": "npm ls", "flow": "flow", "fmt": "prettier --write .", "fmt:check": "prettier --check ."}, "repository": {"type": "git", "url": "**************:STRML/react-grid-layout.git"}, "keywords": ["react", "grid", "drag", "draggable", "resize", "resizable", "fluid", "responsive"], "author": "<PERSON> <<EMAIL>> (http://strml.net/)", "license": "MIT", "bugs": {"url": "https://github.com/STRML/react-grid-layout/issues"}, "homepage": "https://github.com/STRML/react-grid-layout", "dependencies": {"clsx": "^2.1.1", "fast-equals": "^4.0.3", "prop-types": "^15.8.1", "react-draggable": "^4.4.6", "react-resizable": "^3.0.5", "resize-observer-polyfill": "^1.5.1"}, "_dependencyNotes": {"fast-equals": "Bug in CRA5 causes fast-equals@5 to fail to import due to .cjs file. See https://github.com/react-grid-layout/react-grid-layout/issues/1904"}, "devDependencies": {"@babel/cli": "^7.27.2", "@babel/core": "^7.27.4", "@babel/eslint-parser": "^7.27.5", "@babel/plugin-transform-class-properties": "^7.18.6", "@babel/plugin-transform-flow-comments": "^7.27.3", "@babel/preset-env": "^7.27.2", "@babel/preset-flow": "^7.27.1", "@babel/preset-react": "^7.27.1", "@babel/register": "^7.27.1", "@webpack-cli/serve": "^2.0.5", "babel-jest": "^29.7.0", "babel-loader": "^9.1.3", "babel-plugin-preval": "^5.1.0", "css-loader": "^6.8.1", "ejs": "^3.1.10", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.8", "enzyme-to-json": "^3.6.2", "eslint": "^8.54.0", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-mocha": "^10.2.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-unicorn": "^49.0.0", "exports-loader": "^4.0.0", "flow-bin": "^0.172.0", "husky": "^8.0.3", "imports-loader": "^4.0.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.1.0", "lodash": "^4.17.21", "opener": "^1.5.2", "prettier": "^3.1.0", "react": "^16.13.1", "react-dom": "^16.13.1", "react-hot-loader": "^4.13.1", "react-transform-hmr": "^1.0.2", "style-loader": "^3.3.3", "timsort": "^0.3.0", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "resolutions": {"**/cheerio": "npm:cheerio@1.0.0-rc.12"}, "_resolutionNotes": {"**/cheerio": "See https://github.com/cheeriojs/cheerio/issues/4618"}, "peerDependencies": {"react": ">= 16.3.0", "react-dom": ">= 16.3.0"}, "publishConfig": {"registry": "https://registry.npmjs.org"}, "jest": {"setupFilesAfterEnv": ["<rootDir>test/util/setupTests.js"], "snapshotSerializers": ["enzyme-to-json/serializer"], "testMatch": ["<rootDir>/test/spec/*.js"], "testEnvironment": "jsdom", "testEnvironmentOptions": {"url": "http://localhost"}, "coverageThreshold": {"global": {"statements": 77, "branches": 72, "functions": 77, "lines": 78}}}, "lint-staged": {"*.{js,jsx}": ["eslint --ext .js,.jsx --fix"], "*": ["prettier --ignore-unknown --write"]}}