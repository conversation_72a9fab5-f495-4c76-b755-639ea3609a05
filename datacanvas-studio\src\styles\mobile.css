/* Mobile-specific styles for DataCanvas Studio */

/* Touch-friendly interactions */
.touch-target {
  min-width: 44px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Mobile-optimized buttons */
.mobile-button {
  @apply px-4 py-3 text-base font-medium rounded-lg;
  min-height: 44px;
  touch-action: manipulation;
}

.mobile-button-sm {
  @apply px-3 py-2 text-sm font-medium rounded-md;
  min-height: 36px;
  touch-action: manipulation;
}

/* Mobile form inputs */
.mobile-input {
  @apply px-4 py-3 text-base border border-gray-300 rounded-lg;
  min-height: 44px;
  -webkit-appearance: none;
  appearance: none;
}

.mobile-select {
  @apply px-4 py-3 text-base border border-gray-300 rounded-lg bg-white;
  min-height: 44px;
  -webkit-appearance: none;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: 40px;
}

/* Mobile-optimized tables */
.mobile-table {
  @apply w-full;
}

.mobile-table-responsive {
  @apply block overflow-x-auto whitespace-nowrap;
  -webkit-overflow-scrolling: touch;
}

.mobile-table-card {
  @apply block bg-white border border-gray-200 rounded-lg p-4 mb-4;
}

/* Mobile navigation */
.mobile-nav-item {
  @apply flex items-center px-4 py-3 text-base font-medium rounded-lg;
  min-height: 48px;
  touch-action: manipulation;
}

/* Mobile dashboard grid */
.mobile-dashboard-grid {
  @apply grid gap-4 p-4;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .mobile-dashboard-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Mobile chart containers */
.mobile-chart-container {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
  min-height: 200px;
}

.mobile-chart-header {
  @apply flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50;
}

.mobile-chart-content {
  @apply p-3;
  min-height: 160px;
}

/* Mobile modal optimizations */
.mobile-modal {
  @apply fixed inset-0 z-50 overflow-y-auto;
}

.mobile-modal-backdrop {
  @apply fixed inset-0 bg-black bg-opacity-50;
}

.mobile-modal-content {
  @apply relative bg-white rounded-t-xl mx-0 mt-auto mb-0 min-h-[50vh] max-h-[90vh] overflow-hidden;
}

@media (min-width: 640px) {
  .mobile-modal-content {
    @apply rounded-xl mx-4 mt-8 mb-8 min-h-0;
  }
}

/* Mobile form layouts */
.mobile-form-group {
  @apply space-y-2 mb-4;
}

.mobile-form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.mobile-form-actions {
  @apply flex flex-col space-y-3 pt-4;
}

@media (min-width: 640px) {
  .mobile-form-actions {
    @apply flex-row space-y-0 space-x-3 justify-end;
  }
}

/* Mobile data visualization optimizations */
.mobile-viz-container {
  @apply w-full overflow-hidden;
  touch-action: pan-x pan-y;
}

.mobile-viz-scrollable {
  @apply overflow-x-auto;
  -webkit-overflow-scrolling: touch;
}

/* Mobile-specific utility classes */
.mobile-only {
  @apply block;
}

.desktop-only {
  @apply hidden;
}

@media (min-width: 1024px) {
  .mobile-only {
    @apply hidden;
  }
  
  .desktop-only {
    @apply block;
  }
}

/* Touch-friendly spacing */
.mobile-spacing {
  @apply space-y-4;
}

.mobile-spacing > * + * {
  margin-top: 1rem;
}

/* Mobile-optimized cards */
.mobile-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4;
}

.mobile-card-header {
  @apply flex items-center justify-between mb-4 pb-3 border-b border-gray-200;
}

.mobile-card-content {
  @apply space-y-3;
}

/* Mobile-friendly loading states */
.mobile-loading {
  @apply flex items-center justify-center py-8;
}

.mobile-loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600;
}

/* Mobile-optimized empty states */
.mobile-empty-state {
  @apply text-center py-12 px-4;
}

.mobile-empty-state-icon {
  @apply h-12 w-12 mx-auto mb-4 text-gray-400;
}

.mobile-empty-state-title {
  @apply text-lg font-medium text-gray-900 mb-2;
}

.mobile-empty-state-description {
  @apply text-gray-600 text-sm mb-4;
}

/* Mobile-specific animations */
@keyframes mobile-slide-up {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.mobile-slide-up {
  animation: mobile-slide-up 0.3s ease-out;
}

@keyframes mobile-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.mobile-fade-in {
  animation: mobile-fade-in 0.2s ease-out;
}

/* Mobile-optimized tooltips */
.mobile-tooltip {
  @apply hidden;
}

@media (min-width: 1024px) {
  .mobile-tooltip {
    @apply block;
  }
}

/* Mobile-friendly focus states */
.mobile-focus:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

/* Mobile-optimized scrollbars */
.mobile-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 transparent;
}

.mobile-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.mobile-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.mobile-scrollbar::-webkit-scrollbar-thumb {
  background-color: #cbd5e0;
  border-radius: 3px;
}

.mobile-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #a0aec0;
}

/* Safe area handling for iOS */
@supports (padding: max(0px)) {
  .mobile-safe-area-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }
  
  .mobile-safe-area-bottom {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
  
  .mobile-safe-area-left {
    padding-left: max(1rem, env(safe-area-inset-left));
  }
  
  .mobile-safe-area-right {
    padding-right: max(1rem, env(safe-area-inset-right));
  }
}

/* Mobile-specific print styles */
@media print {
  .mobile-no-print {
    display: none !important;
  }
  
  .mobile-print-full-width {
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }
}
