import { DataSource, Dataset, CSVConnectionConfig, DatabaseConnectionConfig, APIConnectionConfig } from '@/lib/types/database';
import csv from 'csv-parser';
import fs from 'fs';
import { Client as PgClient } from 'pg';
import mysql from 'mysql2/promise';

export class DataProcessingService {
  static async processCSVData(filePath: string, config: CSVConnectionConfig): Promise<{
    schema: any;
    sampleData: any[];
    rowCount: number;
  }> {
    return new Promise((resolve, reject) => {
      const results: any[] = [];
      let schema: any = null;
      let rowCount = 0;

      fs.createReadStream(filePath)
        .pipe(csv({
          separator: config.delimiter || ',',
          headers: config.has_header !== false,
        }))
        .on('data', (data) => {
          rowCount++;
          
          // Capture first 100 rows as sample data
          if (results.length < 100) {
            results.push(data);
          }
          
          // Generate schema from first row
          if (!schema && Object.keys(data).length > 0) {
            schema = {
              columns: Object.keys(data).map(key => ({
                name: key,
                type: this.inferDataType(data[key]),
                nullable: true,
              }))
            };
          }
        })
        .on('end', () => {
          resolve({
            schema,
            sampleData: results,
            rowCount,
          });
        })
        .on('error', reject);
    });
  }

  static async processPostgreSQLData(config: DatabaseConnectionConfig, query: string): Promise<{
    schema: any;
    sampleData: any[];
    rowCount: number;
  }> {
    const client = new PgClient({
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.username,
      password: config.password,
      ssl: config.ssl ? { rejectUnauthorized: false } : false,
    });

    try {
      await client.connect();
      
      // Get sample data
      const sampleQuery = `${query} LIMIT 100`;
      const sampleResult = await client.query(sampleQuery);
      
      // Get total count
      const countQuery = `SELECT COUNT(*) FROM (${query}) as count_query`;
      const countResult = await client.query(countQuery);
      const rowCount = parseInt(countResult.rows[0].count);
      
      // Generate schema from result fields
      const schema = {
        columns: sampleResult.fields.map(field => ({
          name: field.name,
          type: this.mapPostgreSQLType(field.dataTypeID),
          nullable: true,
        }))
      };

      return {
        schema,
        sampleData: sampleResult.rows,
        rowCount,
      };
    } finally {
      await client.end();
    }
  }

  static async processMySQLData(config: DatabaseConnectionConfig, query: string): Promise<{
    schema: any;
    sampleData: any[];
    rowCount: number;
  }> {
    const connection = await mysql.createConnection({
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.username,
      password: config.password,
      ssl: config.ssl ? {} : false,
    });

    try {
      // Get sample data
      const sampleQuery = `${query} LIMIT 100`;
      const [sampleRows, sampleFields] = await connection.execute(sampleQuery);
      
      // Get total count
      const countQuery = `SELECT COUNT(*) as count FROM (${query}) as count_query`;
      const [countRows] = await connection.execute(countQuery) as any;
      const rowCount = countRows[0].count;
      
      // Generate schema from result fields
      const schema = {
        columns: (sampleFields as any[]).map(field => ({
          name: field.name,
          type: this.mapMySQLType(field.type),
          nullable: true,
        }))
      };

      return {
        schema,
        sampleData: sampleRows as any[],
        rowCount,
      };
    } finally {
      await connection.end();
    }
  }

  static async processAPIData(config: APIConnectionConfig): Promise<{
    schema: any;
    sampleData: any[];
    rowCount: number;
  }> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...config.headers,
    };

    // Add authentication headers
    if (config.auth) {
      switch (config.auth.type) {
        case 'bearer':
          headers.Authorization = `Bearer ${config.auth.token}`;
          break;
        case 'basic':
          const credentials = Buffer.from(`${config.auth.username}:${config.auth.password}`).toString('base64');
          headers.Authorization = `Basic ${credentials}`;
          break;
        case 'api_key':
          headers[config.auth.api_key_header || 'X-API-Key'] = config.auth.api_key!;
          break;
      }
    }

    const url = new URL(config.url);
    if (config.params) {
      Object.entries(config.params).forEach(([key, value]) => {
        url.searchParams.append(key, value);
      });
    }

    const response = await fetch(url.toString(), {
      method: config.method,
      headers,
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.statusText}`);
    }

    const data = await response.json();
    
    // Handle different response formats
    let arrayData: any[] = [];
    if (Array.isArray(data)) {
      arrayData = data;
    } else if (data.data && Array.isArray(data.data)) {
      arrayData = data.data;
    } else if (data.results && Array.isArray(data.results)) {
      arrayData = data.results;
    } else {
      throw new Error('API response is not in a supported array format');
    }

    const sampleData = arrayData.slice(0, 100);
    
    // Generate schema from first item
    const schema = {
      columns: sampleData.length > 0 
        ? Object.keys(sampleData[0]).map(key => ({
            name: key,
            type: this.inferDataType(sampleData[0][key]),
            nullable: true,
          }))
        : []
    };

    return {
      schema,
      sampleData,
      rowCount: arrayData.length,
    };
  }

  static async getDatasetData(dataset: Dataset, filters?: any[], limit = 1000, offset = 0): Promise<any[]> {
    // This would implement the actual data fetching based on the dataset's data source
    // For now, return sample data
    return dataset.sample_data || [];
  }

  private static inferDataType(value: any): string {
    if (value === null || value === undefined) return 'text';
    
    if (typeof value === 'number') {
      return Number.isInteger(value) ? 'integer' : 'decimal';
    }
    
    if (typeof value === 'boolean') return 'boolean';
    
    if (typeof value === 'string') {
      // Check if it's a date
      if (!isNaN(Date.parse(value))) return 'date';
      
      // Check if it's a number
      if (!isNaN(Number(value))) {
        return value.includes('.') ? 'decimal' : 'integer';
      }
      
      return 'text';
    }
    
    return 'text';
  }

  private static mapPostgreSQLType(typeId: number): string {
    const typeMap: Record<number, string> = {
      16: 'boolean',
      20: 'integer',
      21: 'integer',
      23: 'integer',
      25: 'text',
      700: 'decimal',
      701: 'decimal',
      1082: 'date',
      1114: 'datetime',
      1184: 'datetime',
    };
    
    return typeMap[typeId] || 'text';
  }

  private static mapMySQLType(type: number): string {
    const typeMap: Record<number, string> = {
      1: 'integer',
      2: 'integer',
      3: 'integer',
      4: 'decimal',
      5: 'decimal',
      7: 'datetime',
      10: 'date',
      12: 'datetime',
      253: 'text',
      254: 'text',
    };
    
    return typeMap[type] || 'text';
  }

  static async validateDataSource(dataSource: DataSource): Promise<boolean> {
    try {
      switch (dataSource.type) {
        case 'csv':
          const csvConfig = dataSource.connection_config as CSVConnectionConfig;
          return fs.existsSync(csvConfig.file_path);
          
        case 'postgresql':
          const pgConfig = dataSource.connection_config as DatabaseConnectionConfig;
          const pgClient = new PgClient({
            host: pgConfig.host,
            port: pgConfig.port,
            database: pgConfig.database,
            user: pgConfig.username,
            password: pgConfig.password,
            ssl: pgConfig.ssl ? { rejectUnauthorized: false } : false,
          });
          await pgClient.connect();
          await pgClient.end();
          return true;
          
        case 'mysql':
          const mysqlConfig = dataSource.connection_config as DatabaseConnectionConfig;
          const connection = await mysql.createConnection({
            host: mysqlConfig.host,
            port: mysqlConfig.port,
            database: mysqlConfig.database,
            user: mysqlConfig.username,
            password: mysqlConfig.password,
            ssl: mysqlConfig.ssl ? {} : false,
          });
          await connection.end();
          return true;
          
        case 'api':
          const apiConfig = dataSource.connection_config as APIConnectionConfig;
          const response = await fetch(apiConfig.url, { method: 'HEAD' });
          return response.ok;
          
        default:
          return false;
      }
    } catch (error) {
      console.error('Data source validation failed:', error);
      return false;
    }
  }
}
