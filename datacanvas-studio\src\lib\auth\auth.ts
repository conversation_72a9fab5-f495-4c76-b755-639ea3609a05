import { createSupabaseServerClient } from '@/lib/supabase/server';
import { User } from '@/lib/types/database';
import { redirect } from 'next/navigation';

export async function requireAuth(): Promise<User> {
  const supabase = await createSupabaseServerClient();
  const { data: { user }, error } = await supabase.auth.getUser();

  if (error || !user) {
    redirect('/auth/login');
  }

  // Get user profile from our users table
  const { data: profile, error: profileError } = await supabase
    .from('users')
    .select('*')
    .eq('id', user.id)
    .single();

  if (profileError || !profile) {
    // Create user profile if it doesn't exist
    const { data: newProfile, error: createError } = await supabase
      .from('users')
      .insert({
        id: user.id,
        email: user.email!,
        full_name: user.user_metadata?.full_name || null,
        avatar_url: user.user_metadata?.avatar_url || null,
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating user profile:', createError);
      redirect('/auth/login');
    }

    return newProfile;
  }

  return profile;
}

export async function requireRole(allowedRoles: string[]): Promise<User> {
  const user = await requireAuth();
  
  if (!allowedRoles.includes(user.role)) {
    redirect('/unauthorized');
  }
  
  return user;
}

export async function requireAdmin(): Promise<User> {
  return requireRole(['admin']);
}

export async function requireEditorOrAdmin(): Promise<User> {
  return requireRole(['admin', 'editor']);
}

export async function getOptionalAuth(): Promise<User | null> {
  try {
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error || !user) {
      return null;
    }

    const { data: profile } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single();

    return profile;
  } catch {
    return null;
  }
}

export async function signOut() {
  const supabase = await createSupabaseServerClient();
  await supabase.auth.signOut();
  redirect('/auth/login');
}

export function hasPermission(user: User, requiredRole: string): boolean {
  const roleHierarchy = {
    viewer: 1,
    editor: 2,
    admin: 3,
  };

  const userLevel = roleHierarchy[user.role as keyof typeof roleHierarchy] || 0;
  const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;

  return userLevel >= requiredLevel;
}

export function canEditDashboard(user: User, dashboard: any, permissions: any[] = []): boolean {
  // Admin can edit everything
  if (user.role === 'admin') return true;
  
  // Dashboard creator can edit
  if (dashboard.created_by === user.id) return true;
  
  // Check explicit permissions
  const userPermission = permissions.find(p => p.user_id === user.id);
  if (userPermission && ['edit', 'admin'].includes(userPermission.permission)) {
    return true;
  }
  
  return false;
}

export function canViewDashboard(user: User, dashboard: any, permissions: any[] = []): boolean {
  // Public dashboards can be viewed by anyone
  if (dashboard.is_public) return true;
  
  // Same organization members can view
  if (dashboard.organization_id === user.organization_id) return true;
  
  // Check explicit permissions
  const userPermission = permissions.find(p => p.user_id === user.id);
  if (userPermission) return true;
  
  return false;
}
