import { createSupabaseServerClient } from '@/lib/supabase/server';
import { 
  User, 
  Organization, 
  DataSource, 
  Dataset, 
  Dashboard, 
  Chart, 
  Comment,
  Report,
  AuditLog,
  ApiResponse,
  PaginatedResponse 
} from '@/lib/types/database';

export class DatabaseService {
  private static async getClient() {
    return await createSupabaseServerClient();
  }

  // User operations
  static async getUser(id: string): Promise<ApiResponse<User>> {
    try {
      const supabase = await this.getClient();
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  static async updateUser(id: string, updates: Partial<User>): Promise<ApiResponse<User>> {
    try {
      const supabase = await this.getClient();
      const { data, error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  // Organization operations
  static async getOrganization(id: string): Promise<ApiResponse<Organization>> {
    try {
      const supabase = await this.getClient();
      const { data, error } = await supabase
        .from('organizations')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  static async createOrganization(org: Omit<Organization, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<Organization>> {
    try {
      const supabase = await this.getClient();
      const { data, error } = await supabase
        .from('organizations')
        .insert(org)
        .select()
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  // Data source operations
  static async getDataSources(organizationId: string): Promise<ApiResponse<DataSource[]>> {
    try {
      const supabase = await this.getClient();
      const { data, error } = await supabase
        .from('data_sources')
        .select('*')
        .eq('organization_id', organizationId)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data: data || [] };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  static async createDataSource(dataSource: Omit<DataSource, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<DataSource>> {
    try {
      const supabase = await this.getClient();
      const { data, error } = await supabase
        .from('data_sources')
        .insert(dataSource)
        .select()
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  // Dataset operations
  static async getDatasets(organizationId: string): Promise<ApiResponse<Dataset[]>> {
    try {
      const supabase = await this.getClient();
      const { data, error } = await supabase
        .from('datasets')
        .select(`
          *,
          data_source:data_sources(name, type)
        `)
        .eq('organization_id', organizationId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data: data || [] };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  static async createDataset(dataset: Omit<Dataset, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<Dataset>> {
    try {
      const supabase = await this.getClient();
      const { data, error } = await supabase
        .from('datasets')
        .insert(dataset)
        .select()
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  // Dashboard operations
  static async getDashboards(organizationId: string, userId: string): Promise<ApiResponse<Dashboard[]>> {
    try {
      const supabase = await this.getClient();
      const { data, error } = await supabase
        .from('dashboards')
        .select(`
          *,
          created_by_user:users!dashboards_created_by_fkey(full_name, email),
          permissions:dashboard_permissions(user_id, permission)
        `)
        .or(`organization_id.eq.${organizationId},is_public.eq.true,permissions.user_id.eq.${userId}`)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data: data || [] };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  static async getDashboard(id: string): Promise<ApiResponse<Dashboard>> {
    try {
      const supabase = await this.getClient();
      const { data, error } = await supabase
        .from('dashboards')
        .select(`
          *,
          charts(*),
          permissions:dashboard_permissions(user_id, permission, user:users(full_name, email))
        `)
        .eq('id', id)
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  static async createDashboard(dashboard: Omit<Dashboard, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<Dashboard>> {
    try {
      const supabase = await this.getClient();
      const { data, error } = await supabase
        .from('dashboards')
        .insert(dashboard)
        .select()
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  static async updateDashboard(id: string, updates: Partial<Dashboard>): Promise<ApiResponse<Dashboard>> {
    try {
      const supabase = await this.getClient();
      const { data, error } = await supabase
        .from('dashboards')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  // Chart operations
  static async createChart(chart: Omit<Chart, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<Chart>> {
    try {
      const supabase = await this.getClient();
      const { data, error } = await supabase
        .from('charts')
        .insert(chart)
        .select()
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  static async updateChart(id: string, updates: Partial<Chart>): Promise<ApiResponse<Chart>> {
    try {
      const supabase = await this.getClient();
      const { data, error } = await supabase
        .from('charts')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  static async deleteChart(id: string): Promise<ApiResponse<void>> {
    try {
      const supabase = await this.getClient();
      const { error } = await supabase
        .from('charts')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return { message: 'Chart deleted successfully' };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  // Comment operations
  static async getComments(dashboardId: string): Promise<ApiResponse<Comment[]>> {
    try {
      const supabase = await this.getClient();
      const { data, error } = await supabase
        .from('comments')
        .select(`
          *,
          user:users(full_name, email, avatar_url)
        `)
        .eq('dashboard_id', dashboardId)
        .is('parent_id', null)
        .order('created_at', { ascending: true });

      if (error) throw error;

      // Get replies for each comment
      const commentsWithReplies = await Promise.all(
        (data || []).map(async (comment) => {
          const { data: replies } = await supabase
            .from('comments')
            .select(`
              *,
              user:users(full_name, email, avatar_url)
            `)
            .eq('parent_id', comment.id)
            .order('created_at', { ascending: true });

          return { ...comment, replies: replies || [] };
        })
      );

      return { data: commentsWithReplies };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  static async createComment(comment: Omit<Comment, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<Comment>> {
    try {
      const supabase = await this.getClient();
      const { data, error } = await supabase
        .from('comments')
        .insert(comment)
        .select(`
          *,
          user:users(full_name, email, avatar_url)
        `)
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  // Audit log operations
  static async createAuditLog(log: Omit<AuditLog, 'id' | 'created_at'>): Promise<void> {
    try {
      const supabase = await this.getClient();
      await supabase.from('audit_logs').insert(log);
    } catch (error) {
      console.error('Failed to create audit log:', error);
    }
  }
}
