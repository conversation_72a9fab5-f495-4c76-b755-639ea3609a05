'use client';

import { useState, useEffect } from 'react';
import { User } from '@/lib/types/database';
import { Palette, Upload, Eye, Save, RotateCcw } from 'lucide-react';

interface BrandingConfig {
  primary_color?: string;
  secondary_color?: string;
  logo_url?: string;
  favicon_url?: string;
  company_name?: string;
  app_name?: string;
  theme?: 'light' | 'dark' | 'auto';
  custom_css?: string;
  hide_powered_by?: boolean;
  custom_domain?: string;
}

interface BrandingSettingsProps {
  user: User;
}

export function BrandingSettings({ user }: BrandingSettingsProps) {
  const [branding, setBranding] = useState<BrandingConfig>({});
  const [originalBranding, setOriginalBranding] = useState<BrandingConfig>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState('');
  const [previewMode, setPreviewMode] = useState(false);

  useEffect(() => {
    loadBrandingSettings();
  }, [user.organization_id]);

  const loadBrandingSettings = async () => {
    if (!user.organization_id) return;

    try {
      const response = await fetch(`/api/organizations/${user.organization_id}/branding`);
      const result = await response.json();

      if (response.ok) {
        const brandingData = result.data?.custom_branding || {};
        setBranding(brandingData);
        setOriginalBranding(brandingData);
      } else {
        setError(result.error || 'Failed to load branding settings');
      }
    } catch (err) {
      setError('Failed to load branding settings');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!user.organization_id) return;

    setIsSaving(true);
    setError('');

    try {
      const response = await fetch(`/api/organizations/${user.organization_id}/branding`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          custom_branding: branding,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setOriginalBranding(branding);
        // You might want to trigger a page refresh or update global state here
      } else {
        setError(result.error || 'Failed to save branding settings');
      }
    } catch (err) {
      setError('Failed to save branding settings');
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    setBranding(originalBranding);
    setError('');
  };

  const handleInputChange = (field: keyof BrandingConfig, value: any) => {
    setBranding(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const hasChanges = JSON.stringify(branding) !== JSON.stringify(originalBranding);

  const applyPreviewStyles = () => {
    if (!previewMode) return {};

    return {
      '--primary-color': branding.primary_color || '#3B82F6',
      '--secondary-color': branding.secondary_color || '#6B7280',
    } as React.CSSProperties;
  };

  if (user.role !== 'admin') {
    return (
      <div className="text-center py-12">
        <Palette className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
        <p className="text-gray-600">You need admin privileges to manage branding settings.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6" style={applyPreviewStyles()}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Palette className="h-6 w-6 text-gray-600" />
          <h2 className="text-xl font-semibold text-gray-900">Branding Settings</h2>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setPreviewMode(!previewMode)}
            className={`
              inline-flex items-center px-3 py-2 border rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-blue-500
              ${previewMode 
                ? 'border-blue-300 bg-blue-50 text-blue-700' 
                : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
              }
            `}
          >
            <Eye className="h-4 w-4 mr-2" />
            {previewMode ? 'Exit Preview' : 'Preview'}
          </button>
          {hasChanges && (
            <button
              onClick={handleReset}
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </button>
          )}
          <button
            onClick={handleSave}
            disabled={!hasChanges || isSaving}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-sm text-red-800">{error}</p>
        </div>
      )}

      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Settings */}
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Company Name
                </label>
                <input
                  type="text"
                  value={branding.company_name || ''}
                  onChange={(e) => handleInputChange('company_name', e.target.value)}
                  placeholder="Your Company Name"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Application Name
                </label>
                <input
                  type="text"
                  value={branding.app_name || ''}
                  onChange={(e) => handleInputChange('app_name', e.target.value)}
                  placeholder="DataCanvas Studio"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Custom Domain
                </label>
                <input
                  type="url"
                  value={branding.custom_domain || ''}
                  onChange={(e) => handleInputChange('custom_domain', e.target.value)}
                  placeholder="https://analytics.yourcompany.com"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Configure your custom domain for white-label deployment
                </p>
              </div>
            </div>
          </div>

          {/* Colors & Theme */}
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Colors & Theme</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Primary Color
                </label>
                <div className="flex items-center space-x-3">
                  <input
                    type="color"
                    value={branding.primary_color || '#3B82F6'}
                    onChange={(e) => handleInputChange('primary_color', e.target.value)}
                    className="h-10 w-16 border border-gray-300 rounded-md cursor-pointer"
                  />
                  <input
                    type="text"
                    value={branding.primary_color || ''}
                    onChange={(e) => handleInputChange('primary_color', e.target.value)}
                    placeholder="#3B82F6"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Secondary Color
                </label>
                <div className="flex items-center space-x-3">
                  <input
                    type="color"
                    value={branding.secondary_color || '#6B7280'}
                    onChange={(e) => handleInputChange('secondary_color', e.target.value)}
                    className="h-10 w-16 border border-gray-300 rounded-md cursor-pointer"
                  />
                  <input
                    type="text"
                    value={branding.secondary_color || ''}
                    onChange={(e) => handleInputChange('secondary_color', e.target.value)}
                    placeholder="#6B7280"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Theme
                </label>
                <select
                  value={branding.theme || 'light'}
                  onChange={(e) => handleInputChange('theme', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="light">Light</option>
                  <option value="dark">Dark</option>
                  <option value="auto">Auto (System)</option>
                </select>
              </div>
            </div>
          </div>

          {/* Logo & Assets */}
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Logo & Assets</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Logo URL
                </label>
                <input
                  type="url"
                  value={branding.logo_url || ''}
                  onChange={(e) => handleInputChange('logo_url', e.target.value)}
                  placeholder="https://example.com/logo.png"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                {branding.logo_url && (
                  <div className="mt-2">
                    <img
                      src={branding.logo_url}
                      alt="Logo preview"
                      className="h-12 object-contain"
                      onError={(e) => {
                        (e.target as HTMLImageElement).style.display = 'none';
                      }}
                    />
                  </div>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Favicon URL
                </label>
                <input
                  type="url"
                  value={branding.favicon_url || ''}
                  onChange={(e) => handleInputChange('favicon_url', e.target.value)}
                  placeholder="https://example.com/favicon.ico"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Advanced Settings */}
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Advanced Settings</h3>
            <div className="space-y-4">
              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={branding.hide_powered_by || false}
                    onChange={(e) => handleInputChange('hide_powered_by', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    Hide "Powered by DataCanvas Studio"
                  </span>
                </label>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Custom CSS
                </label>
                <textarea
                  value={branding.custom_css || ''}
                  onChange={(e) => handleInputChange('custom_css', e.target.value)}
                  placeholder="/* Add your custom CSS here */"
                  rows={6}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Advanced: Add custom CSS to further customize the appearance
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
