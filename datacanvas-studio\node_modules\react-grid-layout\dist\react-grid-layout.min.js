!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("react"),require("react-dom")):"function"==typeof define&&define.amd?define(["react","react-dom"],e):"object"==typeof exports?exports.ReactGridLayout=e(require("react"),require("react-dom")):t.ReactGridLayout=e(t.React,t.ReactDOM)}(self,(t,e)=>(()=>{var n={12:e=>{"use strict";e.exports=t},27:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"DraggableCore",{enumerable:!0,get:function(){return c.default}}),e.default=void 0;var r=function(t,e){if(t&&t.__esModule)return t;if(null===t||"object"!=typeof t&&"function"!=typeof t)return{default:t};var n=h(e);if(n&&n.has(t))return n.get(t);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in t)if("default"!==i&&Object.prototype.hasOwnProperty.call(t,i)){var a=o?Object.getOwnPropertyDescriptor(t,i):null;a&&(a.get||a.set)?Object.defineProperty(r,i,a):r[i]=t[i]}return r.default=t,n&&n.set(t,r),r}(n(12)),o=d(n(556)),i=d(n(33)),a=d(n(322)),s=n(89),l=n(726),u=n(56),c=d(n(888)),p=d(n(696));function d(t){return t&&t.__esModule?t:{default:t}}function h(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,n=new WeakMap;return(h=function(t){return t?n:e})(t)}function f(){return f=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},f.apply(this,arguments)}function g(t,e,n){return(e=function(t){var e=function(t){if("object"!=typeof t||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}class m extends r.Component{static getDerivedStateFromProps(t,e){let{position:n}=t,{prevPropsPosition:r}=e;return!n||r&&n.x===r.x&&n.y===r.y?null:((0,p.default)("Draggable: getDerivedStateFromProps %j",{position:n,prevPropsPosition:r}),{x:n.x,y:n.y,prevPropsPosition:{...n}})}constructor(t){super(t),g(this,"onDragStart",(t,e)=>{if((0,p.default)("Draggable: onDragStart: %j",e),!1===this.props.onStart(t,(0,l.createDraggableData)(this,e)))return!1;this.setState({dragging:!0,dragged:!0})}),g(this,"onDrag",(t,e)=>{if(!this.state.dragging)return!1;(0,p.default)("Draggable: onDrag: %j",e);const n=(0,l.createDraggableData)(this,e),r={x:n.x,y:n.y,slackX:0,slackY:0};if(this.props.bounds){const{x:t,y:e}=r;r.x+=this.state.slackX,r.y+=this.state.slackY;const[o,i]=(0,l.getBoundPosition)(this,r.x,r.y);r.x=o,r.y=i,r.slackX=this.state.slackX+(t-r.x),r.slackY=this.state.slackY+(e-r.y),n.x=r.x,n.y=r.y,n.deltaX=r.x-this.state.x,n.deltaY=r.y-this.state.y}if(!1===this.props.onDrag(t,n))return!1;this.setState(r)}),g(this,"onDragStop",(t,e)=>{if(!this.state.dragging)return!1;if(!1===this.props.onStop(t,(0,l.createDraggableData)(this,e)))return!1;(0,p.default)("Draggable: onDragStop: %j",e);const n={dragging:!1,slackX:0,slackY:0};if(Boolean(this.props.position)){const{x:t,y:e}=this.props.position;n.x=t,n.y=e}this.setState(n)}),this.state={dragging:!1,dragged:!1,x:t.position?t.position.x:t.defaultPosition.x,y:t.position?t.position.y:t.defaultPosition.y,prevPropsPosition:{...t.position},slackX:0,slackY:0,isElementSVG:!1},!t.position||t.onDrag||t.onStop||console.warn("A `position` was applied to this <Draggable>, without drag handlers. This will make this component effectively undraggable. Please attach `onDrag` or `onStop` handlers so you can adjust the `position` of this element.")}componentDidMount(){void 0!==window.SVGElement&&this.findDOMNode()instanceof window.SVGElement&&this.setState({isElementSVG:!0})}componentWillUnmount(){this.setState({dragging:!1})}findDOMNode(){var t,e;return null!==(t=null===(e=this.props)||void 0===e||null===(e=e.nodeRef)||void 0===e?void 0:e.current)&&void 0!==t?t:i.default.findDOMNode(this)}render(){const{axis:t,bounds:e,children:n,defaultPosition:o,defaultClassName:i,defaultClassNameDragging:u,defaultClassNameDragged:p,position:d,positionOffset:h,scale:g,...m}=this.props;let y={},b=null;const v=!Boolean(d)||this.state.dragging,w=d||o,S={x:(0,l.canDragX)(this)&&v?this.state.x:w.x,y:(0,l.canDragY)(this)&&v?this.state.y:w.y};this.state.isElementSVG?b=(0,s.createSVGTransform)(S,h):y=(0,s.createCSSTransform)(S,h);const O=(0,a.default)(n.props.className||"",i,{[u]:this.state.dragging,[p]:this.state.dragged});return r.createElement(c.default,f({},m,{onStart:this.onDragStart,onDrag:this.onDrag,onStop:this.onDragStop}),r.cloneElement(r.Children.only(n),{className:O,style:{...n.props.style,...y},transform:b}))}}e.default=m,g(m,"displayName","Draggable"),g(m,"propTypes",{...c.default.propTypes,axis:o.default.oneOf(["both","x","y","none"]),bounds:o.default.oneOfType([o.default.shape({left:o.default.number,right:o.default.number,top:o.default.number,bottom:o.default.number}),o.default.string,o.default.oneOf([!1])]),defaultClassName:o.default.string,defaultClassNameDragging:o.default.string,defaultClassNameDragged:o.default.string,defaultPosition:o.default.shape({x:o.default.number,y:o.default.number}),positionOffset:o.default.shape({x:o.default.oneOfType([o.default.number,o.default.string]),y:o.default.oneOfType([o.default.number,o.default.string])}),position:o.default.shape({x:o.default.number,y:o.default.number}),className:u.dontSetMe,style:u.dontSetMe,transform:u.dontSetMe}),g(m,"defaultProps",{...c.default.defaultProps,axis:"both",bounds:!1,defaultClassName:"react-draggable",defaultClassNameDragging:"react-draggable-dragging",defaultClassNameDragged:"react-draggable-dragged",defaultPosition:{x:0,y:0},scale:1})},30:(t,e,n)=>{"use strict";e.__esModule=!0,e.cloneElement=function(t,e){return e.style&&t.props.style&&(e.style=a(a({},t.props.style),e.style)),e.className&&t.props.className&&(e.className=t.props.className+" "+e.className),o.default.cloneElement(t,e)};var r,o=(r=n(12))&&r.__esModule?r:{default:r};function i(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function a(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?i(Object(n),!0).forEach(function(e){s(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function s(t,e,n){return(e=function(t){var e=function(t){if("object"!=typeof t||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}},33:t=>{"use strict";t.exports=e},36:(t,e,n)=>{"use strict";t.exports=function(){throw new Error("Don't instantiate Resizable directly! Use require('react-resizable').Resizable")},t.exports.Resizable=n(630).default,t.exports.ResizableBox=n(661).default},56:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.dontSetMe=function(t,e,n){if(t[e])return new Error("Invalid prop ".concat(e," passed to ").concat(n," - do not set this, set it on the child."))},e.findInArray=function(t,e){for(let n=0,r=t.length;n<r;n++)if(e.apply(e,[t[n],n,t]))return t[n]},e.int=function(t){return parseInt(t,10)},e.isFunction=function(t){return"function"==typeof t||"[object Function]"===Object.prototype.toString.call(t)},e.isNum=function(t){return"number"==typeof t&&!isNaN(t)}},89:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.addClassName=u,e.addEvent=function(t,e,n,r){if(!t)return;const o={capture:!0,...r};t.addEventListener?t.addEventListener(e,n,o):t.attachEvent?t.attachEvent("on"+e,n):t["on"+e]=n},e.addUserSelectStyles=function(t){if(!t)return;let e=t.getElementById("react-draggable-style-el");e||(e=t.createElement("style"),e.type="text/css",e.id="react-draggable-style-el",e.innerHTML=".react-draggable-transparent-selection *::-moz-selection {all: inherit;}\n",e.innerHTML+=".react-draggable-transparent-selection *::selection {all: inherit;}\n",t.getElementsByTagName("head")[0].appendChild(e)),t.body&&u(t.body,"react-draggable-transparent-selection")},e.createCSSTransform=function(t,e){const n=l(t,e,"px");return{[(0,o.browserPrefixToKey)("transform",o.default)]:n}},e.createSVGTransform=function(t,e){return l(t,e,"")},e.getTouch=function(t,e){return t.targetTouches&&(0,r.findInArray)(t.targetTouches,t=>e===t.identifier)||t.changedTouches&&(0,r.findInArray)(t.changedTouches,t=>e===t.identifier)},e.getTouchIdentifier=function(t){return t.targetTouches&&t.targetTouches[0]?t.targetTouches[0].identifier:t.changedTouches&&t.changedTouches[0]?t.changedTouches[0].identifier:void 0},e.getTranslation=l,e.innerHeight=function(t){let e=t.clientHeight;const n=t.ownerDocument.defaultView.getComputedStyle(t);return e-=(0,r.int)(n.paddingTop),e-=(0,r.int)(n.paddingBottom),e},e.innerWidth=function(t){let e=t.clientWidth;const n=t.ownerDocument.defaultView.getComputedStyle(t);return e-=(0,r.int)(n.paddingLeft),e-=(0,r.int)(n.paddingRight),e},e.matchesSelector=s,e.matchesSelectorAndParentsTo=function(t,e,n){let r=t;do{if(s(r,e))return!0;if(r===n)return!1;r=r.parentNode}while(r);return!1},e.offsetXYFromParent=function(t,e,n){const r=e===e.ownerDocument.body?{left:0,top:0}:e.getBoundingClientRect();return{x:(t.clientX+e.scrollLeft-r.left)/n,y:(t.clientY+e.scrollTop-r.top)/n}},e.outerHeight=function(t){let e=t.clientHeight;const n=t.ownerDocument.defaultView.getComputedStyle(t);return e+=(0,r.int)(n.borderTopWidth),e+=(0,r.int)(n.borderBottomWidth),e},e.outerWidth=function(t){let e=t.clientWidth;const n=t.ownerDocument.defaultView.getComputedStyle(t);return e+=(0,r.int)(n.borderLeftWidth),e+=(0,r.int)(n.borderRightWidth),e},e.removeClassName=c,e.removeEvent=function(t,e,n,r){if(!t)return;const o={capture:!0,...r};t.removeEventListener?t.removeEventListener(e,n,o):t.detachEvent?t.detachEvent("on"+e,n):t["on"+e]=null},e.removeUserSelectStyles=function(t){if(t)try{if(t.body&&c(t.body,"react-draggable-transparent-selection"),t.selection)t.selection.empty();else{const e=(t.defaultView||window).getSelection();e&&"Caret"!==e.type&&e.removeAllRanges()}}catch(t){}};var r=n(56),o=function(t,e){if(t&&t.__esModule)return t;if(null===t||"object"!=typeof t&&"function"!=typeof t)return{default:t};var n=i(e);if(n&&n.has(t))return n.get(t);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in t)if("default"!==a&&Object.prototype.hasOwnProperty.call(t,a)){var s=o?Object.getOwnPropertyDescriptor(t,a):null;s&&(s.get||s.set)?Object.defineProperty(r,a,s):r[a]=t[a]}return r.default=t,n&&n.set(t,r),r}(n(514));function i(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,n=new WeakMap;return(i=function(t){return t?n:e})(t)}let a="";function s(t,e){return a||(a=(0,r.findInArray)(["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"],function(e){return(0,r.isFunction)(t[e])})),!!(0,r.isFunction)(t[a])&&t[a](e)}function l(t,e,n){let{x:r,y:o}=t,i="translate(".concat(r).concat(n,",").concat(o).concat(n,")");if(e){const t="".concat("string"==typeof e.x?e.x:e.x+n),r="".concat("string"==typeof e.y?e.y:e.y+n);i="translate(".concat(t,", ").concat(r,")")+i}return i}function u(t,e){t.classList?t.classList.add(e):t.className.match(new RegExp("(?:^|\\s)".concat(e,"(?!\\S)")))||(t.className+=" ".concat(e))}function c(t,e){t.classList?t.classList.remove(e):t.className=t.className.replace(new RegExp("(?:^|\\s)".concat(e,"(?!\\S)"),"g"),"")}},159:(t,e,n)=>{"use strict";n.d(e,{default:()=>f});var r=n(12),o=n(556),i=n.n(o),a=n(548),s=n(335),l=n(209),u=n(326);function c(){return c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)({}).hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},c.apply(null,arguments)}function p(t,e,n){return(e=function(t){var e=function(t){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:e+""}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}const d=t=>Object.prototype.toString.call(t);function h(t,e){return null==t?null:Array.isArray(t)?t:t[e]}class f extends r.Component{constructor(){super(...arguments),p(this,"state",this.generateInitialState()),p(this,"onLayoutChange",t=>{this.props.onLayoutChange(t,{...this.props.layouts,[this.state.breakpoint]:t})})}generateInitialState(){const{width:t,breakpoints:e,layouts:n,cols:r}=this.props,o=(0,l.getBreakpointFromWidth)(e,t),i=(0,l.getColsFromBreakpoint)(o,r),a=!1===this.props.verticalCompact?null:this.props.compactType;return{layout:(0,l.findOrGenerateResponsiveLayout)(n,e,o,o,i,a),breakpoint:o,cols:i}}static getDerivedStateFromProps(t,e){if(!(0,a.deepEqual)(t.layouts,e.layouts)){const{breakpoint:n,cols:r}=e;return{layout:(0,l.findOrGenerateResponsiveLayout)(t.layouts,t.breakpoints,n,n,r,t.compactType),layouts:t.layouts}}return null}componentDidUpdate(t){this.props.width==t.width&&this.props.breakpoint===t.breakpoint&&(0,a.deepEqual)(this.props.breakpoints,t.breakpoints)&&(0,a.deepEqual)(this.props.cols,t.cols)||this.onWidthChange(t)}onWidthChange(t){const{breakpoints:e,cols:n,layouts:r,compactType:o}=this.props,i=this.props.breakpoint||(0,l.getBreakpointFromWidth)(this.props.breakpoints,this.props.width),a=this.state.breakpoint,u=(0,l.getColsFromBreakpoint)(i,n),c={...r};if(a!==i||t.breakpoints!==e||t.cols!==n){a in c||(c[a]=(0,s.cloneLayout)(this.state.layout));let t=(0,l.findOrGenerateResponsiveLayout)(c,e,i,a,u,o);t=(0,s.synchronizeLayoutWithChildren)(t,this.props.children,u,o,this.props.allowOverlap),c[i]=t,this.props.onBreakpointChange(i,u),this.props.onLayoutChange(t,c),this.setState({breakpoint:i,layout:t,cols:u})}const p=h(this.props.margin,i),d=h(this.props.containerPadding,i);this.props.onWidthChange(this.props.width,p,u,d)}render(){const{breakpoint:t,breakpoints:e,cols:n,layouts:o,margin:i,containerPadding:a,onBreakpointChange:s,onLayoutChange:l,onWidthChange:p,...d}=this.props;return r.createElement(u.default,c({},d,{margin:h(i,this.state.breakpoint),containerPadding:h(a,this.state.breakpoint),onLayoutChange:this.onLayoutChange,layout:this.state.layout,cols:this.state.cols}))}}p(f,"propTypes",{breakpoint:i().string,breakpoints:i().object,allowOverlap:i().bool,cols:i().object,margin:i().oneOfType([i().array,i().object]),containerPadding:i().oneOfType([i().array,i().object]),layouts(t,e){if("[object Object]"!==d(t[e]))throw new Error("Layout property must be an object. Received: "+d(t[e]));Object.keys(t[e]).forEach(e=>{if(!(e in t.breakpoints))throw new Error("Each key in layouts must align with a key in breakpoints.");(0,s.validateLayout)(t.layouts[e],"layouts."+e)})},width:i().number.isRequired,onBreakpointChange:i().func,onLayoutChange:i().func,onWidthChange:i().func}),p(f,"defaultProps",{breakpoints:{lg:1200,md:996,sm:768,xs:480,xxs:0},cols:{lg:12,md:10,sm:6,xs:4,xxs:2},containerPadding:{lg:null,md:null,sm:null,xs:null,xxs:null},layouts:{},margin:[10,10],allowOverlap:!1,onBreakpointChange:s.noop,onLayoutChange:s.noop,onWidthChange:s.noop})},164:(t,e,n)=>{"use strict";function r(t){var e,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var i=t.length;for(e=0;e<i;e++)t[e]&&(n=r(t[e]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n);return o}n.d(e,{A:()=>o});const o=function(){for(var t,e,n=0,o="",i=arguments.length;n<i;n++)(t=arguments[n])&&(e=r(t))&&(o&&(o+=" "),o+=e);return o}},209:(t,e,n)=>{"use strict";n.r(e),n.d(e,{findOrGenerateResponsiveLayout:()=>a,getBreakpointFromWidth:()=>o,getColsFromBreakpoint:()=>i,sortBreakpoints:()=>s});var r=n(335);function o(t,e){const n=s(t);let r=n[0];for(let o=1,i=n.length;o<i;o++){const i=n[o];e>t[i]&&(r=i)}return r}function i(t,e){if(!e[t])throw new Error("ResponsiveReactGridLayout: `cols` entry for breakpoint "+t+" is missing!");return e[t]}function a(t,e,n,o,i,a){if(t[n])return(0,r.cloneLayout)(t[n]);let l=t[o];const u=s(e),c=u.slice(u.indexOf(n));for(let e=0,n=c.length;e<n;e++){const n=c[e];if(t[n]){l=t[n];break}}return l=(0,r.cloneLayout)(l||[]),(0,r.compact)((0,r.correctBounds)(l,{cols:i}),a,i)}function s(t){return Object.keys(t).sort(function(e,n){return t[e]-t[n]})}},224:(t,e,n)=>{"use strict";n.d(e,{default:()=>M});var r=n(12),o=n(556),i=n.n(o),a=function(){if("undefined"!=typeof Map)return Map;function t(t,e){var n=-1;return t.some(function(t,r){return t[0]===e&&(n=r,!0)}),n}return function(){function e(){this.__entries__=[]}return Object.defineProperty(e.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),e.prototype.get=function(e){var n=t(this.__entries__,e),r=this.__entries__[n];return r&&r[1]},e.prototype.set=function(e,n){var r=t(this.__entries__,e);~r?this.__entries__[r][1]=n:this.__entries__.push([e,n])},e.prototype.delete=function(e){var n=this.__entries__,r=t(n,e);~r&&n.splice(r,1)},e.prototype.has=function(e){return!!~t(this.__entries__,e)},e.prototype.clear=function(){this.__entries__.splice(0)},e.prototype.forEach=function(t,e){void 0===e&&(e=null);for(var n=0,r=this.__entries__;n<r.length;n++){var o=r[n];t.call(e,o[1],o[0])}},e}()}(),s="undefined"!=typeof window&&"undefined"!=typeof document&&window.document===document,l=void 0!==n.g&&n.g.Math===Math?n.g:"undefined"!=typeof self&&self.Math===Math?self:"undefined"!=typeof window&&window.Math===Math?window:Function("return this")(),u="function"==typeof requestAnimationFrame?requestAnimationFrame.bind(l):function(t){return setTimeout(function(){return t(Date.now())},1e3/60)},c=["top","right","bottom","left","width","height","size","weight"],p="undefined"!=typeof MutationObserver,d=function(){function t(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=function(t){var e=!1,n=!1,r=0;function o(){e&&(e=!1,t()),n&&a()}function i(){u(o)}function a(){var t=Date.now();if(e){if(t-r<2)return;n=!0}else e=!0,n=!1,setTimeout(i,20);r=t}return a}(this.refresh.bind(this))}return t.prototype.addObserver=function(t){~this.observers_.indexOf(t)||this.observers_.push(t),this.connected_||this.connect_()},t.prototype.removeObserver=function(t){var e=this.observers_,n=e.indexOf(t);~n&&e.splice(n,1),!e.length&&this.connected_&&this.disconnect_()},t.prototype.refresh=function(){this.updateObservers_()&&this.refresh()},t.prototype.updateObservers_=function(){var t=this.observers_.filter(function(t){return t.gatherActive(),t.hasActive()});return t.forEach(function(t){return t.broadcastActive()}),t.length>0},t.prototype.connect_=function(){s&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),p?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},t.prototype.disconnect_=function(){s&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},t.prototype.onTransitionEnd_=function(t){var e=t.propertyName,n=void 0===e?"":e;c.some(function(t){return!!~n.indexOf(t)})&&this.refresh()},t.getInstance=function(){return this.instance_||(this.instance_=new t),this.instance_},t.instance_=null,t}(),h=function(t,e){for(var n=0,r=Object.keys(e);n<r.length;n++){var o=r[n];Object.defineProperty(t,o,{value:e[o],enumerable:!1,writable:!1,configurable:!0})}return t},f=function(t){return t&&t.ownerDocument&&t.ownerDocument.defaultView||l},g=w(0,0,0,0);function m(t){return parseFloat(t)||0}function y(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return e.reduce(function(e,n){return e+m(t["border-"+n+"-width"])},0)}var b="undefined"!=typeof SVGGraphicsElement?function(t){return t instanceof f(t).SVGGraphicsElement}:function(t){return t instanceof f(t).SVGElement&&"function"==typeof t.getBBox};function v(t){return s?b(t)?function(t){var e=t.getBBox();return w(0,0,e.width,e.height)}(t):function(t){var e=t.clientWidth,n=t.clientHeight;if(!e&&!n)return g;var r=f(t).getComputedStyle(t),o=function(t){for(var e={},n=0,r=["top","right","bottom","left"];n<r.length;n++){var o=r[n],i=t["padding-"+o];e[o]=m(i)}return e}(r),i=o.left+o.right,a=o.top+o.bottom,s=m(r.width),l=m(r.height);if("border-box"===r.boxSizing&&(Math.round(s+i)!==e&&(s-=y(r,"left","right")+i),Math.round(l+a)!==n&&(l-=y(r,"top","bottom")+a)),!function(t){return t===f(t).document.documentElement}(t)){var u=Math.round(s+i)-e,c=Math.round(l+a)-n;1!==Math.abs(u)&&(s-=u),1!==Math.abs(c)&&(l-=c)}return w(o.left,o.top,s,l)}(t):g}function w(t,e,n,r){return{x:t,y:e,width:n,height:r}}var S=function(){function t(t){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=w(0,0,0,0),this.target=t}return t.prototype.isActive=function(){var t=v(this.target);return this.contentRect_=t,t.width!==this.broadcastWidth||t.height!==this.broadcastHeight},t.prototype.broadcastRect=function(){var t=this.contentRect_;return this.broadcastWidth=t.width,this.broadcastHeight=t.height,t},t}(),O=function(t,e){var n,r,o,i,a,s,l,u=(r=(n=e).x,o=n.y,i=n.width,a=n.height,s="undefined"!=typeof DOMRectReadOnly?DOMRectReadOnly:Object,l=Object.create(s.prototype),h(l,{x:r,y:o,width:i,height:a,top:o,right:r+i,bottom:a+o,left:r}),l);h(this,{target:t,contentRect:u})},x=function(){function t(t,e,n){if(this.activeObservations_=[],this.observations_=new a,"function"!=typeof t)throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=t,this.controller_=e,this.callbackCtx_=n}return t.prototype.observe=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(t instanceof f(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var e=this.observations_;e.has(t)||(e.set(t,new S(t)),this.controller_.addObserver(this),this.controller_.refresh())}},t.prototype.unobserve=function(t){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(t instanceof f(t).Element))throw new TypeError('parameter 1 is not of type "Element".');var e=this.observations_;e.has(t)&&(e.delete(t),e.size||this.controller_.removeObserver(this))}},t.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},t.prototype.gatherActive=function(){var t=this;this.clearActive(),this.observations_.forEach(function(e){e.isActive()&&t.activeObservations_.push(e)})},t.prototype.broadcastActive=function(){if(this.hasActive()){var t=this.callbackCtx_,e=this.activeObservations_.map(function(t){return new O(t.target,t.broadcastRect())});this.callback_.call(t,e,t),this.clearActive()}},t.prototype.clearActive=function(){this.activeObservations_.splice(0)},t.prototype.hasActive=function(){return this.activeObservations_.length>0},t}(),D="undefined"!=typeof WeakMap?new WeakMap:new a,R=function t(e){if(!(this instanceof t))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var n=d.getInstance(),r=new x(e,n,this);D.set(this,r)};["observe","unobserve","disconnect"].forEach(function(t){R.prototype[t]=function(){var e;return(e=D.get(this))[t].apply(e,arguments)}});const P=void 0!==l.ResizeObserver?l.ResizeObserver:R;var E=n(164);function z(){return z=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)({}).hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},z.apply(null,arguments)}function j(t,e,n){return(e=function(t){var e=function(t){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:e+""}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}const C="react-grid-layout";function M(t){var e;return e=class extends r.Component{constructor(){super(...arguments),j(this,"state",{width:1280}),j(this,"elementRef",r.createRef()),j(this,"mounted",!1),j(this,"resizeObserver",void 0)}componentDidMount(){this.mounted=!0,this.resizeObserver=new P(t=>{if(this.elementRef.current instanceof HTMLElement){const e=t[0].contentRect.width;this.setState({width:e})}});const t=this.elementRef.current;t instanceof HTMLElement&&this.resizeObserver.observe(t)}componentWillUnmount(){this.mounted=!1;const t=this.elementRef.current;t instanceof HTMLElement&&this.resizeObserver.unobserve(t),this.resizeObserver.disconnect()}render(){const{measureBeforeMount:e,...n}=this.props;return e&&!this.mounted?r.createElement("div",{className:(0,E.A)(this.props.className,C),style:this.props.style,ref:this.elementRef}):r.createElement(t,z({innerRef:this.elementRef},n,this.state))}},j(e,"defaultProps",{measureBeforeMount:!1}),j(e,"propTypes",{measureBeforeMount:i().bool}),e}},322:(t,e,n)=>{"use strict";function r(t){var e,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t))for(e=0;e<t.length;e++)t[e]&&(n=r(t[e]))&&(o&&(o+=" "),o+=n);else for(e in t)t[e]&&(o&&(o+=" "),o+=e);return o}function o(){for(var t,e,n=0,o="";n<arguments.length;)(t=arguments[n++])&&(e=r(t))&&(o&&(o+=" "),o+=e);return o}n.r(e),n.d(e,{clsx:()=>o,default:()=>i});const i=o},326:(t,e,n)=>{"use strict";n.d(e,{default:()=>O});var r=n(12),o=n.n(r),i=n(548),a=n(164),s=n(335),l=n(417),u=n(33),c=n(556),p=n.n(c),d=n(794),h=n(36);const f=p().arrayOf(p().oneOf(["s","w","e","n","sw","nw","se","ne"])),g=p().oneOfType([p().node,p().func]),m={className:p().string,style:p().object,width:p().number,autoSize:p().bool,cols:p().number,draggableCancel:p().string,draggableHandle:p().string,verticalCompact:function(t){t.verticalCompact},compactType:p().oneOf(["vertical","horizontal"]),layout:function(t){var e=t.layout;void 0!==e&&n(335).validateLayout(e,"layout")},margin:p().arrayOf(p().number),containerPadding:p().arrayOf(p().number),rowHeight:p().number,maxRows:p().number,isBounded:p().bool,isDraggable:p().bool,isResizable:p().bool,allowOverlap:p().bool,preventCollision:p().bool,useCSSTransforms:p().bool,transformScale:p().number,isDroppable:p().bool,resizeHandles:f,resizeHandle:g,onLayoutChange:p().func,onDragStart:p().func,onDrag:p().func,onDragStop:p().func,onResizeStart:p().func,onResize:p().func,onResizeStop:p().func,onDrop:p().func,droppingItem:p().shape({i:p().string.isRequired,w:p().number.isRequired,h:p().number.isRequired}),children:function(t,e){const n=t[e],r={};o().Children.forEach(n,function(t){if(null!=t?.key){if(r[t.key])throw new Error('Duplicate child key "'+t.key+'" found! This will cause problems in ReactGridLayout.');r[t.key]=!0}})},innerRef:p().any};function y(t,e,n){return(e=function(t){var e=function(t){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:e+""}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}class b extends o().Component{constructor(){super(...arguments),y(this,"state",{resizing:null,dragging:null,className:""}),y(this,"elementRef",o().createRef()),y(this,"onDragStart",(t,e)=>{let{node:n}=e;const{onDragStart:r,transformScale:o}=this.props;if(!r)return;const i={top:0,left:0},{offsetParent:a}=n;if(!a)return;const s=a.getBoundingClientRect(),u=n.getBoundingClientRect(),c=u.left/o,p=s.left/o,d=u.top/o,h=s.top/o;i.left=c-p+a.scrollLeft,i.top=d-h+a.scrollTop,this.setState({dragging:i});const{x:f,y:g}=(0,l.calcXY)(this.getPositionParams(),i.top,i.left,this.props.w,this.props.h);return r.call(this,this.props.i,f,g,{e:t,node:n,newPosition:i})}),y(this,"onDrag",(t,e,n)=>{let{node:r,deltaX:o,deltaY:i}=e;const{onDrag:a}=this.props;if(!a)return;if(!this.state.dragging)throw new Error("onDrag called before onDragStart.");let s=this.state.dragging.top+i,c=this.state.dragging.left+o;const{isBounded:p,i:d,w:h,h:f,containerWidth:g}=this.props,m=this.getPositionParams();if(p){const{offsetParent:t}=r;if(t){const{margin:e,rowHeight:n}=this.props,r=t.clientHeight-(0,l.calcGridItemWHPx)(f,n,e[1]);s=(0,l.clamp)(s,0,r);const o=(0,l.calcGridColWidth)(m),i=g-(0,l.calcGridItemWHPx)(h,o,e[0]);c=(0,l.clamp)(c,0,i)}}const y={top:s,left:c};n?this.setState({dragging:y}):(0,u.flushSync)(()=>{this.setState({dragging:y})});const{x:b,y:v}=(0,l.calcXY)(m,s,c,h,f);return a.call(this,d,b,v,{e:t,node:r,newPosition:y})}),y(this,"onDragStop",(t,e)=>{let{node:n}=e;const{onDragStop:r}=this.props;if(!r)return;if(!this.state.dragging)throw new Error("onDragEnd called before onDragStart.");const{w:o,h:i,i:a}=this.props,{left:s,top:u}=this.state.dragging,c={top:u,left:s};this.setState({dragging:null});const{x:p,y:d}=(0,l.calcXY)(this.getPositionParams(),u,s,o,i);return r.call(this,a,p,d,{e:t,node:n,newPosition:c})}),y(this,"onResizeStop",(t,e,n)=>this.onResizeHandler(t,e,n,"onResizeStop")),y(this,"onResizeStart",(t,e,n)=>this.onResizeHandler(t,e,n,"onResizeStart")),y(this,"onResize",(t,e,n)=>this.onResizeHandler(t,e,n,"onResize"))}shouldComponentUpdate(t,e){if(this.props.children!==t.children)return!0;if(this.props.droppingPosition!==t.droppingPosition)return!0;const n=(0,l.calcGridItemPosition)(this.getPositionParams(this.props),this.props.x,this.props.y,this.props.w,this.props.h,this.state),r=(0,l.calcGridItemPosition)(this.getPositionParams(t),t.x,t.y,t.w,t.h,e);return!(0,s.fastPositionEqual)(n,r)||this.props.useCSSTransforms!==t.useCSSTransforms}componentDidMount(){this.moveDroppingItem({})}componentDidUpdate(t){this.moveDroppingItem(t)}moveDroppingItem(t){const{droppingPosition:e}=this.props;if(!e)return;const n=this.elementRef.current;if(!n)return;const r=t.droppingPosition||{left:0,top:0},{dragging:o}=this.state,i=o&&e.left!==r.left||e.top!==r.top;if(o){if(i){const t=e.left-o.left,r=e.top-o.top;this.onDrag(e.e,{node:n,deltaX:t,deltaY:r},!0)}}else this.onDragStart(e.e,{node:n,deltaX:e.left,deltaY:e.top})}getPositionParams(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props;return{cols:t.cols,containerPadding:t.containerPadding,containerWidth:t.containerWidth,margin:t.margin,maxRows:t.maxRows,rowHeight:t.rowHeight}}createStyle(t){const{usePercentages:e,containerWidth:n,useCSSTransforms:r}=this.props;let o;return r?o=(0,s.setTransform)(t):(o=(0,s.setTopLeft)(t),e&&(o.left=(0,s.perc)(t.left/n),o.width=(0,s.perc)(t.width/n))),o}mixinDraggable(t,e){return o().createElement(d.DraggableCore,{disabled:!e,onStart:this.onDragStart,onDrag:this.onDrag,onStop:this.onDragStop,handle:this.props.handle,cancel:".react-resizable-handle"+(this.props.cancel?","+this.props.cancel:""),scale:this.props.transformScale,nodeRef:this.elementRef},t)}curryResizeHandler(t,e){return(n,r)=>e(n,r,t)}mixinResizable(t,e,n){const{cols:r,minW:i,minH:a,maxW:s,maxH:u,transformScale:c,resizeHandles:p,resizeHandle:d}=this.props,f=this.getPositionParams(),g=(0,l.calcGridItemPosition)(f,0,0,r,0).width,m=(0,l.calcGridItemPosition)(f,0,0,i,a),y=(0,l.calcGridItemPosition)(f,0,0,s,u),b=[m.width,m.height],v=[Math.min(y.width,g),Math.min(y.height,1/0)];return o().createElement(h.Resizable,{draggableOpts:{disabled:!n},className:n?void 0:"react-resizable-hide",width:e.width,height:e.height,minConstraints:b,maxConstraints:v,onResizeStop:this.curryResizeHandler(e,this.onResizeStop),onResizeStart:this.curryResizeHandler(e,this.onResizeStart),onResize:this.curryResizeHandler(e,this.onResize),transformScale:c,resizeHandles:p,handle:d},t)}onResizeHandler(t,e,n,r){let{node:o,size:i,handle:a}=e;const c=this.props[r];if(!c)return;const{x:p,y:d,i:h,maxH:f,minH:g,containerWidth:m}=this.props,{minW:y,maxW:b}=this.props;let v=i;o&&(v=(0,s.resizeItemInDirection)(a,n,i,m),(0,u.flushSync)(()=>{this.setState({resizing:"onResizeStop"===r?null:v})}));let{w,h:S}=(0,l.calcWH)(this.getPositionParams(),v.width,v.height,p,d,a);w=(0,l.clamp)(w,Math.max(y,1),b),S=(0,l.clamp)(S,g,f),c.call(this,h,w,S,{e:t,node:o,size:v,handle:a})}render(){const{x:t,y:e,w:n,h:r,isDraggable:i,isResizable:s,droppingPosition:u,useCSSTransforms:c}=this.props,p=(0,l.calcGridItemPosition)(this.getPositionParams(),t,e,n,r,this.state),d=o().Children.only(this.props.children);let h=o().cloneElement(d,{ref:this.elementRef,className:(0,a.A)("react-grid-item",d.props.className,this.props.className,{static:this.props.static,resizing:Boolean(this.state.resizing),"react-draggable":i,"react-draggable-dragging":Boolean(this.state.dragging),dropping:Boolean(u),cssTransforms:c}),style:{...this.props.style,...d.props.style,...this.createStyle(p)}});return h=this.mixinResizable(h,p,s),h=this.mixinDraggable(h,i),h}}function v(t,e,n){return(e=function(t){var e=function(t){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:e+""}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}y(b,"propTypes",{children:p().element,cols:p().number.isRequired,containerWidth:p().number.isRequired,rowHeight:p().number.isRequired,margin:p().array.isRequired,maxRows:p().number.isRequired,containerPadding:p().array.isRequired,x:p().number.isRequired,y:p().number.isRequired,w:p().number.isRequired,h:p().number.isRequired,minW:function(t,e){const n=t[e];return"number"!=typeof n?new Error("minWidth not Number"):n>t.w||n>t.maxW?new Error("minWidth larger than item width/maxWidth"):void 0},maxW:function(t,e){const n=t[e];return"number"!=typeof n?new Error("maxWidth not Number"):n<t.w||n<t.minW?new Error("maxWidth smaller than item width/minWidth"):void 0},minH:function(t,e){const n=t[e];return"number"!=typeof n?new Error("minHeight not Number"):n>t.h||n>t.maxH?new Error("minHeight larger than item height/maxHeight"):void 0},maxH:function(t,e){const n=t[e];return"number"!=typeof n?new Error("maxHeight not Number"):n<t.h||n<t.minH?new Error("maxHeight smaller than item height/minHeight"):void 0},i:p().string.isRequired,resizeHandles:f,resizeHandle:g,onDragStop:p().func,onDragStart:p().func,onDrag:p().func,onResizeStop:p().func,onResizeStart:p().func,onResize:p().func,isDraggable:p().bool.isRequired,isResizable:p().bool.isRequired,isBounded:p().bool.isRequired,static:p().bool,useCSSTransforms:p().bool.isRequired,transformScale:p().number,className:p().string,handle:p().string,cancel:p().string,droppingPosition:p().shape({e:p().object.isRequired,left:p().number.isRequired,top:p().number.isRequired})}),y(b,"defaultProps",{className:"",cancel:"",handle:"",minH:1,minW:1,maxH:1/0,maxW:1/0,transformScale:1});const w="react-grid-layout";let S=!1;try{S=/firefox/i.test(navigator.userAgent)}catch(t){}class O extends r.Component{constructor(){super(...arguments),v(this,"state",{activeDrag:null,layout:(0,s.synchronizeLayoutWithChildren)(this.props.layout,this.props.children,this.props.cols,(0,s.compactType)(this.props),this.props.allowOverlap),mounted:!1,oldDragItem:null,oldLayout:null,oldResizeItem:null,resizing:!1,droppingDOMNode:null,children:[]}),v(this,"dragEnterCounter",0),v(this,"onDragStart",(t,e,n,r)=>{let{e:o,node:i}=r;const{layout:a}=this.state,l=(0,s.getLayoutItem)(a,t);if(!l)return;const u={w:l.w,h:l.h,x:l.x,y:l.y,placeholder:!0,i:t};return this.setState({oldDragItem:(0,s.cloneLayoutItem)(l),oldLayout:a,activeDrag:u}),this.props.onDragStart(a,l,l,null,o,i)}),v(this,"onDrag",(t,e,n,r)=>{let{e:o,node:i}=r;const{oldDragItem:a}=this.state;let{layout:l}=this.state;const{cols:u,allowOverlap:c,preventCollision:p}=this.props,d=(0,s.getLayoutItem)(l,t);if(!d)return;const h={w:d.w,h:d.h,x:d.x,y:d.y,placeholder:!0,i:t};l=(0,s.moveElement)(l,d,e,n,!0,p,(0,s.compactType)(this.props),u,c),this.props.onDrag(l,a,d,h,o,i),this.setState({layout:c?l:(0,s.compact)(l,(0,s.compactType)(this.props),u),activeDrag:h})}),v(this,"onDragStop",(t,e,n,r)=>{let{e:o,node:i}=r;if(!this.state.activeDrag)return;const{oldDragItem:a}=this.state;let{layout:l}=this.state;const{cols:u,preventCollision:c,allowOverlap:p}=this.props,d=(0,s.getLayoutItem)(l,t);if(!d)return;l=(0,s.moveElement)(l,d,e,n,!0,c,(0,s.compactType)(this.props),u,p);const h=p?l:(0,s.compact)(l,(0,s.compactType)(this.props),u);this.props.onDragStop(h,a,d,null,o,i);const{oldLayout:f}=this.state;this.setState({activeDrag:null,layout:h,oldDragItem:null,oldLayout:null}),this.onLayoutMaybeChanged(h,f)}),v(this,"onResizeStart",(t,e,n,r)=>{let{e:o,node:i}=r;const{layout:a}=this.state,l=(0,s.getLayoutItem)(a,t);l&&(this.setState({oldResizeItem:(0,s.cloneLayoutItem)(l),oldLayout:this.state.layout,resizing:!0}),this.props.onResizeStart(a,l,l,null,o,i))}),v(this,"onResize",(t,e,n,r)=>{let{e:o,node:i,size:a,handle:l}=r;const{oldResizeItem:u}=this.state,{layout:c}=this.state,{cols:p,preventCollision:d,allowOverlap:h}=this.props;let f,g,m,y=!1;const[b,v]=(0,s.withLayoutItem)(c,t,t=>{let r;return g=t.x,m=t.y,-1!==["sw","w","nw","n","ne"].indexOf(l)&&(-1!==["sw","nw","w"].indexOf(l)&&(g=t.x+(t.w-e),e=t.x!==g&&g<0?t.w:e,g=g<0?0:g),-1!==["ne","n","nw"].indexOf(l)&&(m=t.y+(t.h-n),n=t.y!==m&&m<0?t.h:n,m=m<0?0:m),y=!0),d&&!h&&(r=(0,s.getAllCollisions)(c,{...t,w:e,h:n,x:g,y:m}).filter(e=>e.i!==t.i).length>0,r&&(m=t.y,n=t.h,g=t.x,e=t.w,y=!1)),t.w=e,t.h=n,t});if(!v)return;if(f=b,y){const t=!0;f=(0,s.moveElement)(b,v,g,m,t,this.props.preventCollision,(0,s.compactType)(this.props),p,h)}const w={w:v.w,h:v.h,x:v.x,y:v.y,static:!0,i:t};this.props.onResize(f,u,v,w,o,i),this.setState({layout:h?f:(0,s.compact)(f,(0,s.compactType)(this.props),p),activeDrag:w})}),v(this,"onResizeStop",(t,e,n,r)=>{let{e:o,node:i}=r;const{layout:a,oldResizeItem:l}=this.state,{cols:u,allowOverlap:c}=this.props,p=(0,s.getLayoutItem)(a,t),d=c?a:(0,s.compact)(a,(0,s.compactType)(this.props),u);this.props.onResizeStop(d,l,p,null,o,i);const{oldLayout:h}=this.state;this.setState({activeDrag:null,layout:d,oldResizeItem:null,oldLayout:null,resizing:!1}),this.onLayoutMaybeChanged(d,h)}),v(this,"onDragOver",t=>{if(t.preventDefault(),t.stopPropagation(),S&&!t.nativeEvent.target?.classList.contains(w))return!1;const{droppingItem:e,onDropDragOver:n,margin:o,cols:i,rowHeight:a,maxRows:s,width:u,containerPadding:c,transformScale:p}=this.props,d=n?.(t);if(!1===d)return this.state.droppingDOMNode&&this.removeDroppingPlaceholder(),!1;const h={...e,...d},{layout:f}=this.state,g=t.currentTarget.getBoundingClientRect(),m=t.clientX-g.left,y=t.clientY-g.top,b={left:m/p,top:y/p,e:t};if(this.state.droppingDOMNode){if(this.state.droppingPosition){const{left:t,top:e}=this.state.droppingPosition;(t!=m||e!=y)&&this.setState({droppingPosition:b})}}else{const t={cols:i,margin:o,maxRows:s,rowHeight:a,containerWidth:u,containerPadding:c||o},e=(0,l.calcXY)(t,y,m,h.w,h.h);this.setState({droppingDOMNode:r.createElement("div",{key:h.i}),droppingPosition:b,layout:[...f,{...h,x:e.x,y:e.y,static:!1,isDraggable:!0}]})}}),v(this,"removeDroppingPlaceholder",()=>{const{droppingItem:t,cols:e}=this.props,{layout:n}=this.state,r=(0,s.compact)(n.filter(e=>e.i!==t.i),(0,s.compactType)(this.props),e,this.props.allowOverlap);this.setState({layout:r,droppingDOMNode:null,activeDrag:null,droppingPosition:void 0})}),v(this,"onDragLeave",t=>{t.preventDefault(),t.stopPropagation(),this.dragEnterCounter--,0===this.dragEnterCounter&&this.removeDroppingPlaceholder()}),v(this,"onDragEnter",t=>{t.preventDefault(),t.stopPropagation(),this.dragEnterCounter++}),v(this,"onDrop",t=>{t.preventDefault(),t.stopPropagation();const{droppingItem:e}=this.props,{layout:n}=this.state,r=n.find(t=>t.i===e.i);this.dragEnterCounter=0,this.removeDroppingPlaceholder(),this.props.onDrop(n,r,t)})}componentDidMount(){this.setState({mounted:!0}),this.onLayoutMaybeChanged(this.state.layout,this.props.layout)}static getDerivedStateFromProps(t,e){let n;return e.activeDrag?null:((0,i.deepEqual)(t.layout,e.propsLayout)&&t.compactType===e.compactType?(0,s.childrenEqual)(t.children,e.children)||(n=e.layout):n=t.layout,n?{layout:(0,s.synchronizeLayoutWithChildren)(n,t.children,t.cols,(0,s.compactType)(t),t.allowOverlap),compactType:t.compactType,children:t.children,propsLayout:t.layout}:null)}shouldComponentUpdate(t,e){return this.props.children!==t.children||!(0,s.fastRGLPropsEqual)(this.props,t,i.deepEqual)||this.state.activeDrag!==e.activeDrag||this.state.mounted!==e.mounted||this.state.droppingPosition!==e.droppingPosition}componentDidUpdate(t,e){if(!this.state.activeDrag){const t=this.state.layout,n=e.layout;this.onLayoutMaybeChanged(t,n)}}containerHeight(){if(!this.props.autoSize)return;const t=(0,s.bottom)(this.state.layout),e=this.props.containerPadding?this.props.containerPadding[1]:this.props.margin[1];return t*this.props.rowHeight+(t-1)*this.props.margin[1]+2*e+"px"}onLayoutMaybeChanged(t,e){e||(e=this.state.layout),(0,i.deepEqual)(e,t)||this.props.onLayoutChange(t)}placeholder(){const{activeDrag:t}=this.state;if(!t)return null;const{width:e,cols:n,margin:o,containerPadding:i,rowHeight:a,maxRows:s,useCSSTransforms:l,transformScale:u}=this.props;return r.createElement(b,{w:t.w,h:t.h,x:t.x,y:t.y,i:t.i,className:"react-grid-placeholder "+(this.state.resizing?"placeholder-resizing":""),containerWidth:e,cols:n,margin:o,containerPadding:i||o,maxRows:s,rowHeight:a,isDraggable:!1,isResizable:!1,isBounded:!1,useCSSTransforms:l,transformScale:u},r.createElement("div",null))}processGridItem(t,e){if(!t||!t.key)return;const n=(0,s.getLayoutItem)(this.state.layout,String(t.key));if(!n)return null;const{width:o,cols:i,margin:a,containerPadding:l,rowHeight:u,maxRows:c,isDraggable:p,isResizable:d,isBounded:h,useCSSTransforms:f,transformScale:g,draggableCancel:m,draggableHandle:y,resizeHandles:v,resizeHandle:w}=this.props,{mounted:S,droppingPosition:O}=this.state,x="boolean"==typeof n.isDraggable?n.isDraggable:!n.static&&p,D="boolean"==typeof n.isResizable?n.isResizable:!n.static&&d,R=n.resizeHandles||v,P=x&&h&&!1!==n.isBounded;return r.createElement(b,{containerWidth:o,cols:i,margin:a,containerPadding:l||a,maxRows:c,rowHeight:u,cancel:m,handle:y,onDragStop:this.onDragStop,onDragStart:this.onDragStart,onDrag:this.onDrag,onResizeStart:this.onResizeStart,onResize:this.onResize,onResizeStop:this.onResizeStop,isDraggable:x,isResizable:D,isBounded:P,useCSSTransforms:f&&S,usePercentages:!S,transformScale:g,w:n.w,h:n.h,x:n.x,y:n.y,i:n.i,minH:n.minH,minW:n.minW,maxH:n.maxH,maxW:n.maxW,static:n.static,droppingPosition:e?O:void 0,resizeHandles:R,resizeHandle:w},t)}render(){const{className:t,style:e,isDroppable:n,innerRef:o}=this.props,i=(0,a.A)(w,t),l={height:this.containerHeight(),...e};return r.createElement("div",{ref:o,className:i,style:l,onDrop:n?this.onDrop:s.noop,onDragLeave:n?this.onDragLeave:s.noop,onDragEnter:n?this.onDragEnter:s.noop,onDragOver:n?this.onDragOver:s.noop},r.Children.map(this.props.children,t=>this.processGridItem(t)),n&&this.state.droppingDOMNode&&this.processGridItem(this.state.droppingDOMNode,!0),this.placeholder())}}v(O,"displayName","ReactGridLayout"),v(O,"propTypes",m),v(O,"defaultProps",{autoSize:!0,cols:12,className:"",style:{},draggableHandle:"",draggableCancel:"",containerPadding:null,rowHeight:150,maxRows:1/0,layout:[],margin:[10,10],isBounded:!1,isDraggable:!0,isResizable:!0,allowOverlap:!1,isDroppable:!1,useCSSTransforms:!0,transformScale:1,verticalCompact:!0,compactType:"vertical",preventCollision:!1,droppingItem:{i:"__dropping-elem__",h:1,w:1},resizeHandles:["se"],onLayoutChange:s.noop,onDragStart:s.noop,onDrag:s.noop,onDragStop:s.noop,onResizeStart:s.noop,onResize:s.noop,onResizeStop:s.noop,onDrop:s.noop,onDropDragOver:s.noop})},329:(t,e,n)=>{"use strict";e.__esModule=!0,e.resizableProps=void 0;var r,o=(r=n(556))&&r.__esModule?r:{default:r};n(794);var i={axis:o.default.oneOf(["both","x","y","none"]),className:o.default.string,children:o.default.element.isRequired,draggableOpts:o.default.shape({allowAnyClick:o.default.bool,cancel:o.default.string,children:o.default.node,disabled:o.default.bool,enableUserSelectHack:o.default.bool,offsetParent:o.default.node,grid:o.default.arrayOf(o.default.number),handle:o.default.string,nodeRef:o.default.object,onStart:o.default.func,onDrag:o.default.func,onStop:o.default.func,onMouseDown:o.default.func,scale:o.default.number}),height:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var r,i=e[0];return"both"===i.axis||"y"===i.axis?(r=o.default.number).isRequired.apply(r,e):o.default.number.apply(o.default,e)},handle:o.default.oneOfType([o.default.node,o.default.func]),handleSize:o.default.arrayOf(o.default.number),lockAspectRatio:o.default.bool,maxConstraints:o.default.arrayOf(o.default.number),minConstraints:o.default.arrayOf(o.default.number),onResizeStop:o.default.func,onResizeStart:o.default.func,onResize:o.default.func,resizeHandles:o.default.arrayOf(o.default.oneOf(["s","w","e","n","sw","nw","se","ne"])),transformScale:o.default.number,width:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var r,i=e[0];return"both"===i.axis||"x"===i.axis?(r=o.default.number).isRequired.apply(r,e):o.default.number.apply(o.default,e)}};e.resizableProps=i},335:(t,e,n)=>{"use strict";n.r(e),n.d(e,{bottom:()=>l,childrenEqual:()=>h,cloneLayout:()=>u,cloneLayoutItem:()=>d,collides:()=>m,compact:()=>y,compactItem:()=>w,compactType:()=>$,correctBounds:()=>S,fastPositionEqual:()=>g,fastRGLPropsEqual:()=>f,getAllCollisions:()=>D,getFirstCollision:()=>x,getLayoutItem:()=>O,getStatics:()=>R,modifyLayout:()=>c,moveElement:()=>P,moveElementAwayFromCollision:()=>E,noop:()=>F,perc:()=>z,resizeItemInDirection:()=>W,setTopLeft:()=>I,setTransform:()=>q,sortLayoutItems:()=>A,sortLayoutItemsByColRow:()=>G,sortLayoutItemsByRowCol:()=>B,synchronizeLayoutWithChildren:()=>X,validateLayout:()=>Y,withLayoutItem:()=>p});var r=n(548),o=n(12),i=n.n(o);const a=!0,s=!1;function l(t){let e,n=0;for(let r=0,o=t.length;r<o;r++)e=t[r].y+t[r].h,e>n&&(n=e);return n}function u(t){const e=Array(t.length);for(let n=0,r=t.length;n<r;n++)e[n]=d(t[n]);return e}function c(t,e){const n=Array(t.length);for(let r=0,o=t.length;r<o;r++)e.i===t[r].i?n[r]=e:n[r]=t[r];return n}function p(t,e,n){let r=O(t,e);return r?(r=n(d(r)),[t=c(t,r),r]):[t,null]}function d(t){return{w:t.w,h:t.h,x:t.x,y:t.y,i:t.i,minW:t.minW,maxW:t.maxW,minH:t.minH,maxH:t.maxH,moved:Boolean(t.moved),static:Boolean(t.static),isDraggable:t.isDraggable,isResizable:t.isResizable,resizeHandles:t.resizeHandles,isBounded:t.isBounded}}function h(t,e){return(0,r.deepEqual)(i().Children.map(t,t=>t?.key),i().Children.map(e,t=>t?.key))&&(0,r.deepEqual)(i().Children.map(t,t=>t?.props["data-grid"]),i().Children.map(e,t=>t?.props["data-grid"]))}const f=n(407);function g(t,e){return t.left===e.left&&t.top===e.top&&t.width===e.width&&t.height===e.height}function m(t,e){return!(t.i===e.i||t.x+t.w<=e.x||t.x>=e.x+e.w||t.y+t.h<=e.y||t.y>=e.y+e.h)}function y(t,e,n,r){const o=R(t),i=A(t,e),a=Array(t.length);for(let s=0,l=i.length;s<l;s++){let l=d(i[s]);l.static||(l=w(o,l,e,n,i,r),o.push(l)),a[t.indexOf(i[s])]=l,l.moved=!1}return a}const b={x:"w",y:"h"};function v(t,e,n,r){const o=b[r];e[r]+=1;for(let i=t.map(t=>t.i).indexOf(e.i)+1;i<t.length;i++){const a=t[i];if(!a.static){if(a.y>e.y+e.h)break;m(e,a)&&v(t,a,n+e[o],r)}}e[r]=n}function w(t,e,n,r,o,i){const a="horizontal"===n;if("vertical"===n)for(e.y=Math.min(l(t),e.y);e.y>0&&!x(t,e);)e.y--;else if(a)for(;e.x>0&&!x(t,e);)e.x--;let s;for(;(s=x(t,e))&&(null!==n||!i);)if(a?v(o,e,s.x+s.w,"x"):v(o,e,s.y+s.h,"y"),a&&e.x+e.w>r)for(e.x=r-e.w,e.y++;e.x>0&&!x(t,e);)e.x--;return e.y=Math.max(e.y,0),e.x=Math.max(e.x,0),e}function S(t,e){const n=R(t);for(let r=0,o=t.length;r<o;r++){const o=t[r];if(o.x+o.w>e.cols&&(o.x=e.cols-o.w),o.x<0&&(o.x=0,o.w=e.cols),o.static)for(;x(n,o);)o.y++;else n.push(o)}return t}function O(t,e){for(let n=0,r=t.length;n<r;n++)if(t[n].i===e)return t[n]}function x(t,e){for(let n=0,r=t.length;n<r;n++)if(m(t[n],e))return t[n]}function D(t,e){return t.filter(t=>m(t,e))}function R(t){return t.filter(t=>t.static)}function P(t,e,n,r,o,i,a,s,l){if(e.static&&!0!==e.isDraggable)return t;if(e.y===r&&e.x===n)return t;U(`Moving element ${e.i} to [${String(n)},${String(r)}] from [${e.x},${e.y}]`);const c=e.x,p=e.y;"number"==typeof n&&(e.x=n),"number"==typeof r&&(e.y=r),e.moved=!0;let d=A(t,a);("vertical"===a&&"number"==typeof r?p>=r:"horizontal"===a&&"number"==typeof n&&c>=n)&&(d=d.reverse());const h=D(d,e),f=h.length>0;if(f&&l)return u(t);if(f&&i)return U(`Collision prevented on ${e.i}, reverting.`),e.x=c,e.y=p,e.moved=!1,t;for(let n=0,r=h.length;n<r;n++){const r=h[n];U(`Resolving collision between ${e.i} at [${e.x},${e.y}] and ${r.i} at [${r.x},${r.y}]`),r.moved||(t=r.static?E(t,r,e,o,a,s):E(t,e,r,o,a,s))}return t}function E(t,e,n,r,o,i){const a="horizontal"===o,s="vertical"===o,l=e.static;if(r){r=!1;const u={x:a?Math.max(e.x-n.w,0):n.x,y:s?Math.max(e.y-n.h,0):n.y,w:n.w,h:n.h,i:"-1"},c=x(t,u),p=c&&c.y+c.h>e.y,d=c&&e.x+e.w>c.x;if(!c)return U(`Doing reverse collision on ${n.i} up to [${u.x},${u.y}].`),P(t,n,a?u.x:void 0,s?u.y:void 0,r,l,o,i);if(p&&s)return P(t,n,void 0,e.y+1,r,l,o,i);if(p&&null==o)return e.y=n.y,n.y=n.y+n.h,t;if(d&&a)return P(t,e,n.x,void 0,r,l,o,i)}const u=a?n.x+1:void 0,c=s?n.y+1:void 0;return null==u&&null==c?t:P(t,n,a?n.x+1:void 0,s?n.y+1:void 0,r,l,o,i)}function z(t){return 100*t+"%"}const j=(t,e,n,r)=>t+n>r?e:n,C=(t,e,n)=>t<0?e:n,M=t=>Math.max(0,t),_=t=>Math.max(0,t),T=(t,e,n)=>{let{left:r,height:o,width:i}=e;const a=t.top-(o-t.height);return{left:r,width:i,height:C(a,t.height,o),top:_(a)}},k=(t,e,n)=>{let{top:r,left:o,height:i,width:a}=e;return{top:r,height:i,width:j(t.left,t.width,a,n),left:M(o)}},H=(t,e,n)=>{let{top:r,height:o,width:i}=e;const a=t.left-(i-t.width);return{height:o,width:a<0?t.width:j(t.left,t.width,i,n),top:_(r),left:M(a)}},L=(t,e,n)=>{let{top:r,left:o,height:i,width:a}=e;return{width:a,left:o,height:C(r,t.height,i),top:_(r)}},N={n:T,ne:function(){return T(arguments.length<=0?void 0:arguments[0],k(...arguments))},e:k,se:function(){return L(arguments.length<=0?void 0:arguments[0],k(...arguments))},s:L,sw:function(){return L(arguments.length<=0?void 0:arguments[0],H(...arguments))},w:H,nw:function(){return T(arguments.length<=0?void 0:arguments[0],H(...arguments))}};function W(t,e,n,r){const o=N[t];return o?o(e,{...e,...n},r):n}function q(t){let{top:e,left:n,width:r,height:o}=t;const i=`translate(${n}px,${e}px)`;return{transform:i,WebkitTransform:i,MozTransform:i,msTransform:i,OTransform:i,width:`${r}px`,height:`${o}px`,position:"absolute"}}function I(t){let{top:e,left:n,width:r,height:o}=t;return{top:`${e}px`,left:`${n}px`,width:`${r}px`,height:`${o}px`,position:"absolute"}}function A(t,e){return"horizontal"===e?G(t):"vertical"===e?B(t):t}function B(t){return t.slice(0).sort(function(t,e){return t.y>e.y||t.y===e.y&&t.x>e.x?1:t.y===e.y&&t.x===e.x?0:-1})}function G(t){return t.slice(0).sort(function(t,e){return t.x>e.x||t.x===e.x&&t.y>e.y?1:-1})}function X(t,e,n,r,o){t=t||[];const s=[];i().Children.forEach(e,e=>{if(null==e?.key)return;const n=O(t,String(e.key)),r=e.props["data-grid"];n&&null==r?s.push(d(n)):r?(a||Y([r],"ReactGridLayout.children"),s.push(d({...r,i:e.key}))):s.push(d({w:1,h:1,x:0,y:l(s),i:String(e.key)}))});const u=S(s,{cols:n});return o?u:y(u,r,n)}function Y(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Layout";const n=["x","y","w","h"];if(!Array.isArray(t))throw new Error(e+" must be an array!");for(let r=0,o=t.length;r<o;r++){const o=t[r];for(let t=0;t<n.length;t++){const i=n[t],a=o[i];if("number"!=typeof a||Number.isNaN(a))throw new Error(`ReactGridLayout: ${e}[${r}].${i} must be a number! Received: ${a} (${typeof a})`)}if(void 0!==o.i&&"string"!=typeof o.i)throw new Error(`ReactGridLayout: ${e}[${r}].i must be a string! Received: ${o.i} (${typeof o.i})`)}}function $(t){const{verticalCompact:e,compactType:n}=t||{};return!1===e?null:n}function U(){s&&console.log(...arguments)}const F=()=>{}},407:t=>{t.exports=function(t,e,n){return t===e||t.className===e.className&&n(t.style,e.style)&&t.width===e.width&&t.autoSize===e.autoSize&&t.cols===e.cols&&t.draggableCancel===e.draggableCancel&&t.draggableHandle===e.draggableHandle&&n(t.verticalCompact,e.verticalCompact)&&n(t.compactType,e.compactType)&&n(t.layout,e.layout)&&n(t.margin,e.margin)&&n(t.containerPadding,e.containerPadding)&&t.rowHeight===e.rowHeight&&t.maxRows===e.maxRows&&t.isBounded===e.isBounded&&t.isDraggable===e.isDraggable&&t.isResizable===e.isResizable&&t.allowOverlap===e.allowOverlap&&t.preventCollision===e.preventCollision&&t.useCSSTransforms===e.useCSSTransforms&&t.transformScale===e.transformScale&&t.isDroppable===e.isDroppable&&n(t.resizeHandles,e.resizeHandles)&&n(t.resizeHandle,e.resizeHandle)&&t.onLayoutChange===e.onLayoutChange&&t.onDragStart===e.onDragStart&&t.onDrag===e.onDrag&&t.onDragStop===e.onDragStop&&t.onResizeStart===e.onResizeStart&&t.onResize===e.onResize&&t.onResizeStop===e.onResizeStop&&t.onDrop===e.onDrop&&n(t.droppingItem,e.droppingItem)&&n(t.innerRef,e.innerRef)}},417:(t,e,n)=>{"use strict";function r(t){const{margin:e,containerPadding:n,containerWidth:r,cols:o}=t;return(r-e[0]*(o-1)-2*n[0])/o}function o(t,e,n){return Number.isFinite(t)?Math.round(e*t+Math.max(0,t-1)*n):t}function i(t,e,n,i,a,s){const{margin:l,containerPadding:u,rowHeight:c}=t,p=r(t),d={};return s&&s.resizing?(d.width=Math.round(s.resizing.width),d.height=Math.round(s.resizing.height)):(d.width=o(i,p,l[0]),d.height=o(a,c,l[1])),s&&s.dragging?(d.top=Math.round(s.dragging.top),d.left=Math.round(s.dragging.left)):s&&s.resizing&&"number"==typeof s.resizing.top&&"number"==typeof s.resizing.left?(d.top=Math.round(s.resizing.top),d.left=Math.round(s.resizing.left)):(d.top=Math.round((c+l[1])*n+u[1]),d.left=Math.round((p+l[0])*e+u[0])),d}function a(t,e,n,o,i){const{margin:a,containerPadding:s,cols:u,rowHeight:c,maxRows:p}=t,d=r(t);let h=Math.round((n-s[0])/(d+a[0])),f=Math.round((e-s[1])/(c+a[1]));return h=l(h,0,u-o),f=l(f,0,p-i),{x:h,y:f}}function s(t,e,n,o,i,a){const{margin:s,maxRows:u,cols:c,rowHeight:p}=t,d=r(t);let h=Math.round((e+s[0])/(d+s[0])),f=Math.round((n+s[1])/(p+s[1])),g=l(h,0,c-o),m=l(f,0,u-i);return-1!==["sw","w","nw"].indexOf(a)&&(g=l(h,0,c)),-1!==["nw","n","ne"].indexOf(a)&&(m=l(f,0,u)),{w:g,h:m}}function l(t,e,n){return Math.max(Math.min(t,n),e)}n.r(e),n.d(e,{calcGridColWidth:()=>r,calcGridItemPosition:()=>i,calcGridItemWHPx:()=>o,calcWH:()=>s,calcXY:()=>a,clamp:()=>l})},514:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.browserPrefixToKey=o,e.browserPrefixToStyle=function(t,e){return e?"-".concat(e.toLowerCase(),"-").concat(t):t},e.default=void 0,e.getPrefix=r;const n=["Moz","Webkit","O","ms"];function r(){var t;let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"transform";if("undefined"==typeof window)return"";const r=null===(t=window.document)||void 0===t||null===(t=t.documentElement)||void 0===t?void 0:t.style;if(!r)return"";if(e in r)return"";for(let t=0;t<n.length;t++)if(o(e,n[t])in r)return n[t];return""}function o(t,e){return e?"".concat(e).concat(function(t){let e="",n=!0;for(let r=0;r<t.length;r++)n?(e+=t[r].toUpperCase(),n=!1):"-"===t[r]?n=!0:e+=t[r];return e}(t)):t}e.default=r()},548:function(t,e){!function(t){"use strict";function e(t){return function(e,n,r,o,i,a,s){return t(e,n,s)}}function n(t){return function(e,n,r,o){if(!e||!n||"object"!=typeof e||"object"!=typeof n)return t(e,n,r,o);var i=o.get(e),a=o.get(n);if(i&&a)return i===n&&a===e;o.set(e,n),o.set(n,e);var s=t(e,n,r,o);return o.delete(e),o.delete(n),s}}function r(t,e){var n={};for(var r in t)n[r]=t[r];for(var r in e)n[r]=e[r];return n}function o(t){return t.constructor===Object||null==t.constructor}function i(t){return"function"==typeof t.then}function a(t,e){return t===e||t!=t&&e!=e}var s=Object.prototype.toString;function l(t){var e=t.areArraysEqual,n=t.areDatesEqual,r=t.areMapsEqual,l=t.areObjectsEqual,u=t.areRegExpsEqual,c=t.areSetsEqual,p=(0,t.createIsNestedEqual)(d);function d(t,d,h){if(t===d)return!0;if(!t||!d||"object"!=typeof t||"object"!=typeof d)return t!=t&&d!=d;if(o(t)&&o(d))return l(t,d,p,h);var f=Array.isArray(t),g=Array.isArray(d);if(f||g)return f===g&&e(t,d,p,h);var m=s.call(t);return m===s.call(d)&&("[object Date]"===m?n(t,d,p,h):"[object RegExp]"===m?u(t,d,p,h):"[object Map]"===m?r(t,d,p,h):"[object Set]"===m?c(t,d,p,h):"[object Object]"===m||"[object Arguments]"===m?!i(t)&&!i(d)&&l(t,d,p,h):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&a(t.valueOf(),d.valueOf()))}return d}function u(t,e,n,r){var o=t.length;if(e.length!==o)return!1;for(;o-- >0;)if(!n(t[o],e[o],o,o,t,e,r))return!1;return!0}var c=n(u);function p(t,e){return a(t.valueOf(),e.valueOf())}function d(t,e,n,r){var o=t.size===e.size;if(!o)return!1;if(!t.size)return!0;var i={},a=0;return t.forEach(function(s,l){if(o){var u=!1,c=0;e.forEach(function(o,p){u||i[c]||!(u=n(l,p,a,c,t,e,r)&&n(s,o,l,p,t,e,r))||(i[c]=!0),c++}),a++,o=u}}),o}var h=n(d),f=Object.prototype.hasOwnProperty;function g(t,e,n,r){var o,i=Object.keys(t),a=i.length;if(Object.keys(e).length!==a)return!1;for(;a-- >0;){if("_owner"===(o=i[a])){var s=!!t.$$typeof,l=!!e.$$typeof;if((s||l)&&s!==l)return!1}if(!f.call(e,o)||!n(t[o],e[o],o,o,t,e,r))return!1}return!0}var m=n(g);function y(t,e){return t.source===e.source&&t.flags===e.flags}function b(t,e,n,r){var o=t.size===e.size;if(!o)return!1;if(!t.size)return!0;var i={};return t.forEach(function(a,s){if(o){var l=!1,u=0;e.forEach(function(o,c){l||i[u]||!(l=n(a,o,s,c,t,e,r))||(i[u]=!0),u++}),o=l}}),o}var v=n(b),w=Object.freeze({areArraysEqual:u,areDatesEqual:p,areMapsEqual:d,areObjectsEqual:g,areRegExpsEqual:y,areSetsEqual:b,createIsNestedEqual:e}),S=Object.freeze({areArraysEqual:c,areDatesEqual:p,areMapsEqual:h,areObjectsEqual:m,areRegExpsEqual:y,areSetsEqual:v,createIsNestedEqual:e}),O=l(w);var x=l(r(w,{createIsNestedEqual:function(){return a}}));var D=l(S);var R=l(r(S,{createIsNestedEqual:function(){return a}}));t.circularDeepEqual=function(t,e){return D(t,e,new WeakMap)},t.circularShallowEqual=function(t,e){return R(t,e,new WeakMap)},t.createCustomCircularEqual=function(t){var e=l(r(S,t(S)));return function(t,n,r){return void 0===r&&(r=new WeakMap),e(t,n,r)}},t.createCustomEqual=function(t){return l(r(w,t(w)))},t.deepEqual=function(t,e){return O(t,e,void 0)},t.sameValueZeroEqual=a,t.shallowEqual=function(t,e){return x(t,e,void 0)},Object.defineProperty(t,"__esModule",{value:!0})}(e)},556:(t,e,n)=>{t.exports=n(694)()},630:(t,e,n)=>{"use strict";e.__esModule=!0,e.default=void 0;var r=function(t,e){if(t&&t.__esModule)return t;if(null===t||"object"!=typeof t&&"function"!=typeof t)return{default:t};var n=l(e);if(n&&n.has(t))return n.get(t);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in t)if("default"!==i&&Object.prototype.hasOwnProperty.call(t,i)){var a=o?Object.getOwnPropertyDescriptor(t,i):null;a&&(a.get||a.set)?Object.defineProperty(r,i,a):r[i]=t[i]}return r.default=t,n&&n.set(t,r),r}(n(12)),o=n(794),i=n(30),a=n(329),s=["children","className","draggableOpts","width","height","handle","handleSize","lockAspectRatio","axis","minConstraints","maxConstraints","onResize","onResizeStop","onResizeStart","resizeHandles","transformScale"];function l(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,n=new WeakMap;return(l=function(t){return t?n:e})(t)}function u(){return u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},u.apply(this,arguments)}function c(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function p(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?c(Object(n),!0).forEach(function(e){d(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function d(t,e,n){return(e=function(t){var e=function(t){if("object"!=typeof t||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function h(t,e){return h=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},h(t,e)}var f=function(t){var e,n;function a(){for(var e,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return(e=t.call.apply(t,[this].concat(r))||this).handleRefs={},e.lastHandleRect=null,e.slack=null,e}n=t,(e=a).prototype=Object.create(n.prototype),e.prototype.constructor=e,h(e,n);var l=a.prototype;return l.componentWillUnmount=function(){this.resetData()},l.resetData=function(){this.lastHandleRect=this.slack=null},l.runConstraints=function(t,e){var n=this.props,r=n.minConstraints,o=n.maxConstraints,i=n.lockAspectRatio;if(!r&&!o&&!i)return[t,e];if(i){var a=this.props.width/this.props.height,s=t-this.props.width,l=e-this.props.height;Math.abs(s)>Math.abs(l*a)?e=t/a:t=e*a}var u=t,c=e,p=this.slack||[0,0],d=p[0],h=p[1];return t+=d,e+=h,r&&(t=Math.max(r[0],t),e=Math.max(r[1],e)),o&&(t=Math.min(o[0],t),e=Math.min(o[1],e)),this.slack=[d+(u-t),h+(c-e)],[t,e]},l.resizeHandler=function(t,e){var n=this;return function(r,o){var i=o.node,a=o.deltaX,s=o.deltaY;"onResizeStart"===t&&n.resetData();var l=("both"===n.props.axis||"x"===n.props.axis)&&"n"!==e&&"s"!==e,u=("both"===n.props.axis||"y"===n.props.axis)&&"e"!==e&&"w"!==e;if(l||u){var c=e[0],p=e[e.length-1],d=i.getBoundingClientRect();null!=n.lastHandleRect&&("w"===p&&(a+=d.left-n.lastHandleRect.left),"n"===c&&(s+=d.top-n.lastHandleRect.top)),n.lastHandleRect=d,"w"===p&&(a=-a),"n"===c&&(s=-s);var h=n.props.width+(l?a/n.props.transformScale:0),f=n.props.height+(u?s/n.props.transformScale:0),g=n.runConstraints(h,f);h=g[0],f=g[1];var m=h!==n.props.width||f!==n.props.height,y="function"==typeof n.props[t]?n.props[t]:null;y&&!("onResize"===t&&!m)&&(null==r.persist||r.persist(),y(r,{node:i,size:{width:h,height:f},handle:e})),"onResizeStop"===t&&n.resetData()}}},l.renderResizeHandle=function(t,e){var n=this.props.handle;if(!n)return r.createElement("span",{className:"react-resizable-handle react-resizable-handle-"+t,ref:e});if("function"==typeof n)return n(t,e);var o=p({ref:e},"string"==typeof n.type?{}:{handleAxis:t});return r.cloneElement(n,o)},l.render=function(){var t=this,e=this.props,n=e.children,a=e.className,l=e.draggableOpts,c=(e.width,e.height,e.handle,e.handleSize,e.lockAspectRatio,e.axis,e.minConstraints,e.maxConstraints,e.onResize,e.onResizeStop,e.onResizeStart,e.resizeHandles),d=(e.transformScale,function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(e,s));return(0,i.cloneElement)(n,p(p({},d),{},{className:(a?a+" ":"")+"react-resizable",children:[].concat(n.props.children,c.map(function(e){var n,i=null!=(n=t.handleRefs[e])?n:t.handleRefs[e]=r.createRef();return r.createElement(o.DraggableCore,u({},l,{nodeRef:i,key:"resizableHandle-"+e,onStop:t.resizeHandler("onResizeStop",e),onStart:t.resizeHandler("onResizeStart",e),onDrag:t.resizeHandler("onResize",e)}),t.renderResizeHandle(e,i))}))}))},a}(r.Component);e.default=f,f.propTypes=a.resizableProps,f.defaultProps={axis:"both",handleSize:[20,20],lockAspectRatio:!1,minConstraints:[20,20],maxConstraints:[1/0,1/0],resizeHandles:["se"],transformScale:1}},661:(t,e,n)=>{"use strict";e.default=void 0;var r=function(t,e){if(t&&t.__esModule)return t;if(null===t||"object"!=typeof t&&"function"!=typeof t)return{default:t};var n=u(e);if(n&&n.has(t))return n.get(t);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in t)if("default"!==i&&Object.prototype.hasOwnProperty.call(t,i)){var a=o?Object.getOwnPropertyDescriptor(t,i):null;a&&(a.get||a.set)?Object.defineProperty(r,i,a):r[i]=t[i]}return r.default=t,n&&n.set(t,r),r}(n(12)),o=l(n(556)),i=l(n(630)),a=n(329),s=["handle","handleSize","onResize","onResizeStart","onResizeStop","draggableOpts","minConstraints","maxConstraints","lockAspectRatio","axis","width","height","resizeHandles","style","transformScale"];function l(t){return t&&t.__esModule?t:{default:t}}function u(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,n=new WeakMap;return(u=function(t){return t?n:e})(t)}function c(){return c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},c.apply(this,arguments)}function p(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function d(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?p(Object(n),!0).forEach(function(e){h(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function h(t,e,n){return(e=function(t){var e=function(t){if("object"!=typeof t||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function f(t,e){return f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},f(t,e)}var g=function(t){var e,n;function o(){for(var e,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return(e=t.call.apply(t,[this].concat(r))||this).state={width:e.props.width,height:e.props.height,propsWidth:e.props.width,propsHeight:e.props.height},e.onResize=function(t,n){var r=n.size;e.props.onResize?(null==t.persist||t.persist(),e.setState(r,function(){return e.props.onResize&&e.props.onResize(t,n)})):e.setState(r)},e}return n=t,(e=o).prototype=Object.create(n.prototype),e.prototype.constructor=e,f(e,n),o.getDerivedStateFromProps=function(t,e){return e.propsWidth!==t.width||e.propsHeight!==t.height?{width:t.width,height:t.height,propsWidth:t.width,propsHeight:t.height}:null},o.prototype.render=function(){var t=this.props,e=t.handle,n=t.handleSize,o=(t.onResize,t.onResizeStart),a=t.onResizeStop,l=t.draggableOpts,u=t.minConstraints,p=t.maxConstraints,h=t.lockAspectRatio,f=t.axis,g=(t.width,t.height,t.resizeHandles),m=t.style,y=t.transformScale,b=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,s);return r.createElement(i.default,{axis:f,draggableOpts:l,handle:e,handleSize:n,height:this.state.height,lockAspectRatio:h,maxConstraints:p,minConstraints:u,onResizeStart:o,onResize:this.onResize,onResizeStop:a,resizeHandles:g,transformScale:y,width:this.state.width},r.createElement("div",c({},b,{style:d(d({},m),{},{width:this.state.width+"px",height:this.state.height+"px"})})))},o}(r.Component);e.default=g,g.propTypes=d(d({},a.resizableProps),{},{children:o.default.element})},694:(t,e,n)=>{"use strict";var r=n(925);function o(){}function i(){}i.resetWarningCache=o,t.exports=function(){function t(t,e,n,o,i,a){if(a!==r){var s=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function e(){return t}t.isRequired=t;var n={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:i,resetWarningCache:o};return n.PropTypes=n,n}},696:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){}},726:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.canDragX=function(t){return"both"===t.props.axis||"x"===t.props.axis},e.canDragY=function(t){return"both"===t.props.axis||"y"===t.props.axis},e.createCoreData=function(t,e,n){const o=!(0,r.isNum)(t.lastX),a=i(t);return o?{node:a,deltaX:0,deltaY:0,lastX:e,lastY:n,x:e,y:n}:{node:a,deltaX:e-t.lastX,deltaY:n-t.lastY,lastX:t.lastX,lastY:t.lastY,x:e,y:n}},e.createDraggableData=function(t,e){const n=t.props.scale;return{node:e.node,x:t.state.x+e.deltaX/n,y:t.state.y+e.deltaY/n,deltaX:e.deltaX/n,deltaY:e.deltaY/n,lastX:t.state.x,lastY:t.state.y}},e.getBoundPosition=function(t,e,n){if(!t.props.bounds)return[e,n];let{bounds:a}=t.props;a="string"==typeof a?a:function(t){return{left:t.left,top:t.top,right:t.right,bottom:t.bottom}}(a);const s=i(t);if("string"==typeof a){const{ownerDocument:t}=s,e=t.defaultView;let n;if(n="parent"===a?s.parentNode:t.querySelector(a),!(n instanceof e.HTMLElement))throw new Error('Bounds selector "'+a+'" could not find an element.');const i=n,l=e.getComputedStyle(s),u=e.getComputedStyle(i);a={left:-s.offsetLeft+(0,r.int)(u.paddingLeft)+(0,r.int)(l.marginLeft),top:-s.offsetTop+(0,r.int)(u.paddingTop)+(0,r.int)(l.marginTop),right:(0,o.innerWidth)(i)-(0,o.outerWidth)(s)-s.offsetLeft+(0,r.int)(u.paddingRight)-(0,r.int)(l.marginRight),bottom:(0,o.innerHeight)(i)-(0,o.outerHeight)(s)-s.offsetTop+(0,r.int)(u.paddingBottom)-(0,r.int)(l.marginBottom)}}return(0,r.isNum)(a.right)&&(e=Math.min(e,a.right)),(0,r.isNum)(a.bottom)&&(n=Math.min(n,a.bottom)),(0,r.isNum)(a.left)&&(e=Math.max(e,a.left)),(0,r.isNum)(a.top)&&(n=Math.max(n,a.top)),[e,n]},e.getControlPosition=function(t,e,n){const r="number"==typeof e?(0,o.getTouch)(t,e):null;if("number"==typeof e&&!r)return null;const a=i(n),s=n.props.offsetParent||a.offsetParent||a.ownerDocument.body;return(0,o.offsetXYFromParent)(r||t,s,n.props.scale)},e.snapToGrid=function(t,e,n){return[Math.round(e/t[0])*t[0],Math.round(n/t[1])*t[1]]};var r=n(56),o=n(89);function i(t){const e=t.findDOMNode();if(!e)throw new Error("<DraggableCore>: Unmounted during event!");return e}},794:(t,e,n)=>{"use strict";const{default:r,DraggableCore:o}=n(27);t.exports=r,t.exports.default=r,t.exports.DraggableCore=o},888:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=function(t,e){if(t&&t.__esModule)return t;if(null===t||"object"!=typeof t&&"function"!=typeof t)return{default:t};var n=p(e);if(n&&n.has(t))return n.get(t);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in t)if("default"!==i&&Object.prototype.hasOwnProperty.call(t,i)){var a=o?Object.getOwnPropertyDescriptor(t,i):null;a&&(a.get||a.set)?Object.defineProperty(r,i,a):r[i]=t[i]}return r.default=t,n&&n.set(t,r),r}(n(12)),o=c(n(556)),i=c(n(33)),a=n(89),s=n(726),l=n(56),u=c(n(696));function c(t){return t&&t.__esModule?t:{default:t}}function p(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,n=new WeakMap;return(p=function(t){return t?n:e})(t)}function d(t,e,n){return(e=function(t){var e=function(t){if("object"!=typeof t||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}const h={start:"touchstart",move:"touchmove",stop:"touchend"},f={start:"mousedown",move:"mousemove",stop:"mouseup"};let g=f;class m extends r.Component{constructor(){super(...arguments),d(this,"dragging",!1),d(this,"lastX",NaN),d(this,"lastY",NaN),d(this,"touchIdentifier",null),d(this,"mounted",!1),d(this,"handleDragStart",t=>{if(this.props.onMouseDown(t),!this.props.allowAnyClick&&"number"==typeof t.button&&0!==t.button)return!1;const e=this.findDOMNode();if(!e||!e.ownerDocument||!e.ownerDocument.body)throw new Error("<DraggableCore> not mounted on DragStart!");const{ownerDocument:n}=e;if(this.props.disabled||!(t.target instanceof n.defaultView.Node)||this.props.handle&&!(0,a.matchesSelectorAndParentsTo)(t.target,this.props.handle,e)||this.props.cancel&&(0,a.matchesSelectorAndParentsTo)(t.target,this.props.cancel,e))return;"touchstart"===t.type&&t.preventDefault();const r=(0,a.getTouchIdentifier)(t);this.touchIdentifier=r;const o=(0,s.getControlPosition)(t,r,this);if(null==o)return;const{x:i,y:l}=o,c=(0,s.createCoreData)(this,i,l);(0,u.default)("DraggableCore: handleDragStart: %j",c),(0,u.default)("calling",this.props.onStart),!1!==this.props.onStart(t,c)&&!1!==this.mounted&&(this.props.enableUserSelectHack&&(0,a.addUserSelectStyles)(n),this.dragging=!0,this.lastX=i,this.lastY=l,(0,a.addEvent)(n,g.move,this.handleDrag),(0,a.addEvent)(n,g.stop,this.handleDragStop))}),d(this,"handleDrag",t=>{const e=(0,s.getControlPosition)(t,this.touchIdentifier,this);if(null==e)return;let{x:n,y:r}=e;if(Array.isArray(this.props.grid)){let t=n-this.lastX,e=r-this.lastY;if([t,e]=(0,s.snapToGrid)(this.props.grid,t,e),!t&&!e)return;n=this.lastX+t,r=this.lastY+e}const o=(0,s.createCoreData)(this,n,r);if((0,u.default)("DraggableCore: handleDrag: %j",o),!1!==this.props.onDrag(t,o)&&!1!==this.mounted)this.lastX=n,this.lastY=r;else try{this.handleDragStop(new MouseEvent("mouseup"))}catch(t){const e=document.createEvent("MouseEvents");e.initMouseEvent("mouseup",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),this.handleDragStop(e)}}),d(this,"handleDragStop",t=>{if(!this.dragging)return;const e=(0,s.getControlPosition)(t,this.touchIdentifier,this);if(null==e)return;let{x:n,y:r}=e;if(Array.isArray(this.props.grid)){let t=n-this.lastX||0,e=r-this.lastY||0;[t,e]=(0,s.snapToGrid)(this.props.grid,t,e),n=this.lastX+t,r=this.lastY+e}const o=(0,s.createCoreData)(this,n,r);if(!1===this.props.onStop(t,o)||!1===this.mounted)return!1;const i=this.findDOMNode();i&&this.props.enableUserSelectHack&&(0,a.removeUserSelectStyles)(i.ownerDocument),(0,u.default)("DraggableCore: handleDragStop: %j",o),this.dragging=!1,this.lastX=NaN,this.lastY=NaN,i&&((0,u.default)("DraggableCore: Removing handlers"),(0,a.removeEvent)(i.ownerDocument,g.move,this.handleDrag),(0,a.removeEvent)(i.ownerDocument,g.stop,this.handleDragStop))}),d(this,"onMouseDown",t=>(g=f,this.handleDragStart(t))),d(this,"onMouseUp",t=>(g=f,this.handleDragStop(t))),d(this,"onTouchStart",t=>(g=h,this.handleDragStart(t))),d(this,"onTouchEnd",t=>(g=h,this.handleDragStop(t)))}componentDidMount(){this.mounted=!0;const t=this.findDOMNode();t&&(0,a.addEvent)(t,h.start,this.onTouchStart,{passive:!1})}componentWillUnmount(){this.mounted=!1;const t=this.findDOMNode();if(t){const{ownerDocument:e}=t;(0,a.removeEvent)(e,f.move,this.handleDrag),(0,a.removeEvent)(e,h.move,this.handleDrag),(0,a.removeEvent)(e,f.stop,this.handleDragStop),(0,a.removeEvent)(e,h.stop,this.handleDragStop),(0,a.removeEvent)(t,h.start,this.onTouchStart,{passive:!1}),this.props.enableUserSelectHack&&(0,a.removeUserSelectStyles)(e)}}findDOMNode(){var t,e;return null!==(t=this.props)&&void 0!==t&&t.nodeRef?null===(e=this.props)||void 0===e||null===(e=e.nodeRef)||void 0===e?void 0:e.current:i.default.findDOMNode(this)}render(){return r.cloneElement(r.Children.only(this.props.children),{onMouseDown:this.onMouseDown,onMouseUp:this.onMouseUp,onTouchEnd:this.onTouchEnd})}}e.default=m,d(m,"displayName","DraggableCore"),d(m,"propTypes",{allowAnyClick:o.default.bool,children:o.default.node.isRequired,disabled:o.default.bool,enableUserSelectHack:o.default.bool,offsetParent:function(t,e){if(t[e]&&1!==t[e].nodeType)throw new Error("Draggable's offsetParent must be a DOM Node.")},grid:o.default.arrayOf(o.default.number),handle:o.default.string,cancel:o.default.string,nodeRef:o.default.object,onStart:o.default.func,onDrag:o.default.func,onStop:o.default.func,onMouseDown:o.default.func,scale:o.default.number,className:l.dontSetMe,style:l.dontSetMe,transform:l.dontSetMe}),d(m,"defaultProps",{allowAnyClick:!1,disabled:!1,enableUserSelectHack:!0,onStart:function(){},onDrag:function(){},onStop:function(){},onMouseDown:function(){},scale:1})},925:t=>{"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},984:(t,e,n)=>{t.exports=n(326).default,t.exports.utils=n(335),t.exports.calculateUtils=n(417),t.exports.Responsive=n(159).default,t.exports.Responsive.utils=n(209),t.exports.WidthProvider=n(224).default}},r={};function o(t){var e=r[t];if(void 0!==e)return e.exports;var i=r[t]={exports:{}};return n[t].call(i.exports,i,i.exports,o),i.exports}return o.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return o.d(e,{a:e}),e},o.d=(t,e)=>{for(var n in e)o.o(e,n)&&!o.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},o.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),o.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),o.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},o(984)})());
//# sourceMappingURL=react-grid-layout.min.js.map