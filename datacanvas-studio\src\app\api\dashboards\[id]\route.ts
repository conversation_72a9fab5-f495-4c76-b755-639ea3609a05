import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, canViewDashboard, canEditDashboard } from '@/lib/auth/auth';
import { DatabaseService } from '@/lib/services/database';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth();
    
    const result = await DatabaseService.getDashboard(params.id);
    
    if (result.error) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    if (!result.data) {
      return NextResponse.json({ error: 'Dashboard not found' }, { status: 404 });
    }

    // Check if user can view this dashboard
    const permissions = (result.data as any).permissions || [];
    if (!canViewDashboard(user, result.data, permissions)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    return NextResponse.json({ data: result.data });

  } catch (error) {
    console.error('Get dashboard error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth();
    
    // Get existing dashboard to check permissions
    const existingResult = await DatabaseService.getDashboard(params.id);
    
    if (existingResult.error || !existingResult.data) {
      return NextResponse.json({ error: 'Dashboard not found' }, { status: 404 });
    }

    // Check if user can edit this dashboard
    const permissions = (existingResult.data as any).permissions || [];
    if (!canEditDashboard(user, existingResult.data, permissions)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const body = await request.json();
    const { name, description, config, layout, is_public } = body;

    // Update dashboard
    const result = await DatabaseService.updateDashboard(params.id, {
      name,
      description,
      config,
      layout,
      is_public,
    });

    if (result.error) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    // Create audit log
    await DatabaseService.createAuditLog({
      action: 'dashboard_updated',
      resource_type: 'dashboard',
      resource_id: params.id,
      user_id: user.id,
      metadata: {
        changes: { name, description, is_public },
      },
    });

    return NextResponse.json({
      data: result.data,
      message: 'Dashboard updated successfully',
    });

  } catch (error) {
    console.error('Update dashboard error:', error);
    return NextResponse.json(
      { error: 'Failed to update dashboard' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth();
    
    // Get existing dashboard to check permissions
    const existingResult = await DatabaseService.getDashboard(params.id);
    
    if (existingResult.error || !existingResult.data) {
      return NextResponse.json({ error: 'Dashboard not found' }, { status: 404 });
    }

    // Check if user can edit this dashboard (delete requires edit permission)
    const permissions = (existingResult.data as any).permissions || [];
    if (!canEditDashboard(user, existingResult.data, permissions)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Delete dashboard (this will cascade delete charts and comments)
    const supabase = await import('@/lib/supabase/server').then(m => m.createSupabaseServerClient());
    const { error } = await (await supabase)
      .from('dashboards')
      .delete()
      .eq('id', params.id);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Create audit log
    await DatabaseService.createAuditLog({
      action: 'dashboard_deleted',
      resource_type: 'dashboard',
      resource_id: params.id,
      user_id: user.id,
      metadata: {
        name: existingResult.data.name,
      },
    });

    return NextResponse.json({
      message: 'Dashboard deleted successfully',
    });

  } catch (error) {
    console.error('Delete dashboard error:', error);
    return NextResponse.json(
      { error: 'Failed to delete dashboard' },
      { status: 500 }
    );
  }
}
