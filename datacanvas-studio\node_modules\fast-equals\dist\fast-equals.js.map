{"version": 3, "file": "fast-equals.js", "sources": ["../src/utils.ts", "../src/comparator.ts", "../src/arrays.ts", "../src/dates.ts", "../src/maps.ts", "../src/objects.ts", "../src/regexps.ts", "../src/sets.ts", "../src/index.ts"], "sourcesContent": ["import {\n  EqualityComparator,\n  InternalEqualityComparator,\n  TypeEqualityComparator,\n} from '../index.d';\n\n/**\n * Default equality comparator pass-through, used as the standard `isEqual` creator for\n * use inside the built comparator.\n */\nexport function createDefaultIsNestedEqual<Meta>(\n  comparator: EqualityComparator<Meta>,\n): InternalEqualityComparator<Meta> {\n  return function isEqual<A, B>(\n    a: A,\n    b: B,\n    _indexOrKeyA: any,\n    _indexOrKeyB: any,\n    _parentA: any,\n    _parentB: any,\n    meta: Meta,\n  ) {\n    return comparator(a, b, meta);\n  };\n}\n\n/**\n * Wrap the provided `areItemsEqual` method to manage the circular cache, allowing\n * for circular references to be safely included in the comparison without creating\n * stack overflows.\n */\nexport function createIsCircular<\n  AreItemsEqual extends TypeEqualityComparator<any, any>,\n>(areItemsEqual: AreItemsEqual): AreItemsEqual {\n  return function isCircular(\n    a: any,\n    b: any,\n    isEqual: InternalEqualityComparator<WeakMap<any, any>>,\n    cache: WeakMap<any, any>,\n  ) {\n    if (!a || !b || typeof a !== 'object' || typeof b !== 'object') {\n      return areItemsEqual(a, b, isEqual, cache);\n    }\n\n    const cachedA = cache.get(a);\n    const cachedB = cache.get(b);\n\n    if (cachedA && cachedB) {\n      return cachedA === b && cachedB === a;\n    }\n\n    cache.set(a, b);\n    cache.set(b, a);\n\n    const result = areItemsEqual(a, b, isEqual, cache);\n\n    cache.delete(a);\n    cache.delete(b);\n\n    return result;\n  } as AreItemsEqual;\n}\n\n/**\n * Targeted shallow merge of two objects.\n *\n * @NOTE\n * This exists as a tinier compiled version of the `__assign` helper that\n * `tsc` injects in case of `Object.assign` not being present.\n */\nexport function merge<A extends object, B extends object>(a: A, b: B): A & B {\n  const merged: Record<string, any> = {};\n\n  for (const key in a) {\n    merged[key] = a[key];\n  }\n\n  for (const key in b) {\n    merged[key] = b[key];\n  }\n\n  return merged as A & B;\n}\n\n/**\n * Whether the value is a plain object.\n *\n * @NOTE\n * This is a same-realm compariosn only.\n */\nexport function isPlainObject(value: any): boolean {\n  return value.constructor === Object || value.constructor == null;\n}\n\n/**\n * When the value is `Promise`-like, aka \"then-able\".\n */\nexport function isPromiseLike(value: any): boolean {\n  return typeof value.then === 'function';\n}\n\n/**\n * Whether the values passed are strictly equal or both NaN.\n */\nexport function sameValueZeroEqual(a: any, b: any): boolean {\n  return a === b || (a !== a && b !== b);\n}\n", "import { isPlainObject, isPromiseLike, sameValueZeroEqual } from './utils';\n\nimport type {\n  CreateComparatorCreatorOptions,\n  EqualityComparator,\n} from '../index.d';\n\nconst ARGUMENTS_TAG = '[object Arguments]';\nconst BOOLEAN_TAG = '[object Boolean]';\nconst DATE_TAG = '[object Date]';\nconst REG_EXP_TAG = '[object RegExp]';\nconst MAP_TAG = '[object Map]';\nconst NUMBER_TAG = '[object Number]';\nconst OBJECT_TAG = '[object Object]';\nconst SET_TAG = '[object Set]';\nconst STRING_TAG = '[object String]';\n\nconst { toString } = Object.prototype;\n\nexport function createComparator<Meta>({\n  areArraysEqual,\n  areDatesEqual,\n  areMapsEqual,\n  areObjectsEqual,\n  areRegExpsEqual,\n  areSetsEqual,\n  createIsNestedEqual,\n}: CreateComparatorCreatorOptions<Meta>): EqualityComparator<Meta> {\n  const isEqual = createIsNestedEqual(comparator as EqualityComparator<Meta>);\n\n  /**\n   * compare the value of the two objects and return true if they are equivalent in values\n   */\n  function comparator(a: any, b: any, meta: Meta): boolean {\n    // If the items are strictly equal, no need to do a value comparison.\n    if (a === b) {\n      return true;\n    }\n\n    // If the items are not non-nullish objects, then the only possibility\n    // of them being equal but not strictly is if they are both `NaN`. Since\n    // `NaN` is uniquely not equal to itself, we can use self-comparison of\n    // both objects, which is faster than `isNaN()`.\n    if (!a || !b || typeof a !== 'object' || typeof b !== 'object') {\n      return a !== a && b !== b;\n    }\n\n    // Checks are listed in order of commonality of use-case:\n    //   1. Common complex object types (plain object, array)\n    //   2. Common data values (date, regexp)\n    //   3. Less-common complex object types (map, set)\n    //   4. Less-common data values (promise, primitive wrappers)\n    // Inherently this is both subjective and assumptive, however\n    // when reviewing comparable libraries in the wild this order\n    // appears to be generally consistent.\n\n    // `isPlainObject` only checks against the object's own realm. Cross-realm\n    // comparisons are rare, and will be handled in the ultimate fallback, so\n    // we can avoid the `toString.call()` cost unless necessary.\n    if (isPlainObject(a) && isPlainObject(b)) {\n      return areObjectsEqual(a, b, isEqual, meta);\n    }\n\n    // `isArray()` works on subclasses and is cross-realm, so we can again avoid\n    // the `toString.call()` cost unless necessary by just checking if either\n    // and then both are arrays.\n    const aArray = Array.isArray(a);\n    const bArray = Array.isArray(b);\n\n    if (aArray || bArray) {\n      return aArray === bArray && areArraysEqual(a, b, isEqual, meta);\n    }\n\n    // Since this is a custom object, use the classic `toString.call()` to get its\n    // type. This is reasonably performant in modern environments like v8 and\n    // SpiderMonkey, and allows for cross-realm comparison when other checks like\n    // `instanceof` do not.\n    const aTag = toString.call(a);\n\n    if (aTag !== toString.call(b)) {\n      return false;\n    }\n\n    if (aTag === DATE_TAG) {\n      // `getTime()` showed better results compared to alternatives like `valueOf()`\n      // or the unary `+` operator.\n      return areDatesEqual(a, b, isEqual, meta);\n    }\n\n    if (aTag === REG_EXP_TAG) {\n      return areRegExpsEqual(a, b, isEqual, meta);\n    }\n\n    if (aTag === MAP_TAG) {\n      return areMapsEqual(a, b, isEqual, meta);\n    }\n\n    if (aTag === SET_TAG) {\n      return areSetsEqual(a, b, isEqual, meta);\n    }\n\n    // If a simple object tag, then we can prioritize a simple object comparison because\n    // it is likely a custom class. If an arguments tag, it should be treated as a standard\n    // object.\n    if (aTag === OBJECT_TAG || aTag === ARGUMENTS_TAG) {\n      // The exception for value comparison is `Promise`-like contracts. These should be\n      // treated the same as standard `Promise` objects, which means strict equality.\n      return isPromiseLike(a) || isPromiseLike(b)\n        ? false\n        : areObjectsEqual(a, b, isEqual, meta);\n    }\n\n    // As the penultimate fallback, check if the values passed are primitive wrappers. This\n    // is very rare in modern JS, which is why it is deprioritized compared to all other object\n    // types.\n    if (aTag === BOOLEAN_TAG || aTag === NUMBER_TAG || aTag === STRING_TAG) {\n      return sameValueZeroEqual(a.valueOf(), b.valueOf());\n    }\n\n    // If not matching any tags that require a specific type of comparison, then we hard-code false because\n    // the only thing remaining is strict equality, which has already been compared. This is for a few reasons:\n    //   - Certain types that cannot be introspected (e.g., `WeakMap`). For these types, this is the only\n    //     comparison that can be made.\n    //   - For types that can be introspected, but rarely have requirements to be compared\n    //     (`ArrayBuffer`, `DataView`, etc.), the cost is avoided to prioritize the common\n    //     use-cases (may be included in a future release, if requested enough).\n    //   - For types that can be introspected but do not have an objective definition of what\n    //     equality is (`Error`, etc.), the subjective decision is to be conservative and strictly compare.\n    // In all cases, these decisions should be reevaluated based on changes to the language and\n    // common development practices.\n    return false;\n  }\n\n  return comparator as EqualityComparator<Meta>;\n}\n", "import { createIsCircular } from './utils';\n\nimport type { InternalEqualityComparator } from '../index.d';\n\n/**\n * Whether the arrays are equal in value.\n */\nexport function areArraysEqual(\n  a: any[],\n  b: any[],\n  isEqual: InternalEqualityComparator<any>,\n  meta: any,\n): boolean {\n  let index = a.length;\n\n  if (b.length !== index) {\n    return false;\n  }\n\n  // Decrementing `while` showed faster results than either incrementing or\n  // decrementing `for` loop and than an incrementing `while` loop. Declarative\n  // methods like `some` / `every` were not used to avoid incurring the garbage\n  // cost of anonymous callbacks.\n  while (index-- > 0) {\n    if (!isEqual(a[index], b[index], index, index, a, b, meta)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Whether the arrays are equal in value, including circular references.\n */\nexport const areArraysEqualCircular = createIsCircular(areArraysEqual);\n", "import { sameValueZeroEqual } from './utils';\n\n/**\n * Whether the dates passed are equal in value.\n *\n * @NOTE\n * This is a standalone function instead of done inline in the comparator\n * to allow for overrides.\n */\nexport function areDatesEqual(a: Date, b: Date): boolean {\n  return sameValueZeroEqual(a.valueOf(), b.valueOf());\n}\n", "import { createIsCircular } from './utils';\n\nimport type { InternalEqualityComparator } from '../index.d';\n\n/**\n * Whether the `Map`s are equal in value.\n */\nexport function areMapsEqual(\n  a: Map<any, any>,\n  b: Map<any, any>,\n  isEqual: InternalEqualityComparator<any>,\n  meta: any,\n): boolean {\n  let isValueEqual = a.size === b.size;\n\n  if (!isValueEqual) {\n    return false;\n  }\n\n  if (!a.size) {\n    return true;\n  }\n\n  // The use of `forEach()` is to avoid the transpilation cost of `for...of` comparisons, and\n  // the inability to control the performance of the resulting code. It also avoids excessive\n  // iteration compared to doing comparisons of `keys()` and `values()`. As a result, though,\n  // we cannot short-circuit the iterations; bookkeeping must be done to short-circuit the\n  // equality checks themselves.\n\n  const matchedIndices: Record<number, true> = {};\n\n  let indexA = 0;\n\n  a.forEach((aValue, aKey) => {\n    if (!isValueEqual) {\n      return;\n    }\n\n    let hasMatch = false;\n    let matchIndexB = 0;\n\n    b.forEach((bValue, bKey) => {\n      if (\n        !hasMatch &&\n        !matchedIndices[matchIndexB] &&\n        (hasMatch =\n          isEqual(aKey, bKey, indexA, matchIndexB, a, b, meta) &&\n          isEqual(aValue, bValue, aKey, bKey, a, b, meta))\n      ) {\n        matchedIndices[matchIndexB] = true;\n      }\n\n      matchIndexB++;\n    });\n\n    indexA++;\n    isValueEqual = hasMatch;\n  });\n\n  return isValueEqual;\n}\n\n/**\n * Whether the `Map`s are equal in value, including circular references.\n */\nexport const areMapsEqualCircular = createIsCircular(areMapsEqual);\n", "import { createIsCircular } from './utils';\n\nimport type { InternalEqualityComparator } from '../index.d';\n\ninterface Dictionary<Value> {\n  [key: string]: Value;\n  $$typeof?: any;\n}\n\nconst OWNER = '_owner';\nconst { hasOwnProperty } = Object.prototype;\n\n/**\n * Whether the objects are equal in value.\n */\nexport function areObjectsEqual(\n  a: Dictionary<any>,\n  b: Dictionary<any>,\n  isEqual: InternalEqualityComparator<any>,\n  meta: any,\n): boolean {\n  const keysA = Object.keys(a);\n\n  let index = keysA.length;\n\n  if (Object.keys(b).length !== index) {\n    return false;\n  }\n\n  let key: string;\n\n  // Decrementing `while` showed faster results than either incrementing or\n  // decrementing `for` loop and than an incrementing `while` loop. Declarative\n  // methods like `some` / `every` were not used to avoid incurring the garbage\n  // cost of anonymous callbacks.\n  while (index-- > 0) {\n    key = keysA[index];\n\n    if (key === OWNER) {\n      const reactElementA = !!a.$$typeof;\n      const reactElementB = !!b.$$typeof;\n\n      if ((reactElementA || reactElementB) && reactElementA !== reactElementB) {\n        return false;\n      }\n    }\n\n    if (\n      !hasOwnProperty.call(b, key) ||\n      !isEqual(a[key], b[key], key, key, a, b, meta)\n    ) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Whether the objects are equal in value, including circular references.\n */\nexport const areObjectsEqualCircular = createIsCircular(areObjectsEqual);\n", "/**\n * Whether the regexps passed are equal in value.\n *\n * @NOTE\n * This is a standalone function instead of done inline in the comparator\n * to allow for overrides. An example of this would be supporting a\n * pre-ES2015 environment where the `flags` property is not available.\n */\nexport function areRegExpsEqual(a: RegExp, b: RegExp): boolean {\n  return a.source === b.source && a.flags === b.flags;\n}\n", "import { createIsCircular } from './utils';\n\nimport type { InternalEqualityComparator } from '../index.d';\n\n/**\n * Whether the `Set`s are equal in value.\n */\nexport function areSetsEqual(\n  a: Set<any>,\n  b: Set<any>,\n  isEqual: InternalEqualityComparator<any>,\n  meta: any,\n): boolean {\n  let isValueEqual = a.size === b.size;\n\n  if (!isValueEqual) {\n    return false;\n  }\n\n  if (!a.size) {\n    return true;\n  }\n\n  // The use of `forEach()` is to avoid the transpilation cost of `for...of` comparisons, and\n  // the inability to control the performance of the resulting code. It also avoids excessive\n  // iteration compared to doing comparisons of `keys()` and `values()`. As a result, though,\n  // we cannot short-circuit the iterations; bookkeeping must be done to short-circuit the\n  // equality checks themselves.\n\n  const matchedIndices: Record<number, true> = {};\n\n  a.forEach((aValue, aKey) => {\n    if (!isValueEqual) {\n      return;\n    }\n\n    let hasMatch = false;\n    let matchIndex = 0;\n\n    b.forEach((bValue, bKey) => {\n      if (\n        !hasMatch &&\n        !matchedIndices[matchIndex] &&\n        (hasMatch = isEqual(aValue, bValue, aKey, bKey, a, b, meta))\n      ) {\n        matchedIndices[matchIndex] = true;\n      }\n\n      matchIndex++;\n    });\n\n    isValueEqual = hasMatch;\n  });\n\n  return isValueEqual;\n}\n\n/**\n * Whether the `Set`s are equal in value, including circular references.\n */\nexport const areSetsEqualCircular = createIsCircular(areSetsEqual);\n", "import { createComparator } from './comparator';\nimport { areArraysEqual, areArraysEqualCircular } from './arrays';\nimport { areDatesEqual } from './dates';\nimport { areMapsEqual, areMapsEqualCircular } from './maps';\nimport { areObjectsEqual, areObjectsEqualCircular } from './objects';\nimport { areRegExpsEqual } from './regexps';\nimport { areSetsEqual, areSetsEqualCircular } from './sets';\nimport { createDefaultIsNestedEqual, merge, sameValueZeroEqual } from './utils';\n\nimport type {\n  BaseCircularMeta,\n  CreateComparatorCreatorOptions,\n  EqualityComparator,\n  GetComparatorOptions,\n} from '../index.d';\n\nexport { sameValueZeroEqual };\n\nconst DEFAULT_CONFIG: CreateComparatorCreatorOptions<undefined> = Object.freeze(\n  {\n    areArraysEqual,\n    areDatesEqual,\n    areMapsEqual,\n    areObjectsEqual,\n    areRegExpsEqual,\n    areSetsEqual,\n    createIsNestedEqual: createDefaultIsNestedEqual,\n  },\n);\nconst DEFAULT_CIRCULAR_CONFIG: CreateComparatorCreatorOptions<BaseCircularMeta> =\n  Object.freeze({\n    areArraysEqual: areArraysEqualCircular,\n    areDatesEqual,\n    areMapsEqual: areMapsEqualCircular,\n    areObjectsEqual: areObjectsEqualCircular,\n    areRegExpsEqual,\n    areSetsEqual: areSetsEqualCircular,\n    createIsNestedEqual: createDefaultIsNestedEqual,\n  });\n\nconst isDeepEqual = createComparator(DEFAULT_CONFIG);\n\n/**\n * Whether the items passed are deeply-equal in value.\n */\nexport function deepEqual<A, B>(a: A, b: B): boolean {\n  return isDeepEqual(a, b, undefined);\n}\n\nconst isShallowEqual = createComparator(\n  merge(DEFAULT_CONFIG, { createIsNestedEqual: () => sameValueZeroEqual }),\n);\n\n/**\n * Whether the items passed are shallowly-equal in value.\n */\nexport function shallowEqual<A, B>(a: A, b: B): boolean {\n  return isShallowEqual(a, b, undefined);\n}\n\nconst isCircularDeepEqual = createComparator(DEFAULT_CIRCULAR_CONFIG);\n\n/**\n * Whether the items passed are deeply-equal in value, including circular references.\n */\nexport function circularDeepEqual<A, B>(a: A, b: B): boolean {\n  return isCircularDeepEqual(a, b, new WeakMap());\n}\n\nconst isCircularShallowEqual = createComparator(\n  merge(DEFAULT_CIRCULAR_CONFIG, {\n    createIsNestedEqual: () => sameValueZeroEqual,\n  }),\n);\n\n/**\n * Whether the items passed are shallowly-equal in value, including circular references.\n */\nexport function circularShallowEqual<A, B>(a: A, b: B): boolean {\n  return isCircularShallowEqual(a, b, new WeakMap());\n}\n\n/**\n * Create a custom equality comparison method.\n *\n * This can be done to create very targeted comparisons in extreme hot-path scenarios\n * where the standard methods are not performant enough, but can also be used to provide\n * support for legacy environments that do not support expected features like\n * `RegExp.prototype.flags` out of the box.\n */\nexport function createCustomEqual<Meta = undefined>(\n  getComparatorOptions: GetComparatorOptions<Meta>,\n): EqualityComparator<Meta> {\n  return createComparator<Meta>(\n    merge(DEFAULT_CONFIG, getComparatorOptions(DEFAULT_CONFIG as any)),\n  );\n}\n\n/**\n * Create a custom equality comparison method that handles circular references. This is very\n * similar to `createCustomEqual`, with the only difference being that `meta` expects to be\n * populated with a `WeakMap`-like contract.\n *\n * This can be done to create very targeted comparisons in extreme hot-path scenarios\n * where the standard methods are not performant enough, but can also be used to provide\n * support for legacy environments that do not support expected features like\n * `WeakMap` out of the box.\n */\nexport function createCustomCircularEqual<\n  Meta extends BaseCircularMeta = WeakMap<any, any>,\n>(getComparatorOptions: GetComparatorOptions<Meta>): EqualityComparator<Meta> {\n  const comparator = createComparator<Meta>(\n    merge(\n      DEFAULT_CIRCULAR_CONFIG,\n      getComparatorOptions(DEFAULT_CIRCULAR_CONFIG as any),\n    ),\n  );\n\n  return ((a: any, b: any, meta: any = new WeakMap()) =>\n    comparator(a, b, meta)) as EqualityComparator<Meta>;\n}\n"], "names": [], "mappings": ";;;;;;EAMA;;;EAGG;EACG,SAAU,0BAA0B,CACxC,UAAoC,EAAA;EAEpC,IAAA,OAAO,SAAS,OAAO,CACrB,CAAI,EACJ,CAAI,EACJ,YAAiB,EACjB,YAAiB,EACjB,QAAa,EACb,QAAa,EACb,IAAU,EAAA;UAEV,OAAO,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;EAChC,KAAC,CAAC;EACJ,CAAC;EAED;;;;EAIG;EACG,SAAU,gBAAgB,CAE9B,aAA4B,EAAA;MAC5B,OAAO,SAAS,UAAU,CACxB,CAAM,EACN,CAAM,EACN,OAAsD,EACtD,KAAwB,EAAA;EAExB,QAAA,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;cAC9D,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;EAC5C,SAAA;UAED,IAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAC7B,IAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAE7B,IAAI,OAAO,IAAI,OAAO,EAAE;EACtB,YAAA,OAAO,OAAO,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,CAAC;EACvC,SAAA;EAED,QAAA,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,QAAA,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAEhB,QAAA,IAAM,MAAM,GAAG,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;EAEnD,QAAA,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;EAChB,QAAA,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;EAEhB,QAAA,OAAO,MAAM,CAAC;EAChB,KAAkB,CAAC;EACrB,CAAC;EAED;;;;;;EAMG;EACa,SAAA,KAAK,CAAqC,CAAI,EAAE,CAAI,EAAA;MAClE,IAAM,MAAM,GAAwB,EAAE,CAAC;EAEvC,IAAA,KAAK,IAAM,GAAG,IAAI,CAAC,EAAE;UACnB,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;EACtB,KAAA;EAED,IAAA,KAAK,IAAM,GAAG,IAAI,CAAC,EAAE;UACnB,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;EACtB,KAAA;EAED,IAAA,OAAO,MAAe,CAAC;EACzB,CAAC;EAED;;;;;EAKG;EACG,SAAU,aAAa,CAAC,KAAU,EAAA;MACtC,OAAO,KAAK,CAAC,WAAW,KAAK,MAAM,IAAI,KAAK,CAAC,WAAW,IAAI,IAAI,CAAC;EACnE,CAAC;EAED;;EAEG;EACG,SAAU,aAAa,CAAC,KAAU,EAAA;EACtC,IAAA,OAAO,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC;EAC1C,CAAC;EAED;;EAEG;EACa,SAAA,kBAAkB,CAAC,CAAM,EAAE,CAAM,EAAA;EAC/C,IAAA,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;EACzC;;ECnGA,IAAM,aAAa,GAAG,oBAAoB,CAAC;EAC3C,IAAM,WAAW,GAAG,kBAAkB,CAAC;EACvC,IAAM,QAAQ,GAAG,eAAe,CAAC;EACjC,IAAM,WAAW,GAAG,iBAAiB,CAAC;EACtC,IAAM,OAAO,GAAG,cAAc,CAAC;EAC/B,IAAM,UAAU,GAAG,iBAAiB,CAAC;EACrC,IAAM,UAAU,GAAG,iBAAiB,CAAC;EACrC,IAAM,OAAO,GAAG,cAAc,CAAC;EAC/B,IAAM,UAAU,GAAG,iBAAiB,CAAC;EAE7B,IAAA,QAAQ,GAAK,MAAM,CAAC,SAAS,SAArB,CAAsB;EAEhC,SAAU,gBAAgB,CAAO,EAQA,EAAA;EAPrC,IAAA,IAAA,cAAc,oBAAA,EACd,aAAa,mBAAA,EACb,YAAY,kBAAA,EACZ,eAAe,GAAA,EAAA,CAAA,eAAA,EACf,eAAe,GAAA,EAAA,CAAA,eAAA,EACf,YAAY,GAAA,EAAA,CAAA,YAAA,EACZ,mBAAmB,GAAA,EAAA,CAAA,mBAAA,CAAA;EAEnB,IAAA,IAAM,OAAO,GAAG,mBAAmB,CAAC,UAAsC,CAAC,CAAC;EAE5E;;EAEG;EACH,IAAA,SAAS,UAAU,CAAC,CAAM,EAAE,CAAM,EAAE,IAAU,EAAA;;UAE5C,IAAI,CAAC,KAAK,CAAC,EAAE;EACX,YAAA,OAAO,IAAI,CAAC;EACb,SAAA;;;;;EAMD,QAAA,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;EAC9D,YAAA,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAC3B,SAAA;;;;;;;;;;;;UAcD,IAAI,aAAa,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC,EAAE;cACxC,OAAO,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;EAC7C,SAAA;;;;UAKD,IAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;UAChC,IAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;UAEhC,IAAI,MAAM,IAAI,MAAM,EAAE;EACpB,YAAA,OAAO,MAAM,KAAK,MAAM,IAAI,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;EACjE,SAAA;;;;;UAMD,IAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;UAE9B,IAAI,IAAI,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;EAC7B,YAAA,OAAO,KAAK,CAAC;EACd,SAAA;UAED,IAAI,IAAI,KAAK,QAAQ,EAAE;;;cAGrB,OAAO,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;EAC3C,SAAA;UAED,IAAI,IAAI,KAAK,WAAW,EAAE;cACxB,OAAO,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;EAC7C,SAAA;UAED,IAAI,IAAI,KAAK,OAAO,EAAE;cACpB,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;EAC1C,SAAA;UAED,IAAI,IAAI,KAAK,OAAO,EAAE;cACpB,OAAO,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;EAC1C,SAAA;;;;EAKD,QAAA,IAAI,IAAI,KAAK,UAAU,IAAI,IAAI,KAAK,aAAa,EAAE;;;cAGjD,OAAO,aAAa,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC;EACzC,kBAAE,KAAK;oBACL,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;EAC1C,SAAA;;;;UAKD,IAAI,IAAI,KAAK,WAAW,IAAI,IAAI,KAAK,UAAU,IAAI,IAAI,KAAK,UAAU,EAAE;EACtE,YAAA,OAAO,kBAAkB,CAAC,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;EACrD,SAAA;;;;;;;;;;;;EAaD,QAAA,OAAO,KAAK,CAAC;OACd;EAED,IAAA,OAAO,UAAsC,CAAC;EAChD;;EClIA;;EAEG;EACG,SAAU,cAAc,CAC5B,CAAQ,EACR,CAAQ,EACR,OAAwC,EACxC,IAAS,EAAA;EAET,IAAA,IAAI,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC;EAErB,IAAA,IAAI,CAAC,CAAC,MAAM,KAAK,KAAK,EAAE;EACtB,QAAA,OAAO,KAAK,CAAC;EACd,KAAA;;;;;EAMD,IAAA,OAAO,KAAK,EAAE,GAAG,CAAC,EAAE;UAClB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE;EAC1D,YAAA,OAAO,KAAK,CAAC;EACd,SAAA;EACF,KAAA;EAED,IAAA,OAAO,IAAI,CAAC;EACd,CAAC;EAED;;EAEG;EACI,IAAM,sBAAsB,GAAG,gBAAgB,CAAC,cAAc,CAAC;;ECjCtE;;;;;;EAMG;EACa,SAAA,aAAa,CAAC,CAAO,EAAE,CAAO,EAAA;EAC5C,IAAA,OAAO,kBAAkB,CAAC,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;EACtD;;ECPA;;EAEG;EACG,SAAU,YAAY,CAC1B,CAAgB,EAChB,CAAgB,EAChB,OAAwC,EACxC,IAAS,EAAA;MAET,IAAI,YAAY,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;MAErC,IAAI,CAAC,YAAY,EAAE;EACjB,QAAA,OAAO,KAAK,CAAC;EACd,KAAA;EAED,IAAA,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;EACX,QAAA,OAAO,IAAI,CAAC;EACb,KAAA;;;;;;MAQD,IAAM,cAAc,GAAyB,EAAE,CAAC;MAEhD,IAAI,MAAM,GAAG,CAAC,CAAC;EAEf,IAAA,CAAC,CAAC,OAAO,CAAC,UAAC,MAAM,EAAE,IAAI,EAAA;UACrB,IAAI,CAAC,YAAY,EAAE;cACjB,OAAO;EACR,SAAA;UAED,IAAI,QAAQ,GAAG,KAAK,CAAC;UACrB,IAAI,WAAW,GAAG,CAAC,CAAC;EAEpB,QAAA,CAAC,CAAC,OAAO,CAAC,UAAC,MAAM,EAAE,IAAI,EAAA;EACrB,YAAA,IACE,CAAC,QAAQ;kBACT,CAAC,cAAc,CAAC,WAAW,CAAC;EAC5B,iBAAC,QAAQ;EACP,oBAAA,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;EACpD,wBAAA,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAClD;EACA,gBAAA,cAAc,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;EACpC,aAAA;EAED,YAAA,WAAW,EAAE,CAAC;EAChB,SAAC,CAAC,CAAC;EAEH,QAAA,MAAM,EAAE,CAAC;UACT,YAAY,GAAG,QAAQ,CAAC;EAC1B,KAAC,CAAC,CAAC;EAEH,IAAA,OAAO,YAAY,CAAC;EACtB,CAAC;EAED;;EAEG;EACI,IAAM,oBAAoB,GAAG,gBAAgB,CAAC,YAAY,CAAC;;ECxDlE,IAAM,KAAK,GAAG,QAAQ,CAAC;EACf,IAAA,cAAc,GAAK,MAAM,CAAC,SAAS,eAArB,CAAsB;EAE5C;;EAEG;EACG,SAAU,eAAe,CAC7B,CAAkB,EAClB,CAAkB,EAClB,OAAwC,EACxC,IAAS,EAAA;MAET,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;EAE7B,IAAA,IAAI,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC;MAEzB,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,KAAK,EAAE;EACnC,QAAA,OAAO,KAAK,CAAC;EACd,KAAA;EAED,IAAA,IAAI,GAAW,CAAC;;;;;EAMhB,IAAA,OAAO,KAAK,EAAE,GAAG,CAAC,EAAE;EAClB,QAAA,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;UAEnB,IAAI,GAAG,KAAK,KAAK,EAAE;EACjB,YAAA,IAAM,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;EACnC,YAAA,IAAM,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;cAEnC,IAAI,CAAC,aAAa,IAAI,aAAa,KAAK,aAAa,KAAK,aAAa,EAAE;EACvE,gBAAA,OAAO,KAAK,CAAC;EACd,aAAA;EACF,SAAA;UAED,IACE,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC;cAC5B,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAC9C;EACA,YAAA,OAAO,KAAK,CAAC;EACd,SAAA;EACF,KAAA;EAED,IAAA,OAAO,IAAI,CAAC;EACd,CAAC;EAED;;EAEG;EACI,IAAM,uBAAuB,GAAG,gBAAgB,CAAC,eAAe,CAAC;;EC7DxE;;;;;;;EAOG;EACa,SAAA,eAAe,CAAC,CAAS,EAAE,CAAS,EAAA;EAClD,IAAA,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK,CAAC;EACtD;;ECNA;;EAEG;EACG,SAAU,YAAY,CAC1B,CAAW,EACX,CAAW,EACX,OAAwC,EACxC,IAAS,EAAA;MAET,IAAI,YAAY,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;MAErC,IAAI,CAAC,YAAY,EAAE;EACjB,QAAA,OAAO,KAAK,CAAC;EACd,KAAA;EAED,IAAA,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;EACX,QAAA,OAAO,IAAI,CAAC;EACb,KAAA;;;;;;MAQD,IAAM,cAAc,GAAyB,EAAE,CAAC;EAEhD,IAAA,CAAC,CAAC,OAAO,CAAC,UAAC,MAAM,EAAE,IAAI,EAAA;UACrB,IAAI,CAAC,YAAY,EAAE;cACjB,OAAO;EACR,SAAA;UAED,IAAI,QAAQ,GAAG,KAAK,CAAC;UACrB,IAAI,UAAU,GAAG,CAAC,CAAC;EAEnB,QAAA,CAAC,CAAC,OAAO,CAAC,UAAC,MAAM,EAAE,IAAI,EAAA;EACrB,YAAA,IACE,CAAC,QAAQ;kBACT,CAAC,cAAc,CAAC,UAAU,CAAC;EAC3B,iBAAC,QAAQ,GAAG,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAC5D;EACA,gBAAA,cAAc,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;EACnC,aAAA;EAED,YAAA,UAAU,EAAE,CAAC;EACf,SAAC,CAAC,CAAC;UAEH,YAAY,GAAG,QAAQ,CAAC;EAC1B,KAAC,CAAC,CAAC;EAEH,IAAA,OAAO,YAAY,CAAC;EACtB,CAAC;EAED;;EAEG;EACI,IAAM,oBAAoB,GAAG,gBAAgB,CAAC,YAAY,CAAC;;EC1ClE,IAAM,cAAc,GAA8C,MAAM,CAAC,MAAM,CAC7E;EACE,IAAA,cAAc,EAAA,cAAA;EACd,IAAA,aAAa,EAAA,aAAA;EACb,IAAA,YAAY,EAAA,YAAA;EACZ,IAAA,eAAe,EAAA,eAAA;EACf,IAAA,eAAe,EAAA,eAAA;EACf,IAAA,YAAY,EAAA,YAAA;EACZ,IAAA,mBAAmB,EAAE,0BAA0B;EAChD,CAAA,CACF,CAAC;EACF,IAAM,uBAAuB,GAC3B,MAAM,CAAC,MAAM,CAAC;EACZ,IAAA,cAAc,EAAE,sBAAsB;EACtC,IAAA,aAAa,EAAA,aAAA;EACb,IAAA,YAAY,EAAE,oBAAoB;EAClC,IAAA,eAAe,EAAE,uBAAuB;EACxC,IAAA,eAAe,EAAA,eAAA;EACf,IAAA,YAAY,EAAE,oBAAoB;EAClC,IAAA,mBAAmB,EAAE,0BAA0B;EAChD,CAAA,CAAC,CAAC;EAEL,IAAM,WAAW,GAAG,gBAAgB,CAAC,cAAc,CAAC,CAAC;EAErD;;EAEG;EACa,SAAA,SAAS,CAAO,CAAI,EAAE,CAAI,EAAA;MACxC,OAAO,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;EACtC,CAAC;EAED,IAAM,cAAc,GAAG,gBAAgB,CACrC,KAAK,CAAC,cAAc,EAAE,EAAE,mBAAmB,EAAE,YAAA,EAAM,OAAA,kBAAkB,CAAA,EAAA,EAAE,CAAC,CACzE,CAAC;EAEF;;EAEG;EACa,SAAA,YAAY,CAAO,CAAI,EAAE,CAAI,EAAA;MAC3C,OAAO,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;EACzC,CAAC;EAED,IAAM,mBAAmB,GAAG,gBAAgB,CAAC,uBAAuB,CAAC,CAAC;EAEtE;;EAEG;EACa,SAAA,iBAAiB,CAAO,CAAI,EAAE,CAAI,EAAA;MAChD,OAAO,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,OAAO,EAAE,CAAC,CAAC;EAClD,CAAC;EAED,IAAM,sBAAsB,GAAG,gBAAgB,CAC7C,KAAK,CAAC,uBAAuB,EAAE;EAC7B,IAAA,mBAAmB,EAAE,YAAA,EAAM,OAAA,kBAAkB,GAAA;EAC9C,CAAA,CAAC,CACH,CAAC;EAEF;;EAEG;EACa,SAAA,oBAAoB,CAAO,CAAI,EAAE,CAAI,EAAA;MACnD,OAAO,sBAAsB,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,OAAO,EAAE,CAAC,CAAC;EACrD,CAAC;EAED;;;;;;;EAOG;EACG,SAAU,iBAAiB,CAC/B,oBAAgD,EAAA;EAEhD,IAAA,OAAO,gBAAgB,CACrB,KAAK,CAAC,cAAc,EAAE,oBAAoB,CAAC,cAAqB,CAAC,CAAC,CACnE,CAAC;EACJ,CAAC;EAED;;;;;;;;;EASG;EACG,SAAU,yBAAyB,CAEvC,oBAAgD,EAAA;EAChD,IAAA,IAAM,UAAU,GAAG,gBAAgB,CACjC,KAAK,CACH,uBAAuB,EACvB,oBAAoB,CAAC,uBAA8B,CAAC,CACrD,CACF,CAAC;EAEF,IAAA,QAAQ,UAAC,CAAM,EAAE,CAAM,EAAE,IAAyB,EAAA;UAAzB,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,EAAA,IAAgB,GAAA,IAAA,OAAO,EAAE,CAAA,EAAA;EAChD,QAAA,OAAA,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAA;EAAtB,KAAsB,EAA8B;EACxD;;;;;;;;;;;;;;;;"}