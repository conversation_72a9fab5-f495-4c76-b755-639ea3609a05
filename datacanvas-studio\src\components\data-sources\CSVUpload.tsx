'use client';

import { useState, useRef } from 'react';
import { Upload, FileText, Loader2, CheckCircle, XCircle } from 'lucide-react';

interface CSVUploadProps {
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
}

export function CSVUpload({ onSuccess, onError }: CSVUploadProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [error, setError] = useState('');
  const [preview, setPreview] = useState<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = Array.from(e.dataTransfer.files);
    const csvFile = files.find(file => file.name.endsWith('.csv'));
    
    if (csvFile) {
      uploadFile(csvFile);
    } else {
      setError('Please drop a CSV file');
      setUploadStatus('error');
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      uploadFile(file);
    }
  };

  const uploadFile = async (file: File) => {
    setIsUploading(true);
    setError('');
    setUploadStatus('idle');
    setPreview(null);

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/upload/csv', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (response.ok) {
        setUploadStatus('success');
        setPreview(result.data);
        onSuccess?.(result.data);
      } else {
        setUploadStatus('error');
        setError(result.error || 'Upload failed');
        onError?.(result.error || 'Upload failed');
      }
    } catch (err) {
      setUploadStatus('error');
      setError('Upload failed');
      onError?.('Upload failed');
    } finally {
      setIsUploading(false);
    }
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="w-full max-w-2xl mx-auto">
      <div
        className={`
          border-2 border-dashed rounded-lg p-8 text-center transition-colors
          ${isDragging ? 'border-blue-400 bg-blue-50' : 'border-gray-300'}
          ${isUploading ? 'opacity-50 pointer-events-none' : 'cursor-pointer hover:border-gray-400'}
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={openFileDialog}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept=".csv"
          onChange={handleFileSelect}
          className="hidden"
        />

        <div className="flex flex-col items-center space-y-4">
          {isUploading ? (
            <Loader2 className="h-12 w-12 text-blue-500 animate-spin" />
          ) : uploadStatus === 'success' ? (
            <CheckCircle className="h-12 w-12 text-green-500" />
          ) : uploadStatus === 'error' ? (
            <XCircle className="h-12 w-12 text-red-500" />
          ) : (
            <Upload className="h-12 w-12 text-gray-400" />
          )}

          <div>
            {isUploading ? (
              <p className="text-lg font-medium text-gray-900">Uploading and processing...</p>
            ) : uploadStatus === 'success' ? (
              <p className="text-lg font-medium text-green-900">Upload successful!</p>
            ) : uploadStatus === 'error' ? (
              <p className="text-lg font-medium text-red-900">Upload failed</p>
            ) : (
              <p className="text-lg font-medium text-gray-900">Upload CSV File</p>
            )}
            
            {!isUploading && uploadStatus === 'idle' && (
              <p className="text-sm text-gray-500 mt-1">
                Drag and drop your CSV file here, or click to browse
              </p>
            )}
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3 w-full">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          )}
        </div>
      </div>

      {preview && (
        <div className="mt-6 bg-white rounded-lg shadow-md">
          <div className="p-4 border-b">
            <h3 className="text-lg font-medium">Data Preview</h3>
            <p className="text-sm text-gray-600">
              Dataset: {preview.dataset?.name} • {preview.dataset?.row_count} rows • {preview.dataset?.schema_info?.columns?.length} columns
            </p>
          </div>
          
          <div className="p-4">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    {preview.dataset?.schema_info?.columns?.slice(0, 6).map((column: any, index: number) => (
                      <th
                        key={index}
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        {column.name}
                        <span className="text-gray-400 ml-1">({column.type})</span>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {preview.preview?.slice(0, 5).map((row: any, rowIndex: number) => (
                    <tr key={rowIndex}>
                      {preview.dataset?.schema_info?.columns?.slice(0, 6).map((column: any, colIndex: number) => (
                        <td
                          key={colIndex}
                          className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                        >
                          {row[column.name]?.toString() || '-'}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            {preview.dataset?.schema_info?.columns?.length > 6 && (
              <p className="text-sm text-gray-500 mt-2">
                ... and {preview.dataset.schema_info.columns.length - 6} more columns
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
