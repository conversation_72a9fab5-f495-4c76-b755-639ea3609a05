'use client';

import { useState, useEffect } from 'react';
import { Report, Dashboard } from '@/lib/types/database';
import { FileText, Plus, Download, Calendar, Mail, Settings, Trash2, Play } from 'lucide-react';

interface ReportManagerProps {
  dashboardId?: string;
}

export function ReportManager({ dashboardId }: ReportManagerProps) {
  const [reports, setReports] = useState<Report[]>([]);
  const [dashboards, setDashboards] = useState<Dashboard[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isGenerating, setIsGenerating] = useState<{ [key: string]: boolean }>({});
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    loadReports();
    if (!dashboardId) {
      loadDashboards();
    }
  }, [dashboardId]);

  const loadReports = async () => {
    try {
      const url = dashboardId ? `/api/reports?dashboard_id=${dashboardId}` : '/api/reports';
      const response = await fetch(url);
      const result = await response.json();

      if (response.ok) {
        setReports(result.data || []);
      } else {
        setError(result.error || 'Failed to load reports');
      }
    } catch (err) {
      setError('Failed to load reports');
    } finally {
      setIsLoading(false);
    }
  };

  const loadDashboards = async () => {
    try {
      const response = await fetch('/api/dashboards');
      const result = await response.json();

      if (response.ok) {
        setDashboards(result.data || []);
      }
    } catch (err) {
      console.error('Failed to load dashboards:', err);
    }
  };

  const handleGenerateReport = async (reportId: string) => {
    setIsGenerating(prev => ({ ...prev, [reportId]: true }));
    setError('');

    try {
      const response = await fetch(`/api/reports/${reportId}/generate`, {
        method: 'GET',
      });

      if (response.ok) {
        // Download the generated report
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = response.headers.get('Content-Disposition')?.split('filename=')[1]?.replace(/"/g, '') || 'report';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        const result = await response.json();
        setError(result.error || 'Failed to generate report');
      }
    } catch (err) {
      setError('Failed to generate report');
    } finally {
      setIsGenerating(prev => ({ ...prev, [reportId]: false }));
    }
  };

  const getFrequencyLabel = (frequency: string) => {
    switch (frequency) {
      case 'daily': return 'Daily';
      case 'weekly': return 'Weekly';
      case 'monthly': return 'Monthly';
      default: return frequency;
    }
  };

  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'pdf': return '📄';
      case 'excel': return '📊';
      case 'csv': return '📋';
      default: return '📄';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <FileText className="h-6 w-6 text-gray-600" />
          <h2 className="text-xl font-semibold text-gray-900">
            {dashboardId ? 'Dashboard Reports' : 'All Reports'}
          </h2>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create Report
        </button>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-sm text-red-800">{error}</p>
          <button
            onClick={() => setError('')}
            className="text-red-600 hover:text-red-800 text-sm underline ml-2"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Reports List */}
      {reports.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No reports yet</h3>
          <p className="text-gray-600 mb-4">
            Create your first automated report to get started.
          </p>
          <button
            onClick={() => setShowCreateForm(true)}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create First Report
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {reports.map((report) => (
            <div
              key={report.id}
              className="bg-white rounded-lg shadow-md border border-gray-200 p-6"
            >
              {/* Report Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="text-2xl">{getFormatIcon(report.format)}</div>
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 truncate">
                      {report.name}
                    </h3>
                    <p className="text-sm text-gray-500">
                      {(report as any).dashboard?.name || 'Unknown Dashboard'}
                    </p>
                  </div>
                </div>
                <div className={`
                  w-3 h-3 rounded-full
                  ${report.is_active ? 'bg-green-400' : 'bg-gray-400'}
                `} />
              </div>

              {/* Report Details */}
              <div className="space-y-2 mb-4">
                <div className="flex items-center text-sm text-gray-600">
                  <Calendar className="h-4 w-4 mr-2" />
                  <span>{getFrequencyLabel(report.schedule_config.frequency)}</span>
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <Mail className="h-4 w-4 mr-2" />
                  <span>{report.recipients.length} recipient{report.recipients.length !== 1 ? 's' : ''}</span>
                </div>
                {report.last_sent_at && (
                  <div className="text-xs text-gray-500">
                    Last sent: {formatDate(report.last_sent_at)}
                  </div>
                )}
              </div>

              {/* Actions */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleGenerateReport(report.id)}
                    disabled={isGenerating[report.id]}
                    className="inline-flex items-center px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isGenerating[report.id] ? (
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1" />
                    ) : (
                      <Download className="h-3 w-3 mr-1" />
                    )}
                    {isGenerating[report.id] ? 'Generating...' : 'Download'}
                  </button>
                </div>

                <div className="flex items-center space-x-1">
                  <button
                    className="p-1 text-gray-400 hover:text-gray-600 rounded"
                    title="Edit report"
                  >
                    <Settings className="h-4 w-4" />
                  </button>
                  <button
                    className="p-1 text-gray-400 hover:text-red-600 rounded"
                    title="Delete report"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Create Report Form Modal */}
      {showCreateForm && (
        <CreateReportForm
          dashboardId={dashboardId}
          dashboards={dashboards}
          onSuccess={(newReport) => {
            setReports(prev => [...prev, newReport]);
            setShowCreateForm(false);
          }}
          onCancel={() => setShowCreateForm(false)}
        />
      )}
    </div>
  );
}

interface CreateReportFormProps {
  dashboardId?: string;
  dashboards: Dashboard[];
  onSuccess: (report: Report) => void;
  onCancel: () => void;
}

function CreateReportForm({ dashboardId, dashboards, onSuccess, onCancel }: CreateReportFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    dashboard_id: dashboardId || '',
    format: 'pdf' as 'pdf' | 'excel' | 'csv',
    schedule_config: {
      frequency: 'weekly' as 'daily' | 'weekly' | 'monthly',
      time: '09:00',
      timezone: 'UTC',
    },
    recipients: [''],
    is_active: true,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate recipients
    const validRecipients = formData.recipients.filter(email => 
      email.trim() && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.trim())
    );

    if (validRecipients.length === 0) {
      setError('Please provide at least one valid email address');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      const response = await fetch('/api/reports', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          recipients: validRecipients,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        onSuccess(result.data);
      } else {
        setError(result.error || 'Failed to create report');
      }
    } catch (err) {
      setError('Failed to create report');
    } finally {
      setIsSubmitting(false);
    }
  };

  const addRecipient = () => {
    setFormData(prev => ({
      ...prev,
      recipients: [...prev.recipients, ''],
    }));
  };

  const removeRecipient = (index: number) => {
    setFormData(prev => ({
      ...prev,
      recipients: prev.recipients.filter((_, i) => i !== index),
    }));
  };

  const updateRecipient = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      recipients: prev.recipients.map((email, i) => i === index ? value : email),
    }));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Create New Report</h3>
          
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Report Name
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>

            {!dashboardId && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Dashboard
                </label>
                <select
                  value={formData.dashboard_id}
                  onChange={(e) => setFormData(prev => ({ ...prev, dashboard_id: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  <option value="">Select a dashboard</option>
                  {dashboards.map(dashboard => (
                    <option key={dashboard.id} value={dashboard.id}>
                      {dashboard.name}
                    </option>
                  ))}
                </select>
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Format
              </label>
              <select
                value={formData.format}
                onChange={(e) => setFormData(prev => ({ ...prev, format: e.target.value as any }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="pdf">PDF</option>
                <option value="excel">Excel</option>
                <option value="csv">CSV</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Frequency
              </label>
              <select
                value={formData.schedule_config.frequency}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  schedule_config: {
                    ...prev.schedule_config,
                    frequency: e.target.value as any,
                  },
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Recipients
              </label>
              {formData.recipients.map((email, index) => (
                <div key={index} className="flex items-center space-x-2 mb-2">
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => updateRecipient(index, e.target.value)}
                    placeholder="<EMAIL>"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  {formData.recipients.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeRecipient(index)}
                      className="p-2 text-red-600 hover:text-red-800"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  )}
                </div>
              ))}
              <button
                type="button"
                onClick={addRecipient}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                + Add recipient
              </button>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <button
                type="button"
                onClick={onCancel}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Creating...' : 'Create Report'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
