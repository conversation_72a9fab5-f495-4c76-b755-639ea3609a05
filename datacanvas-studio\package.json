{"name": "datacanvas-studio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.52.0", "@types/react-grid-layout": "^1.3.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "15.4.1", "react": "19.1.0", "react-dom": "19.1.0", "react-grid-layout": "^1.5.2", "recharts": "^3.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/multer": "^2.0.0", "@types/node": "^20", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.4", "@types/react": "^19", "@types/react-dom": "^19", "csv-parser": "^3.2.0", "eslint": "^9", "eslint-config-next": "15.4.1", "multer": "^2.0.2", "mysql2": "^3.14.2", "nodemailer": "^7.0.5", "pg": "^8.16.3", "tailwindcss": "^4", "typescript": "^5"}}