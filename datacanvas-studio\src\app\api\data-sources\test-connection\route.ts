import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth/auth';
import { DataProcessingService } from '@/lib/services/data-processing';

export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth();
    const body = await request.json();
    const { type, connection_config } = body;

    // Validate required fields
    if (!type || !connection_config) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Validate data source type
    if (!['postgresql', 'mysql', 'api'].includes(type)) {
      return NextResponse.json({ error: 'Invalid data source type' }, { status: 400 });
    }

    // Create a temporary data source object for testing
    const tempDataSource = {
      id: 'temp',
      name: 'Test Connection',
      type,
      connection_config,
      organization_id: user.organization_id!,
      created_by: user.id,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    // Test the connection
    const isValid = await DataProcessingService.validateDataSource(tempDataSource);

    if (!isValid) {
      return NextResponse.json({ 
        success: false, 
        error: 'Failed to connect to data source. Please check your connection settings.' 
      }, { status: 400 });
    }

    // If connection is successful, try to get sample data
    try {
      let sampleData;
      
      switch (type) {
        case 'postgresql':
          const pgQuery = connection_config.query || 'SELECT 1 as test_column';
          sampleData = await DataProcessingService.processPostgreSQLData(connection_config, pgQuery);
          break;
          
        case 'mysql':
          const mysqlQuery = connection_config.query || 'SELECT 1 as test_column';
          sampleData = await DataProcessingService.processMySQLData(connection_config, mysqlQuery);
          break;
          
        case 'api':
          sampleData = await DataProcessingService.processAPIData(connection_config);
          break;
      }

      return NextResponse.json({
        success: true,
        message: 'Connection successful',
        preview: {
          schema: sampleData?.schema,
          sample_data: sampleData?.sampleData?.slice(0, 5),
          row_count: sampleData?.rowCount,
        },
      });

    } catch (dataError) {
      console.error('Data processing error:', dataError);
      return NextResponse.json({
        success: true,
        message: 'Connection successful but failed to process sample data',
        warning: 'You may need to adjust your query or API configuration',
      });
    }

  } catch (error) {
    console.error('Test connection error:', error);
    return NextResponse.json({
      success: false,
      error: 'Connection test failed. Please verify your settings and try again.',
    }, { status: 500 });
  }
}
