import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, canEditDashboard } from '@/lib/auth/auth';
import { DatabaseService } from '@/lib/services/database';

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth();
    
    // Get chart and its dashboard to check permissions
    const supabase = await import('@/lib/supabase/server').then(m => m.createSupabaseServerClient());
    const { data: chart, error: chartError } = await (await supabase)
      .from('charts')
      .select(`
        *,
        dashboard:dashboards(*)
      `)
      .eq('id', params.id)
      .single();

    if (chartError || !chart) {
      return NextResponse.json({ error: 'Chart not found' }, { status: 404 });
    }

    // Check if user can edit the dashboard containing this chart
    if (!canEditDashboard(user, chart.dashboard, [])) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const body = await request.json();
    const { name, type, config, position } = body;

    // Update chart
    const result = await DatabaseService.updateChart(params.id, {
      name,
      type,
      config,
      position,
    });

    if (result.error) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    // Create audit log
    await DatabaseService.createAuditLog({
      action: 'chart_updated',
      resource_type: 'chart',
      resource_id: params.id,
      user_id: user.id,
      metadata: {
        changes: { name, type, config, position },
      },
    });

    return NextResponse.json({
      data: result.data,
      message: 'Chart updated successfully',
    });

  } catch (error) {
    console.error('Update chart error:', error);
    return NextResponse.json(
      { error: 'Failed to update chart' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth();
    
    // Get chart and its dashboard to check permissions
    const supabase = await import('@/lib/supabase/server').then(m => m.createSupabaseServerClient());
    const { data: chart, error: chartError } = await (await supabase)
      .from('charts')
      .select(`
        *,
        dashboard:dashboards(*)
      `)
      .eq('id', params.id)
      .single();

    if (chartError || !chart) {
      return NextResponse.json({ error: 'Chart not found' }, { status: 404 });
    }

    // Check if user can edit the dashboard containing this chart
    if (!canEditDashboard(user, chart.dashboard, [])) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Delete chart
    const result = await DatabaseService.deleteChart(params.id);

    if (result.error) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    // Create audit log
    await DatabaseService.createAuditLog({
      action: 'chart_deleted',
      resource_type: 'chart',
      resource_id: params.id,
      user_id: user.id,
      metadata: {
        name: chart.name,
        dashboard_id: chart.dashboard_id,
      },
    });

    return NextResponse.json({
      message: 'Chart deleted successfully',
    });

  } catch (error) {
    console.error('Delete chart error:', error);
    return NextResponse.json(
      { error: 'Failed to delete chart' },
      { status: 500 }
    );
  }
}
