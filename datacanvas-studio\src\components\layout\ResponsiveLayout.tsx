'use client';

import { ReactNode, useState } from 'react';
import { User } from '@/lib/types/database';
import { useResponsive } from '@/hooks/useResponsive';
import { MobileNavigation, MobileBottomNavigation, MobileFAB } from './MobileNavigation';
import { DesktopSidebar } from './DesktopSidebar';
import { BrandingProvider } from '@/components/branding/BrandingProvider';

interface ResponsiveLayoutProps {
  user: User;
  children: ReactNode;
  currentPath: string;
  onSignOut: () => void;
  onNewDashboard?: () => void;
  showFAB?: boolean;
}

export function ResponsiveLayout({
  user,
  children,
  currentPath,
  onSignOut,
  onNewDashboard,
  showFAB = true,
}: ResponsiveLayoutProps) {
  const { isMobile, isTablet, isDesktop } = useResponsive();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <BrandingProvider organizationId={user.organization_id}>
      <div className="min-h-screen bg-gray-50">
        {/* Mobile Layout */}
        {isMobile && (
          <>
            <MobileNavigation
              user={user}
              currentPath={currentPath}
              onSignOut={onSignOut}
            />
            <main className="pb-16"> {/* Space for bottom navigation */}
              {children}
            </main>
            <MobileBottomNavigation user={user} currentPath={currentPath} />
            {showFAB && onNewDashboard && (
              <MobileFAB user={user} onAction={onNewDashboard} />
            )}
          </>
        )}

        {/* Tablet Layout */}
        {isTablet && (
          <div className="flex h-screen">
            {/* Collapsible Sidebar */}
            <div className={`
              fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out
              ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
            `}>
              <DesktopSidebar
                user={user}
                currentPath={currentPath}
                onSignOut={onSignOut}
                onClose={() => setSidebarOpen(false)}
                isCollapsed={false}
              />
            </div>

            {/* Overlay */}
            {sidebarOpen && (
              <div
                className="fixed inset-0 bg-black bg-opacity-50 z-40"
                onClick={() => setSidebarOpen(false)}
              />
            )}

            {/* Main Content */}
            <div className="flex-1 flex flex-col overflow-hidden">
              {/* Top Bar */}
              <header className="bg-white shadow-sm border-b border-gray-200 px-4 py-3">
                <div className="flex items-center justify-between">
                  <button
                    onClick={() => setSidebarOpen(true)}
                    className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
                  >
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                  </button>
                  <div className="flex items-center space-x-3">
                    <div className="text-sm text-gray-600">
                      {user.full_name || user.email}
                    </div>
                    <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                      {user.avatar_url ? (
                        <img
                          src={user.avatar_url}
                          alt={user.full_name || user.email}
                          className="w-8 h-8 rounded-full"
                        />
                      ) : (
                        <span className="text-xs font-medium text-gray-600">
                          {(user.full_name || user.email).charAt(0).toUpperCase()}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </header>

              {/* Content */}
              <main className="flex-1 overflow-auto">
                {children}
              </main>
            </div>
          </div>
        )}

        {/* Desktop Layout */}
        {isDesktop && (
          <div className="flex h-screen">
            {/* Sidebar */}
            <div className="w-64 bg-white shadow-sm border-r border-gray-200">
              <DesktopSidebar
                user={user}
                currentPath={currentPath}
                onSignOut={onSignOut}
                isCollapsed={false}
              />
            </div>

            {/* Main Content */}
            <div className="flex-1 flex flex-col overflow-hidden">
              <main className="flex-1 overflow-auto">
                {children}
              </main>
            </div>
          </div>
        )}
      </div>
    </BrandingProvider>
  );
}

// Desktop sidebar component
function DesktopSidebar({
  user,
  currentPath,
  onSignOut,
  onClose,
  isCollapsed = false,
}: {
  user: User;
  currentPath: string;
  onSignOut: () => void;
  onClose?: () => void;
  isCollapsed?: boolean;
}) {
  const navigationItems = [
    { href: '/dashboard', icon: '🏠', label: 'Dashboard', roles: ['viewer', 'editor', 'admin'] },
    { href: '/dashboards', icon: '📊', label: 'Dashboards', roles: ['viewer', 'editor', 'admin'] },
    { href: '/data-sources', icon: '🗄️', label: 'Data Sources', roles: ['editor', 'admin'] },
    { href: '/reports', icon: '📄', label: 'Reports', roles: ['viewer', 'editor', 'admin'] },
    { href: '/users', icon: '👥', label: 'Users', roles: ['admin'] },
    { href: '/settings', icon: '⚙️', label: 'Settings', roles: ['admin'] },
  ];

  const isActive = (href: string) => {
    return currentPath === href || currentPath.startsWith(href + '/');
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        {!isCollapsed && (
          <div className="flex items-center space-x-2">
            <div className="text-xl font-bold text-gray-900">DataCanvas</div>
          </div>
        )}
        {onClose && (
          <button
            onClick={onClose}
            className="p-1 text-gray-400 hover:text-gray-600 rounded"
          >
            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-4 space-y-1 overflow-y-auto">
        {navigationItems.map((item) => {
          // Check if user has permission for this item
          if (!item.roles.includes(user.role)) {
            return null;
          }

          return (
            <a
              key={item.href}
              href={item.href}
              className={`
                flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors
                ${isActive(item.href)
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                }
              `}
            >
              <span className="mr-3 text-lg">{item.icon}</span>
              {!isCollapsed && item.label}
            </a>
          );
        })}
      </nav>

      {/* User Info */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center space-x-3 mb-3">
          <div className="flex-shrink-0">
            {user.avatar_url ? (
              <img
                src={user.avatar_url}
                alt={user.full_name || user.email}
                className="h-8 w-8 rounded-full"
              />
            ) : (
              <div className="h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center">
                <span className="text-xs font-medium text-gray-600">
                  {(user.full_name || user.email).charAt(0).toUpperCase()}
                </span>
              </div>
            )}
          </div>
          {!isCollapsed && (
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {user.full_name || 'No name'}
              </p>
              <p className="text-xs text-gray-500 truncate">{user.email}</p>
            </div>
          )}
        </div>
        <button
          onClick={onSignOut}
          className={`
            w-full flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md
            ${isCollapsed ? 'justify-center' : ''}
          `}
        >
          <span className="mr-2">🚪</span>
          {!isCollapsed && 'Sign Out'}
        </button>
      </div>
    </div>
  );
}

// Responsive container component
export function ResponsiveContainer({ 
  children, 
  className = '',
  maxWidth = 'max-w-7xl' 
}: { 
  children: ReactNode; 
  className?: string;
  maxWidth?: string;
}) {
  return (
    <div className={`mx-auto px-4 sm:px-6 lg:px-8 ${maxWidth} ${className}`}>
      {children}
    </div>
  );
}

// Responsive grid component
export function ResponsiveGrid({ 
  children, 
  columns = { mobile: 1, tablet: 2, desktop: 3 },
  gap = 'gap-6',
  className = '' 
}: { 
  children: ReactNode;
  columns?: { mobile?: number; tablet?: number; desktop?: number };
  gap?: string;
  className?: string;
}) {
  const gridCols = `grid-cols-${columns.mobile || 1} md:grid-cols-${columns.tablet || 2} lg:grid-cols-${columns.desktop || 3}`;
  
  return (
    <div className={`grid ${gridCols} ${gap} ${className}`}>
      {children}
    </div>
  );
}
