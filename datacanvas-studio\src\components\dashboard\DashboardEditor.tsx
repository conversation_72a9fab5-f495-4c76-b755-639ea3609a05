'use client';

import { useState, useEffect } from 'react';
import { Layout } from 'react-grid-layout';
import { DashboardGrid } from './DashboardGrid';
import { ChartBuilder } from '@/components/charts/ChartBuilder';
import { ChartGallery } from '@/components/charts/ChartGallery';
import { Dashboard, Chart, Dataset } from '@/lib/types/database';
import { ChartConfig, ChartType } from '@/lib/charts/types';
import { Save, Eye, Settings, Plus, X } from 'lucide-react';

interface DashboardEditorProps {
  dashboard: Dashboard;
  onSave?: (dashboard: Dashboard) => void;
  onCancel?: () => void;
}

type EditorMode = 'grid' | 'chart-gallery' | 'chart-builder';

export function DashboardEditor({ dashboard, onSave, onCancel }: DashboardEditorProps) {
  const [mode, setMode] = useState<EditorMode>('grid');
  const [charts, setCharts] = useState<Chart[]>([]);
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [selectedDataset, setSelectedDataset] = useState<Dataset | null>(null);
  const [selectedChartType, setSelectedChartType] = useState<ChartType | null>(null);
  const [editingChart, setEditingChart] = useState<Chart | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    loadDashboardData();
    loadDatasets();
  }, [dashboard.id]);

  const loadDashboardData = async () => {
    try {
      const response = await fetch(`/api/dashboards/${dashboard.id}`);
      const result = await response.json();

      if (response.ok && result.data) {
        setCharts(result.data.charts || []);
      }
    } catch (err) {
      setError('Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const loadDatasets = async () => {
    try {
      const response = await fetch('/api/datasets');
      const result = await response.json();

      if (response.ok) {
        setDatasets(result.data || []);
      }
    } catch (err) {
      console.error('Failed to load datasets:', err);
    }
  };

  const handleLayoutChange = async (layout: Layout[]) => {
    // Update chart positions
    const updatedCharts = charts.map(chart => {
      const layoutItem = layout.find(item => item.i === chart.id);
      if (layoutItem) {
        return {
          ...chart,
          position: {
            x: layoutItem.x,
            y: layoutItem.y,
            w: layoutItem.w,
            h: layoutItem.h,
          },
        };
      }
      return chart;
    });

    setCharts(updatedCharts);

    // Auto-save layout changes
    try {
      await Promise.all(
        updatedCharts.map(chart =>
          fetch(`/api/charts/${chart.id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              position: chart.position,
            }),
          })
        )
      );
    } catch (err) {
      console.error('Failed to save layout changes:', err);
    }
  };

  const handleAddChart = () => {
    if (datasets.length === 0) {
      setError('No datasets available. Please create a dataset first.');
      return;
    }
    setMode('chart-gallery');
  };

  const handleSelectDataset = (dataset: Dataset) => {
    setSelectedDataset(dataset);
    setMode('chart-gallery');
  };

  const handleSelectChartType = (chartType: ChartType) => {
    setSelectedChartType(chartType);
    setMode('chart-builder');
  };

  const handleSaveChart = async (config: ChartConfig) => {
    try {
      setIsSaving(true);

      if (editingChart) {
        // Update existing chart
        const response = await fetch(`/api/charts/${editingChart.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            name: config.name,
            type: config.type,
            config: {
              data_mapping: config.mapping,
              styling: config.style,
              filters: config.filters,
            },
            position: config.position,
          }),
        });

        if (response.ok) {
          const result = await response.json();
          setCharts(prev => prev.map(chart => 
            chart.id === editingChart.id ? result.data : chart
          ));
        }
      } else {
        // Create new chart
        const response = await fetch(`/api/dashboards/${dashboard.id}/charts`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            name: config.name,
            type: config.type,
            config: {
              data_mapping: config.mapping,
              styling: config.style,
              filters: config.filters,
            },
            dataset_id: config.datasetId,
            position: config.position,
          }),
        });

        if (response.ok) {
          const result = await response.json();
          setCharts(prev => [...prev, result.data]);
        }
      }

      // Reset state and return to grid
      setMode('grid');
      setSelectedDataset(null);
      setSelectedChartType(null);
      setEditingChart(null);
    } catch (err) {
      setError('Failed to save chart');
    } finally {
      setIsSaving(false);
    }
  };

  const handleEditChart = (chart: Chart) => {
    const dataset = datasets.find(d => d.id === chart.dataset_id);
    if (dataset) {
      setSelectedDataset(dataset);
      setEditingChart(chart);
      setMode('chart-builder');
    }
  };

  const handleDeleteChart = async (chart: Chart) => {
    if (!confirm('Are you sure you want to delete this chart?')) return;

    try {
      const response = await fetch(`/api/charts/${chart.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setCharts(prev => prev.filter(c => c.id !== chart.id));
      }
    } catch (err) {
      setError('Failed to delete chart');
    }
  };

  const handleSaveDashboard = async () => {
    try {
      setIsSaving(true);
      
      const response = await fetch(`/api/dashboards/${dashboard.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: dashboard.name,
          description: dashboard.description,
          config: dashboard.config,
          layout: charts.map(chart => ({
            i: chart.id,
            x: chart.position.x,
            y: chart.position.y,
            w: chart.position.w,
            h: chart.position.h,
          })),
          is_public: dashboard.is_public,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        onSave?.(result.data);
      }
    } catch (err) {
      setError('Failed to save dashboard');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    if (mode === 'grid') {
      onCancel?.();
    } else {
      setMode('grid');
      setSelectedDataset(null);
      setSelectedChartType(null);
      setEditingChart(null);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white">
        <div className="flex items-center space-x-4">
          <h1 className="text-xl font-semibold text-gray-900">
            {mode === 'grid' ? 'Edit Dashboard' : 
             mode === 'chart-gallery' ? 'Choose Chart Type' : 
             'Configure Chart'}
          </h1>
          {mode === 'grid' && (
            <span className="text-sm text-gray-500">
              {charts.length} chart{charts.length !== 1 ? 's' : ''}
            </span>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {mode === 'grid' && (
            <>
              <button
                onClick={handleSaveDashboard}
                disabled={isSaving}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
              >
                <Save className="h-4 w-4 mr-2" />
                {isSaving ? 'Saving...' : 'Save'}
              </button>
            </>
          )}
          <button
            onClick={handleCancel}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <X className="h-4 w-4 mr-2" />
            {mode === 'grid' ? 'Cancel' : 'Back'}
          </button>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 m-4">
          <p className="text-sm text-red-800">{error}</p>
          <button
            onClick={() => setError('')}
            className="text-red-600 hover:text-red-800 text-sm underline ml-2"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Content */}
      <div className="flex-1 overflow-auto">
        {mode === 'grid' && (
          <div className="p-4">
            <DashboardGrid
              dashboard={dashboard}
              charts={charts}
              datasets={datasets}
              isEditing={true}
              onLayoutChange={handleLayoutChange}
              onChartEdit={handleEditChart}
              onChartDelete={handleDeleteChart}
              onAddChart={handleAddChart}
            />
          </div>
        )}

        {mode === 'chart-gallery' && selectedDataset && (
          <ChartGallery
            dataset={selectedDataset}
            onSelectChart={handleSelectChartType}
            onCancel={() => setMode('grid')}
          />
        )}

        {mode === 'chart-builder' && selectedDataset && selectedChartType && (
          <ChartBuilder
            dataset={selectedDataset}
            onSave={handleSaveChart}
            onCancel={() => setMode('grid')}
            initialConfig={editingChart ? {
              id: editingChart.id,
              type: selectedChartType,
              name: editingChart.name,
              mapping: editingChart.config.data_mapping || {},
              style: editingChart.config.styling || {},
              filters: editingChart.config.filters || [],
              position: editingChart.position,
            } : undefined}
          />
        )}
      </div>
    </div>
  );
}
