'use client';

import { ReactNode } from 'react';
import { User } from '@/lib/types/database';
import { hasPermission } from '@/lib/auth/auth';
import { Shield, Lock } from 'lucide-react';

interface RoleGuardProps {
  user: User;
  requiredRole: 'admin' | 'editor' | 'viewer';
  children: ReactNode;
  fallback?: ReactNode;
  showError?: boolean;
}

export function RoleGuard({ 
  user, 
  requiredRole, 
  children, 
  fallback,
  showError = true 
}: RoleGuardProps) {
  const hasAccess = hasPermission(user, requiredRole);

  if (!hasAccess) {
    if (fallback) {
      return <>{fallback}</>;
    }

    if (!showError) {
      return null;
    }

    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
            <Lock className="h-6 w-6 text-red-600" />
          </div>
          <h3 className="mt-4 text-lg font-medium text-gray-900">Access Denied</h3>
          <p className="mt-2 text-sm text-gray-600">
            You need {requiredRole} privileges to access this feature.
          </p>
          <p className="mt-1 text-xs text-gray-500">
            Your current role: {user.role}
          </p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

interface PermissionWrapperProps {
  user: User;
  requiredRole: 'admin' | 'editor' | 'viewer';
  children: ReactNode;
  fallback?: ReactNode;
}

export function PermissionWrapper({ 
  user, 
  requiredRole, 
  children, 
  fallback = null 
}: PermissionWrapperProps) {
  const hasAccess = hasPermission(user, requiredRole);
  
  if (!hasAccess) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

interface RoleBasedMenuProps {
  user: User;
  adminItems?: ReactNode;
  editorItems?: ReactNode;
  viewerItems?: ReactNode;
}

export function RoleBasedMenu({ 
  user, 
  adminItems, 
  editorItems, 
  viewerItems 
}: RoleBasedMenuProps) {
  return (
    <>
      {/* Items available to all roles */}
      {viewerItems}
      
      {/* Items available to editors and admins */}
      {hasPermission(user, 'editor') && editorItems}
      
      {/* Items available only to admins */}
      {hasPermission(user, 'admin') && adminItems}
    </>
  );
}

interface ConditionalRenderProps {
  user: User;
  roles: ('admin' | 'editor' | 'viewer')[];
  children: ReactNode;
  fallback?: ReactNode;
}

export function ConditionalRender({ 
  user, 
  roles, 
  children, 
  fallback = null 
}: ConditionalRenderProps) {
  const hasAccess = roles.some(role => hasPermission(user, role));
  
  if (!hasAccess) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// Hook for checking permissions in components
export function usePermissions(user: User) {
  return {
    isAdmin: user.role === 'admin',
    isEditor: hasPermission(user, 'editor'),
    isViewer: hasPermission(user, 'viewer'),
    canEdit: hasPermission(user, 'editor'),
    canAdmin: hasPermission(user, 'admin'),
    hasRole: (role: 'admin' | 'editor' | 'viewer') => hasPermission(user, role),
  };
}
