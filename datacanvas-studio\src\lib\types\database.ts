// Database Types for DataCanvas Studio

export interface User {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  role: 'admin' | 'editor' | 'viewer';
  organization_id?: string;
  created_at: string;
  updated_at: string;
}

export interface Organization {
  id: string;
  name: string;
  slug: string;
  logo_url?: string;
  custom_branding: Record<string, any>;
  settings: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface DataSource {
  id: string;
  name: string;
  type: 'csv' | 'postgresql' | 'mysql' | 'api';
  connection_config: Record<string, any>;
  organization_id: string;
  created_by: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Dataset {
  id: string;
  name: string;
  description?: string;
  data_source_id: string;
  schema_info: {
    columns: Array<{
      name: string;
      type: string;
      nullable: boolean;
    }>;
  };
  sample_data?: Record<string, any>[];
  row_count: number;
  organization_id: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface Dashboard {
  id: string;
  name: string;
  description?: string;
  config: {
    theme?: string;
    filters?: any[];
    settings?: Record<string, any>;
  };
  layout: Array<{
    i: string;
    x: number;
    y: number;
    w: number;
    h: number;
  }>;
  is_public: boolean;
  organization_id: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface DashboardPermission {
  id: string;
  dashboard_id: string;
  user_id: string;
  permission: 'view' | 'edit' | 'admin';
  created_at: string;
}

export interface Chart {
  id: string;
  name: string;
  type: string;
  config: {
    data_mapping: Record<string, string>;
    styling: Record<string, any>;
    filters?: any[];
  };
  dataset_id: string;
  dashboard_id: string;
  position: {
    x: number;
    y: number;
    w: number;
    h: number;
  };
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface Comment {
  id: string;
  content: string;
  dashboard_id?: string;
  chart_id?: string;
  user_id: string;
  parent_id?: string;
  created_at: string;
  updated_at: string;
  user?: User;
  replies?: Comment[];
}

export interface Report {
  id: string;
  name: string;
  dashboard_id: string;
  schedule_config: {
    frequency: 'daily' | 'weekly' | 'monthly';
    time: string;
    timezone: string;
    days_of_week?: number[];
    day_of_month?: number;
  };
  recipients: string[];
  format: 'pdf' | 'excel' | 'csv';
  is_active: boolean;
  last_sent_at?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface AuditLog {
  id: string;
  action: string;
  resource_type: string;
  resource_id: string;
  user_id?: string;
  metadata: Record<string, any>;
  created_at: string;
}

// API Response Types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// Chart Configuration Types
export interface ChartConfig {
  type: 'bar' | 'line' | 'pie' | 'scatter' | 'area' | 'table';
  data_mapping: {
    x?: string;
    y?: string;
    color?: string;
    size?: string;
    label?: string;
  };
  styling: {
    colors?: string[];
    theme?: 'light' | 'dark';
    title?: string;
    subtitle?: string;
    legend?: boolean;
    grid?: boolean;
  };
  filters?: Array<{
    column: string;
    operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains';
    value: any;
  }>;
}

// Data Source Connection Configs
export interface CSVConnectionConfig {
  file_path: string;
  delimiter?: string;
  has_header?: boolean;
  encoding?: string;
}

export interface DatabaseConnectionConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl?: boolean;
}

export interface APIConnectionConfig {
  url: string;
  method: 'GET' | 'POST';
  headers?: Record<string, string>;
  auth?: {
    type: 'bearer' | 'basic' | 'api_key';
    token?: string;
    username?: string;
    password?: string;
    api_key?: string;
    api_key_header?: string;
  };
  params?: Record<string, string>;
}

// Webhook Types
export interface SlackWebhookPayload {
  text: string;
  username?: string;
  icon_emoji?: string;
  channel?: string;
  attachments?: Array<{
    color?: string;
    title?: string;
    text?: string;
    fields?: Array<{
      title: string;
      value: string;
      short?: boolean;
    }>;
  }>;
}

export interface TeamsWebhookPayload {
  '@type': 'MessageCard';
  '@context': 'http://schema.org/extensions';
  themeColor?: string;
  summary: string;
  sections: Array<{
    activityTitle?: string;
    activitySubtitle?: string;
    activityImage?: string;
    facts?: Array<{
      name: string;
      value: string;
    }>;
    markdown?: boolean;
  }>;
  potentialAction?: Array<{
    '@type': 'OpenUri';
    name: string;
    targets: Array<{
      os: 'default';
      uri: string;
    }>;
  }>;
}
