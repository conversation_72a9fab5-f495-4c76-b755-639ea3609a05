import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, canViewDashboard } from '@/lib/auth/auth';
import { DatabaseService } from '@/lib/services/database';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth();
    
    // Check if user can view this dashboard
    const dashboardResult = await DatabaseService.getDashboard(params.id);
    
    if (dashboardResult.error || !dashboardResult.data) {
      return NextResponse.json({ error: 'Dashboard not found' }, { status: 404 });
    }

    const permissions = (dashboardResult.data as any).permissions || [];
    if (!canViewDashboard(user, dashboardResult.data, permissions)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Get comments for this dashboard
    const result = await DatabaseService.getComments(params.id);
    
    if (result.error) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    return NextResponse.json({ data: result.data });

  } catch (error) {
    console.error('Get comments error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch comments' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth();
    
    // Check if user can view this dashboard
    const dashboardResult = await DatabaseService.getDashboard(params.id);
    
    if (dashboardResult.error || !dashboardResult.data) {
      return NextResponse.json({ error: 'Dashboard not found' }, { status: 404 });
    }

    const permissions = (dashboardResult.data as any).permissions || [];
    if (!canViewDashboard(user, dashboardResult.data, permissions)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const body = await request.json();
    const { content, chart_id, parent_id } = body;

    // Validate required fields
    if (!content || content.trim().length === 0) {
      return NextResponse.json({ error: 'Comment content is required' }, { status: 400 });
    }

    // Create comment
    const result = await DatabaseService.createComment({
      content: content.trim(),
      dashboard_id: params.id,
      chart_id: chart_id || undefined,
      user_id: user.id,
      parent_id: parent_id || undefined,
    });

    if (result.error) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    // Create audit log
    await DatabaseService.createAuditLog({
      action: 'comment_created',
      resource_type: 'comment',
      resource_id: result.data!.id,
      user_id: user.id,
      metadata: {
        dashboard_id: params.id,
        chart_id,
        parent_id,
        content_length: content.length,
      },
    });

    return NextResponse.json({
      data: result.data,
      message: 'Comment created successfully',
    });

  } catch (error) {
    console.error('Create comment error:', error);
    return NextResponse.json(
      { error: 'Failed to create comment' },
      { status: 500 }
    );
  }
}
