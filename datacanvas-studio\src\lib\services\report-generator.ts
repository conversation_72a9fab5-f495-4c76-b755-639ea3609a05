import { Dashboard, Chart, Dataset, Report } from '@/lib/types/database';
import { ChartConfig, ChartData } from '@/lib/charts/types';
import { DataProcessingService } from './data-processing';

export interface ReportOptions {
  format: 'pdf' | 'excel' | 'csv';
  includeCharts: boolean;
  includeData: boolean;
  dateRange?: {
    start: string;
    end: string;
  };
  filters?: any[];
}

export interface GeneratedReport {
  buffer: Buffer;
  filename: string;
  mimeType: string;
}

export class ReportGenerator {
  static async generateDashboardReport(
    dashboard: Dashboard,
    charts: Chart[],
    datasets: Dataset[],
    options: ReportOptions
  ): Promise<GeneratedReport> {
    switch (options.format) {
      case 'pdf':
        return this.generatePDFReport(dashboard, charts, datasets, options);
      case 'excel':
        return this.generateExcelReport(dashboard, charts, datasets, options);
      case 'csv':
        return this.generateCSVReport(dashboard, charts, datasets, options);
      default:
        throw new Error(`Unsupported report format: ${options.format}`);
    }
  }

  private static async generatePDFReport(
    dashboard: Dashboard,
    charts: Chart[],
    datasets: Dataset[],
    options: ReportOptions
  ): Promise<GeneratedReport> {
    // For a production app, you'd use a library like puppeteer or jsPDF
    // This is a simplified implementation
    
    const html = await this.generateHTMLReport(dashboard, charts, datasets, options);
    
    // In a real implementation, you'd convert HTML to PDF
    // For now, we'll return the HTML as a mock PDF
    const buffer = Buffer.from(html, 'utf-8');
    
    return {
      buffer,
      filename: `${dashboard.name.replace(/[^a-zA-Z0-9]/g, '_')}_report.pdf`,
      mimeType: 'application/pdf',
    };
  }

  private static async generateExcelReport(
    dashboard: Dashboard,
    charts: Chart[],
    datasets: Dataset[],
    options: ReportOptions
  ): Promise<GeneratedReport> {
    // For a production app, you'd use a library like exceljs
    // This is a simplified implementation
    
    const workbookData = await this.generateWorkbookData(dashboard, charts, datasets, options);
    
    // Mock Excel generation
    const buffer = Buffer.from(JSON.stringify(workbookData), 'utf-8');
    
    return {
      buffer,
      filename: `${dashboard.name.replace(/[^a-zA-Z0-9]/g, '_')}_report.xlsx`,
      mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    };
  }

  private static async generateCSVReport(
    dashboard: Dashboard,
    charts: Chart[],
    datasets: Dataset[],
    options: ReportOptions
  ): Promise<GeneratedReport> {
    const csvData = await this.generateCSVData(dashboard, charts, datasets, options);
    const buffer = Buffer.from(csvData, 'utf-8');
    
    return {
      buffer,
      filename: `${dashboard.name.replace(/[^a-zA-Z0-9]/g, '_')}_report.csv`,
      mimeType: 'text/csv',
    };
  }

  private static async generateHTMLReport(
    dashboard: Dashboard,
    charts: Chart[],
    datasets: Dataset[],
    options: ReportOptions
  ): Promise<string> {
    const chartData = await this.getChartsData(charts, datasets, options);
    
    let html = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>${dashboard.name} - Report</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 40px; }
          .header { border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
          .chart { margin-bottom: 40px; page-break-inside: avoid; }
          .chart-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
          .chart-description { color: #666; margin-bottom: 20px; }
          .data-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
          .data-table th, .data-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          .data-table th { background-color: #f2f2f2; }
          .metadata { margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>${dashboard.name}</h1>
          ${dashboard.description ? `<p>${dashboard.description}</p>` : ''}
          <p>Generated on: ${new Date().toLocaleString()}</p>
        </div>
    `;

    if (options.includeCharts) {
      for (const chart of charts) {
        const data = chartData[chart.id];
        if (!data) continue;

        html += `
          <div class="chart">
            <div class="chart-title">${chart.name}</div>
            ${chart.config.description ? `<div class="chart-description">${chart.config.description}</div>` : ''}
            
            <!-- Chart visualization would go here in a real implementation -->
            <div style="height: 300px; border: 1px solid #ddd; display: flex; align-items: center; justify-content: center; background-color: #f9f9f9;">
              <p>Chart: ${chart.type} (${data.data.length} data points)</p>
            </div>
            
            ${options.includeData ? this.generateDataTable(data) : ''}
          </div>
        `;
      }
    }

    html += `
        <div class="metadata">
          <p>Report Format: PDF</p>
          <p>Charts Included: ${options.includeCharts ? 'Yes' : 'No'}</p>
          <p>Data Included: ${options.includeData ? 'Yes' : 'No'}</p>
          ${options.dateRange ? `<p>Date Range: ${options.dateRange.start} to ${options.dateRange.end}</p>` : ''}
        </div>
      </body>
      </html>
    `;

    return html;
  }

  private static async generateWorkbookData(
    dashboard: Dashboard,
    charts: Chart[],
    datasets: Dataset[],
    options: ReportOptions
  ): Promise<any> {
    const chartData = await this.getChartsData(charts, datasets, options);
    
    const workbook = {
      name: dashboard.name,
      sheets: [
        {
          name: 'Summary',
          data: [
            ['Dashboard Name', dashboard.name],
            ['Description', dashboard.description || ''],
            ['Generated On', new Date().toISOString()],
            ['Number of Charts', charts.length],
            ['Report Format', 'Excel'],
          ],
        },
      ],
    };

    if (options.includeCharts) {
      for (const chart of charts) {
        const data = chartData[chart.id];
        if (!data) continue;

        const sheetData = [
          ['Chart Name', chart.name],
          ['Chart Type', chart.type],
          ['Data Points', data.data.length],
          [],
          // Headers
          data.fields.map(field => field.name),
          // Data rows
          ...data.data.map(row => 
            data.fields.map(field => row[field.name] || '')
          ),
        ];

        workbook.sheets.push({
          name: chart.name.substring(0, 31), // Excel sheet name limit
          data: sheetData,
        });
      }
    }

    return workbook;
  }

  private static async generateCSVData(
    dashboard: Dashboard,
    charts: Chart[],
    datasets: Dataset[],
    options: ReportOptions
  ): Promise<string> {
    let csv = `Dashboard Report: ${dashboard.name}\n`;
    csv += `Generated: ${new Date().toISOString()}\n\n`;

    if (options.includeCharts && charts.length > 0) {
      const chartData = await this.getChartsData(charts, datasets, options);
      
      for (const chart of charts) {
        const data = chartData[chart.id];
        if (!data) continue;

        csv += `\nChart: ${chart.name}\n`;
        csv += `Type: ${chart.type}\n`;
        
        if (options.includeData && data.data.length > 0) {
          // Headers
          csv += data.fields.map(field => `"${field.name}"`).join(',') + '\n';
          
          // Data rows
          for (const row of data.data) {
            const values = data.fields.map(field => {
              const value = row[field.name];
              return `"${value !== null && value !== undefined ? value : ''}"`;
            });
            csv += values.join(',') + '\n';
          }
        }
        
        csv += '\n';
      }
    }

    return csv;
  }

  private static generateDataTable(data: ChartData): string {
    if (!data.data.length) return '<p>No data available</p>';

    let table = '<table class="data-table"><thead><tr>';
    
    // Headers
    for (const field of data.fields.slice(0, 10)) { // Limit columns for readability
      table += `<th>${field.name}</th>`;
    }
    table += '</tr></thead><tbody>';

    // Data rows (limit to first 100 rows)
    for (const row of data.data.slice(0, 100)) {
      table += '<tr>';
      for (const field of data.fields.slice(0, 10)) {
        const value = row[field.name];
        table += `<td>${value !== null && value !== undefined ? value : ''}</td>`;
      }
      table += '</tr>';
    }

    table += '</tbody></table>';
    
    if (data.data.length > 100) {
      table += `<p><em>Showing first 100 rows of ${data.data.length} total rows</em></p>`;
    }

    return table;
  }

  private static async getChartsData(
    charts: Chart[],
    datasets: Dataset[],
    options: ReportOptions
  ): Promise<{ [chartId: string]: ChartData }> {
    const chartData: { [chartId: string]: ChartData } = {};

    for (const chart of charts) {
      const dataset = datasets.find(d => d.id === chart.dataset_id);
      if (!dataset) continue;

      try {
        // Get data for the chart
        const data = await DataProcessingService.getDatasetData(
          dataset,
          options.filters,
          10000, // Limit for reports
          0
        );

        chartData[chart.id] = {
          fields: dataset.schema_info?.columns?.map(col => ({
            name: col.name,
            type: col.type as any,
            nullable: col.nullable,
          })) || [],
          data: data || dataset.sample_data || [],
        };
      } catch (error) {
        console.error(`Failed to load data for chart ${chart.id}:`, error);
        // Use sample data as fallback
        chartData[chart.id] = {
          fields: dataset.schema_info?.columns?.map(col => ({
            name: col.name,
            type: col.type as any,
            nullable: col.nullable,
          })) || [],
          data: dataset.sample_data || [],
        };
      }
    }

    return chartData;
  }

  static async scheduleReport(report: Report): Promise<void> {
    // In a production app, you'd use a job queue like Bull or Agenda
    // For now, we'll just log the scheduling
    console.log(`Scheduling report: ${report.name}`);
    console.log(`Schedule: ${JSON.stringify(report.schedule_config)}`);
    console.log(`Recipients: ${report.recipients.join(', ')}`);
  }

  static async sendReport(
    report: Report,
    generatedReport: GeneratedReport,
    recipients: string[]
  ): Promise<void> {
    // In a production app, you'd use a service like SendGrid or AWS SES
    // For now, we'll just log the sending
    console.log(`Sending report: ${report.name}`);
    console.log(`Format: ${report.format}`);
    console.log(`File: ${generatedReport.filename}`);
    console.log(`Size: ${generatedReport.buffer.length} bytes`);
    console.log(`Recipients: ${recipients.join(', ')}`);
  }
}
