'use client';

import { useState } from 'react';
import { ChartType, CHART_TYPES, ChartTypeDefinition } from '@/lib/charts/types';
import { Dataset } from '@/lib/types/database';

interface ChartGalleryProps {
  dataset: Dataset;
  onSelectChart: (chartType: ChartType) => void;
  onCancel?: () => void;
}

export function ChartGallery({ dataset, onSelectChart, onCancel }: ChartGalleryProps) {
  const [selectedType, setSelectedType] = useState<ChartType | null>(null);
  const [filter, setFilter] = useState<string>('');

  const filteredChartTypes = CHART_TYPES.filter(chart =>
    chart.name.toLowerCase().includes(filter.toLowerCase()) ||
    chart.description.toLowerCase().includes(filter.toLowerCase())
  );

  const getCompatibilityScore = (chartType: ChartTypeDefinition): number => {
    if (!dataset.schema_info?.columns) return 0;

    const fields = dataset.schema_info.columns;
    let score = 0;
    let totalMappings = chartType.requiredMappings.length + chartType.optionalMappings.length;

    // Check how many mappings can be satisfied
    [...chartType.requiredMappings, ...chartType.optionalMappings].forEach(mapping => {
      const supportedTypes = chartType.supportedDataTypes[mapping];
      if (supportedTypes) {
        const hasCompatibleField = fields.some(field => 
          supportedTypes.includes(field.type as any)
        );
        if (hasCompatibleField) score++;
      } else {
        score++; // If no type restriction, assume compatible
      }
    });

    return totalMappings > 0 ? (score / totalMappings) * 100 : 100;
  };

  const getRecommendedFields = (chartType: ChartTypeDefinition) => {
    if (!dataset.schema_info?.columns) return {};

    const fields = dataset.schema_info.columns;
    const recommendations: Record<string, string> = {};

    [...chartType.requiredMappings, ...chartType.optionalMappings].forEach(mapping => {
      const supportedTypes = chartType.supportedDataTypes[mapping];
      if (supportedTypes) {
        const compatibleField = fields.find(field => 
          supportedTypes.includes(field.type as any)
        );
        if (compatibleField) {
          recommendations[mapping] = compatibleField.name;
        }
      }
    });

    return recommendations;
  };

  const handleSelectChart = () => {
    if (selectedType) {
      onSelectChart(selectedType);
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Choose a Chart Type</h2>
        <p className="text-gray-600">
          Select the best visualization for your data: <strong>{dataset.name}</strong>
        </p>
      </div>

      {/* Search Filter */}
      <div className="mb-6">
        <input
          type="text"
          placeholder="Search chart types..."
          value={filter}
          onChange={(e) => setFilter(e.target.value)}
          className="w-full max-w-md px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      {/* Chart Type Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {filteredChartTypes.map((chartType) => {
          const compatibilityScore = getCompatibilityScore(chartType);
          const recommendedFields = getRecommendedFields(chartType);
          const isSelected = selectedType === chartType.type;

          return (
            <div
              key={chartType.type}
              onClick={() => setSelectedType(chartType.type)}
              className={`
                relative p-6 border-2 rounded-lg cursor-pointer transition-all
                ${isSelected 
                  ? 'border-blue-500 bg-blue-50 shadow-md' 
                  : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
                }
              `}
            >
              {/* Compatibility Badge */}
              <div className="absolute top-3 right-3">
                <div className={`
                  px-2 py-1 rounded-full text-xs font-medium
                  ${compatibilityScore >= 80 
                    ? 'bg-green-100 text-green-800' 
                    : compatibilityScore >= 50 
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'bg-red-100 text-red-800'
                  }
                `}>
                  {compatibilityScore.toFixed(0)}% match
                </div>
              </div>

              {/* Chart Icon and Info */}
              <div className="mb-4">
                <div className="text-4xl mb-3">{chartType.icon}</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-1">
                  {chartType.name}
                </h3>
                <p className="text-sm text-gray-600">
                  {chartType.description}
                </p>
              </div>

              {/* Requirements */}
              <div className="mb-4">
                <div className="text-xs font-medium text-gray-700 mb-2">Required:</div>
                <div className="flex flex-wrap gap-1">
                  {chartType.requiredMappings.map(mapping => (
                    <span
                      key={mapping}
                      className={`
                        px-2 py-1 rounded text-xs
                        ${recommendedFields[mapping]
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                        }
                      `}
                    >
                      {mapping.toUpperCase()}
                      {recommendedFields[mapping] && (
                        <span className="ml-1 font-medium">
                          ({recommendedFields[mapping]})
                        </span>
                      )}
                    </span>
                  ))}
                </div>
              </div>

              {/* Optional Mappings */}
              {chartType.optionalMappings.length > 0 && (
                <div>
                  <div className="text-xs font-medium text-gray-700 mb-2">Optional:</div>
                  <div className="flex flex-wrap gap-1">
                    {chartType.optionalMappings.map(mapping => (
                      <span
                        key={mapping}
                        className={`
                          px-2 py-1 rounded text-xs
                          ${recommendedFields[mapping]
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-gray-100 text-gray-600'
                          }
                        `}
                      >
                        {mapping.toUpperCase()}
                        {recommendedFields[mapping] && (
                          <span className="ml-1 font-medium">
                            ({recommendedFields[mapping]})
                          </span>
                        )}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Selection Indicator */}
              {isSelected && (
                <div className="absolute inset-0 border-2 border-blue-500 rounded-lg pointer-events-none">
                  <div className="absolute top-2 left-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-4">
        {onCancel && (
          <button
            onClick={onCancel}
            className="px-6 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Cancel
          </button>
        )}
        <button
          onClick={handleSelectChart}
          disabled={!selectedType}
          className="px-6 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Create Chart
        </button>
      </div>

      {/* Dataset Info */}
      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 mb-2">Dataset Information</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Rows:</span>
            <span className="ml-2 font-medium">{dataset.row_count?.toLocaleString()}</span>
          </div>
          <div>
            <span className="text-gray-600">Columns:</span>
            <span className="ml-2 font-medium">{dataset.schema_info?.columns?.length}</span>
          </div>
          <div>
            <span className="text-gray-600">Numbers:</span>
            <span className="ml-2 font-medium">
              {dataset.schema_info?.columns?.filter(c => c.type === 'number' || c.type === 'integer' || c.type === 'decimal').length}
            </span>
          </div>
          <div>
            <span className="text-gray-600">Text:</span>
            <span className="ml-2 font-medium">
              {dataset.schema_info?.columns?.filter(c => c.type === 'string' || c.type === 'text').length}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
