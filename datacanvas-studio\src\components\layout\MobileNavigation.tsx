'use client';

import { useState } from 'react';
import { User } from '@/lib/types/database';
import { 
  Menu, 
  X, 
  Home, 
  BarChart3, 
  Database, 
  FileText, 
  Users, 
  Settings, 
  LogOut,
  Plus,
  Search
} from 'lucide-react';
import { BrandedLogo, AppName } from '@/components/branding/BrandingProvider';
import { RoleBasedMenu, PermissionWrapper } from '@/components/auth/RoleGuard';

interface MobileNavigationProps {
  user: User;
  currentPath: string;
  onSignOut: () => void;
}

export function MobileNavigation({ user, currentPath, onSignOut }: MobileNavigationProps) {
  const [isOpen, setIsOpen] = useState(false);

  const navigationItems = [
    { href: '/dashboard', icon: Home, label: 'Dashboard', roles: ['viewer', 'editor', 'admin'] },
    { href: '/dashboards', icon: BarChart3, label: 'Dashboards', roles: ['viewer', 'editor', 'admin'] },
    { href: '/data-sources', icon: Database, label: 'Data Sources', roles: ['editor', 'admin'] },
    { href: '/reports', icon: FileText, label: 'Reports', roles: ['viewer', 'editor', 'admin'] },
    { href: '/users', icon: Users, label: 'Users', roles: ['admin'] },
    { href: '/settings', icon: Settings, label: 'Settings', roles: ['admin'] },
  ];

  const isActive = (href: string) => {
    return currentPath === href || currentPath.startsWith(href + '/');
  };

  const closeMenu = () => setIsOpen(false);

  return (
    <>
      {/* Mobile Header */}
      <div className="lg:hidden bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <BrandedLogo 
            className="h-8 w-auto" 
            fallback={<AppName className="text-xl font-bold text-gray-900" />}
          />
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
            title="Search"
          >
            <Search className="h-5 w-5" />
          </button>
          <button
            onClick={() => setIsOpen(true)}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
            aria-label="Open menu"
          >
            <Menu className="h-6 w-6" />
          </button>
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      {isOpen && (
        <div className="lg:hidden fixed inset-0 z-50 flex">
          {/* Backdrop */}
          <div 
            className="fixed inset-0 bg-black bg-opacity-50"
            onClick={closeMenu}
          />
          
          {/* Menu Panel */}
          <div className="relative flex flex-col w-80 max-w-xs bg-white shadow-xl">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <BrandedLogo 
                  className="h-8 w-auto" 
                  fallback={<AppName className="text-xl font-bold text-gray-900" />}
                />
              </div>
              <button
                onClick={closeMenu}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
                aria-label="Close menu"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            {/* User Info */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  {user.avatar_url ? (
                    <img
                      src={user.avatar_url}
                      alt={user.full_name || user.email}
                      className="h-10 w-10 rounded-full"
                    />
                  ) : (
                    <div className="h-10 w-10 bg-gray-300 rounded-full flex items-center justify-center">
                      <Users className="h-5 w-5 text-gray-600" />
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {user.full_name || 'No name'}
                  </p>
                  <p className="text-xs text-gray-500 truncate">{user.email}</p>
                  <p className="text-xs text-blue-600 capitalize">{user.role}</p>
                </div>
              </div>
            </div>

            {/* Navigation */}
            <nav className="flex-1 px-4 py-4 space-y-1 overflow-y-auto">
              {navigationItems.map((item) => (
                <PermissionWrapper
                  key={item.href}
                  user={user}
                  requiredRole={item.roles[0] as any}
                >
                  <a
                    href={item.href}
                    onClick={closeMenu}
                    className={`
                      flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors
                      ${isActive(item.href)
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                      }
                    `}
                  >
                    <item.icon className="mr-3 h-5 w-5" />
                    {item.label}
                  </a>
                </PermissionWrapper>
              ))}
            </nav>

            {/* Quick Actions */}
            <div className="p-4 border-t border-gray-200">
              <div className="space-y-2">
                <PermissionWrapper user={user} requiredRole="editor">
                  <button
                    onClick={closeMenu}
                    className="w-full flex items-center px-3 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    New Dashboard
                  </button>
                </PermissionWrapper>
                
                <button
                  onClick={() => {
                    closeMenu();
                    onSignOut();
                  }}
                  className="w-full flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md"
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  Sign Out
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

// Mobile-friendly bottom navigation for key actions
export function MobileBottomNavigation({ user, currentPath }: { user: User; currentPath: string }) {
  const bottomNavItems = [
    { href: '/dashboard', icon: Home, label: 'Home' },
    { href: '/dashboards', icon: BarChart3, label: 'Dashboards' },
    { href: '/data-sources', icon: Database, label: 'Data', roles: ['editor', 'admin'] },
    { href: '/reports', icon: FileText, label: 'Reports' },
  ];

  const isActive = (href: string) => {
    return currentPath === href || currentPath.startsWith(href + '/');
  };

  return (
    <div className="lg:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-2 py-1 z-40">
      <div className="flex items-center justify-around">
        {bottomNavItems.map((item) => {
          // Check if user has permission for this item
          if (item.roles && !item.roles.includes(user.role)) {
            return null;
          }

          return (
            <a
              key={item.href}
              href={item.href}
              className={`
                flex flex-col items-center px-3 py-2 rounded-md text-xs font-medium transition-colors min-w-0
                ${isActive(item.href)
                  ? 'text-blue-600'
                  : 'text-gray-600 hover:text-gray-900'
                }
              `}
            >
              <item.icon className={`h-5 w-5 mb-1 ${isActive(item.href) ? 'text-blue-600' : ''}`} />
              <span className="truncate">{item.label}</span>
            </a>
          );
        })}
      </div>
    </div>
  );
}

// Mobile-friendly floating action button
export function MobileFAB({ user, onAction }: { user: User; onAction: () => void }) {
  if (!['editor', 'admin'].includes(user.role)) {
    return null;
  }

  return (
    <button
      onClick={onAction}
      className="lg:hidden fixed bottom-20 right-4 w-14 h-14 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 z-30 flex items-center justify-center"
      aria-label="Create new dashboard"
    >
      <Plus className="h-6 w-6" />
    </button>
  );
}
