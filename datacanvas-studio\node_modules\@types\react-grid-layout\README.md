# Installation
> `npm install --save @types/react-grid-layout`

# Summary
This package contains type definitions for react-grid-layout (https://github.com/STRML/react-grid-layout).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-grid-layout.

### Additional Details
 * Last updated: Tue, 07 Nov 2023 09:09:39 GMT
 * Dependencies: [@types/react](https://npmjs.com/package/@types/react)

# Credits
These definitions were written by [<PERSON>](https://github.com/abirkholz), [<PERSON>](https://github.com/alitaheri), [<PERSON><PERSON><PERSON>](https://github.com/<PERSON>heyang<PERSON>ong), [<PERSON>](https://github.com/andrewhathaway), [<PERSON><PERSON>](https://github.com/manav-m), and [<PERSON><PERSON>](https://github.com/al-fyo<PERSON><PERSON>).
