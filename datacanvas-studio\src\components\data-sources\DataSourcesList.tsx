'use client';

import { useState, useEffect } from 'react';
import { Database, FileText, Globe, Plus, Settings, Trash2 } from 'lucide-react';
import { DataSource } from '@/lib/types/database';

interface DataSourcesListProps {
  onAddNew?: () => void;
  onEdit?: (dataSource: DataSource) => void;
  onDelete?: (dataSource: DataSource) => void;
}

export function DataSourcesList({ onAddNew, onEdit, onDelete }: DataSourcesListProps) {
  const [dataSources, setDataSources] = useState<DataSource[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchDataSources();
  }, []);

  const fetchDataSources = async () => {
    try {
      const response = await fetch('/api/data-sources');
      const result = await response.json();

      if (response.ok) {
        setDataSources(result.data || []);
      } else {
        setError(result.error || 'Failed to fetch data sources');
      }
    } catch (err) {
      setError('Failed to fetch data sources');
    } finally {
      setIsLoading(false);
    }
  };

  const getDataSourceIcon = (type: string) => {
    switch (type) {
      case 'csv':
        return <FileText className="h-5 w-5" />;
      case 'postgresql':
      case 'mysql':
        return <Database className="h-5 w-5" />;
      case 'api':
        return <Globe className="h-5 w-5" />;
      default:
        return <Database className="h-5 w-5" />;
    }
  };

  const getDataSourceTypeLabel = (type: string) => {
    switch (type) {
      case 'csv':
        return 'CSV File';
      case 'postgresql':
        return 'PostgreSQL';
      case 'mysql':
        return 'MySQL';
      case 'api':
        return 'REST API';
      default:
        return type.toUpperCase();
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <p className="text-sm text-red-800">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Data Sources</h2>
        <button
          onClick={onAddNew}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Data Source
        </button>
      </div>

      {dataSources.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No data sources yet</h3>
          <p className="text-gray-600 mb-4">
            Connect to your first data source to start creating visualizations.
          </p>
          <button
            onClick={onAddNew}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Your First Data Source
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {dataSources.map((dataSource) => (
            <div
              key={dataSource.id}
              className="bg-white rounded-lg shadow-md border border-gray-200 hover:shadow-lg transition-shadow"
            >
              <div className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0 p-2 bg-blue-100 rounded-lg">
                      {getDataSourceIcon(dataSource.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-medium text-gray-900 truncate">
                        {dataSource.name}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {getDataSourceTypeLabel(dataSource.type)}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <div className={`
                      w-2 h-2 rounded-full
                      ${dataSource.is_active ? 'bg-green-400' : 'bg-red-400'}
                    `} />
                  </div>
                </div>

                <div className="mt-4">
                  <p className="text-sm text-gray-600">
                    Created {new Date(dataSource.created_at).toLocaleDateString()}
                  </p>
                </div>

                <div className="mt-6 flex items-center justify-between">
                  <span className={`
                    inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                    ${dataSource.is_active 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                    }
                  `}>
                    {dataSource.is_active ? 'Active' : 'Inactive'}
                  </span>

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => onEdit?.(dataSource)}
                      className="p-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-md"
                      title="Edit data source"
                    >
                      <Settings className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => onDelete?.(dataSource)}
                      className="p-2 text-gray-400 hover:text-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 rounded-md"
                      title="Delete data source"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
