'use client';

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';

interface BrandingConfig {
  primary_color?: string;
  secondary_color?: string;
  logo_url?: string;
  favicon_url?: string;
  company_name?: string;
  app_name?: string;
  theme?: 'light' | 'dark' | 'auto';
  custom_css?: string;
  hide_powered_by?: boolean;
  custom_domain?: string;
}

interface BrandingContextType {
  branding: BrandingConfig;
  isLoading: boolean;
  updateBranding: (newBranding: BrandingConfig) => void;
}

const BrandingContext = createContext<BrandingContextType | undefined>(undefined);

interface BrandingProviderProps {
  children: ReactNode;
  organizationId?: string;
  initialBranding?: BrandingConfig;
}

export function BrandingProvider({ 
  children, 
  organizationId, 
  initialBranding = {} 
}: BrandingProviderProps) {
  const [branding, setBranding] = useState<BrandingConfig>(initialBranding);
  const [isLoading, setIsLoading] = useState(!!organizationId);

  useEffect(() => {
    if (organizationId) {
      loadBranding();
    }
  }, [organizationId]);

  useEffect(() => {
    applyBranding();
  }, [branding]);

  const loadBranding = async () => {
    if (!organizationId) return;

    try {
      const response = await fetch(`/api/organizations/${organizationId}/branding`);
      const result = await response.json();

      if (response.ok && result.data?.custom_branding) {
        setBranding(result.data.custom_branding);
      }
    } catch (error) {
      console.error('Failed to load branding:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const applyBranding = () => {
    const root = document.documentElement;

    // Apply CSS custom properties
    if (branding.primary_color) {
      root.style.setProperty('--primary-color', branding.primary_color);
      root.style.setProperty('--primary-50', hexToHsl(branding.primary_color, 95));
      root.style.setProperty('--primary-100', hexToHsl(branding.primary_color, 90));
      root.style.setProperty('--primary-500', branding.primary_color);
      root.style.setProperty('--primary-600', hexToHsl(branding.primary_color, -10));
      root.style.setProperty('--primary-700', hexToHsl(branding.primary_color, -20));
    }

    if (branding.secondary_color) {
      root.style.setProperty('--secondary-color', branding.secondary_color);
    }

    // Apply theme
    if (branding.theme) {
      if (branding.theme === 'auto') {
        // Use system preference
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        root.classList.toggle('dark', prefersDark);
      } else {
        root.classList.toggle('dark', branding.theme === 'dark');
      }
    }

    // Update document title
    if (branding.app_name) {
      document.title = branding.app_name;
    }

    // Update favicon
    if (branding.favicon_url) {
      updateFavicon(branding.favicon_url);
    }

    // Apply custom CSS
    if (branding.custom_css) {
      applyCustomCSS(branding.custom_css);
    }
  };

  const updateBranding = (newBranding: BrandingConfig) => {
    setBranding(newBranding);
  };

  return (
    <BrandingContext.Provider value={{ branding, isLoading, updateBranding }}>
      {children}
    </BrandingContext.Provider>
  );
}

export function useBranding() {
  const context = useContext(BrandingContext);
  if (context === undefined) {
    throw new Error('useBranding must be used within a BrandingProvider');
  }
  return context;
}

// Utility functions
function hexToHsl(hex: string, lightnessAdjustment: number = 0): string {
  // Convert hex to RGB
  const r = parseInt(hex.slice(1, 3), 16) / 255;
  const g = parseInt(hex.slice(3, 5), 16) / 255;
  const b = parseInt(hex.slice(5, 7), 16) / 255;

  // Convert RGB to HSL
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;
  let s = 0;
  let l = (max + min) / 2;

  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }

  // Apply lightness adjustment
  l = Math.max(0, Math.min(1, l + lightnessAdjustment / 100));

  // Convert back to hex
  const hslToRgb = (h: number, s: number, l: number) => {
    let r, g, b;

    if (s === 0) {
      r = g = b = l;
    } else {
      const hue2rgb = (p: number, q: number, t: number) => {
        if (t < 0) t += 1;
        if (t > 1) t -= 1;
        if (t < 1/6) return p + (q - p) * 6 * t;
        if (t < 1/2) return q;
        if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
        return p;
      };

      const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
      const p = 2 * l - q;
      r = hue2rgb(p, q, h + 1/3);
      g = hue2rgb(p, q, h);
      b = hue2rgb(p, q, h - 1/3);
    }

    const toHex = (c: number) => {
      const hex = Math.round(c * 255).toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    };

    return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
  };

  return hslToRgb(h, s, l);
}

function updateFavicon(faviconUrl: string) {
  // Remove existing favicon
  const existingFavicon = document.querySelector('link[rel="icon"]');
  if (existingFavicon) {
    existingFavicon.remove();
  }

  // Add new favicon
  const link = document.createElement('link');
  link.rel = 'icon';
  link.href = faviconUrl;
  document.head.appendChild(link);
}

function applyCustomCSS(css: string) {
  // Remove existing custom CSS
  const existingStyle = document.getElementById('custom-branding-css');
  if (existingStyle) {
    existingStyle.remove();
  }

  // Add new custom CSS
  const style = document.createElement('style');
  style.id = 'custom-branding-css';
  style.textContent = css;
  document.head.appendChild(style);
}

// Component for displaying branded logo
export function BrandedLogo({ className = '', fallback }: { className?: string; fallback?: ReactNode }) {
  const { branding } = useBranding();

  if (branding.logo_url) {
    return (
      <img
        src={branding.logo_url}
        alt={branding.company_name || branding.app_name || 'Logo'}
        className={className}
        onError={(e) => {
          (e.target as HTMLImageElement).style.display = 'none';
        }}
      />
    );
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  return (
    <div className={`font-bold text-xl ${className}`}>
      {branding.app_name || 'DataCanvas Studio'}
    </div>
  );
}

// Component for displaying app name
export function AppName({ className = '' }: { className?: string }) {
  const { branding } = useBranding();
  
  return (
    <span className={className}>
      {branding.app_name || 'DataCanvas Studio'}
    </span>
  );
}

// Component for displaying company name
export function CompanyName({ className = '' }: { className?: string }) {
  const { branding } = useBranding();
  
  if (!branding.company_name) return null;
  
  return (
    <span className={className}>
      {branding.company_name}
    </span>
  );
}

// Component for powered by footer
export function PoweredBy({ className = '' }: { className?: string }) {
  const { branding } = useBranding();
  
  if (branding.hide_powered_by) return null;
  
  return (
    <div className={`text-xs text-gray-500 ${className}`}>
      Powered by <span className="font-medium">DataCanvas Studio</span>
    </div>
  );
}
