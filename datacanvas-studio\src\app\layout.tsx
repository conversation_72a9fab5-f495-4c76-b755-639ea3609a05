import type { Metadata, Viewport } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import "../styles/mobile.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "DataCanvas Studio",
  description: "Professional data visualization and dashboard platform",
  keywords: ["data visualization", "dashboards", "analytics", "business intelligence"],
  authors: [{ name: "DataCanvas Studio" }],
  creator: "DataCanvas Studio",
  publisher: "DataCanvas Studio",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  icons: {
    icon: "/favicon.ico",
    shortcut: "/favicon-16x16.png",
    apple: "/apple-touch-icon.png",
  },
  manifest: "/site.webmanifest",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "DataCanvas Studio",
  },
  openGraph: {
    type: "website",
    siteName: "DataCanvas Studio",
    title: "DataCanvas Studio - Professional Data Visualization",
    description: "Create stunning dashboards and visualizations with our professional data platform",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#ffffff" },
    { media: "(prefers-color-scheme: dark)", color: "#1f2937" },
  ],
  colorScheme: "light dark",
  viewportFit: "cover",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="h-full">
      <head>
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="DataCanvas" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="application-name" content="DataCanvas Studio" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="HandheldFriendly" content="true" />
        <meta name="MobileOptimized" content="width" />
      </head>
      <body className={`${inter.className} h-full bg-gray-50 antialiased`}>
        <a
          href="#main-content"
          className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-md z-50"
        >
          Skip to main content
        </a>

        <div id="main-content" className="h-full">
          {children}
        </div>
      </body>
    </html>
  );
}
