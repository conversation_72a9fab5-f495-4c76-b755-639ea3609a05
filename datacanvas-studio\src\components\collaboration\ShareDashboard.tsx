'use client';

import { useState, useEffect } from 'react';
import { Share2, Users, Globe, Lock, Copy, Plus, Trash2, Check } from 'lucide-react';

interface ShareDashboardProps {
  dashboardId: string;
  isOpen: boolean;
  onClose: () => void;
}

interface Permission {
  user_id: string;
  permission: 'view' | 'edit' | 'admin';
  user: {
    email: string;
    full_name?: string;
  };
}

interface SharingInfo {
  is_public: boolean;
  permissions: Permission[];
  share_url?: string;
}

export function ShareDashboard({ dashboardId, isOpen, onClose }: ShareDashboardProps) {
  const [sharingInfo, setSharingInfo] = useState<SharingInfo | null>(null);
  const [newUserEmail, setNewUserEmail] = useState('');
  const [newUserPermission, setNewUserPermission] = useState<'view' | 'edit'>('view');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [copiedUrl, setCopiedUrl] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadSharingInfo();
    }
  }, [isOpen, dashboardId]);

  const loadSharingInfo = async () => {
    setIsLoading(true);
    setError('');
    try {
      const response = await fetch(`/api/dashboards/${dashboardId}/share`);
      const result = await response.json();

      if (response.ok) {
        setSharingInfo(result.data);
      } else {
        setError(result.error || 'Failed to load sharing information');
      }
    } catch (err) {
      setError('Failed to load sharing information');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTogglePublic = async () => {
    setIsSubmitting(true);
    setError('');
    try {
      const response = await fetch(`/api/dashboards/${dashboardId}/share`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'toggle_public' }),
      });

      const result = await response.json();

      if (response.ok) {
        setSharingInfo(prev => prev ? {
          ...prev,
          is_public: result.data.is_public,
          share_url: result.data.share_url,
        } : null);
      } else {
        setError(result.error || 'Failed to update public access');
      }
    } catch (err) {
      setError('Failed to update public access');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAddUser = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newUserEmail.trim()) return;

    setIsSubmitting(true);
    setError('');
    try {
      const response = await fetch(`/api/dashboards/${dashboardId}/share`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'add_user',
          user_email: newUserEmail.trim(),
          permission: newUserPermission,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        // Reload sharing info to get updated permissions
        await loadSharingInfo();
        setNewUserEmail('');
        setNewUserPermission('view');
      } else {
        setError(result.error || 'Failed to add user');
      }
    } catch (err) {
      setError('Failed to add user');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRemoveUser = async (userEmail: string) => {
    if (!confirm(`Remove access for ${userEmail}?`)) return;

    setIsSubmitting(true);
    setError('');
    try {
      const response = await fetch(`/api/dashboards/${dashboardId}/share`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'remove_user',
          user_email: userEmail,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        // Reload sharing info to get updated permissions
        await loadSharingInfo();
      } else {
        setError(result.error || 'Failed to remove user');
      }
    } catch (err) {
      setError('Failed to remove user');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCopyUrl = async () => {
    if (sharingInfo?.share_url) {
      try {
        await navigator.clipboard.writeText(sharingInfo.share_url);
        setCopiedUrl(true);
        setTimeout(() => setCopiedUrl(false), 2000);
      } catch (err) {
        console.error('Failed to copy URL:', err);
      }
    }
  };

  const getPermissionLabel = (permission: string) => {
    switch (permission) {
      case 'view': return 'Can view';
      case 'edit': return 'Can edit';
      case 'admin': return 'Admin';
      default: return permission;
    }
  };

  const getPermissionColor = (permission: string) => {
    switch (permission) {
      case 'view': return 'bg-blue-100 text-blue-800';
      case 'edit': return 'bg-green-100 text-green-800';
      case 'admin': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <Share2 className="h-5 w-5 text-gray-600" />
            <h3 className="text-lg font-medium text-gray-900">Share Dashboard</h3>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 focus:outline-none"
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          )}

          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            </div>
          ) : sharingInfo ? (
            <>
              {/* Public Access */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {sharingInfo.is_public ? (
                      <Globe className="h-5 w-5 text-green-600" />
                    ) : (
                      <Lock className="h-5 w-5 text-gray-400" />
                    )}
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {sharingInfo.is_public ? 'Public Access' : 'Private Dashboard'}
                      </div>
                      <div className="text-xs text-gray-500">
                        {sharingInfo.is_public 
                          ? 'Anyone with the link can view this dashboard'
                          : 'Only people with access can view this dashboard'
                        }
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={handleTogglePublic}
                    disabled={isSubmitting}
                    className={`
                      relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                      ${sharingInfo.is_public ? 'bg-blue-600' : 'bg-gray-200'}
                    `}
                  >
                    <span
                      className={`
                        inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                        ${sharingInfo.is_public ? 'translate-x-6' : 'translate-x-1'}
                      `}
                    />
                  </button>
                </div>

                {/* Public URL */}
                {sharingInfo.is_public && sharingInfo.share_url && (
                  <div className="bg-gray-50 rounded-md p-3">
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="text-xs text-gray-500 mb-1">Public URL</div>
                        <div className="text-sm text-gray-900 truncate">
                          {sharingInfo.share_url}
                        </div>
                      </div>
                      <button
                        onClick={handleCopyUrl}
                        className="ml-2 p-2 text-gray-400 hover:text-gray-600 focus:outline-none"
                      >
                        {copiedUrl ? (
                          <Check className="h-4 w-4 text-green-600" />
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* User Permissions */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Users className="h-5 w-5 text-gray-600" />
                  <h4 className="text-sm font-medium text-gray-900">People with access</h4>
                </div>

                {/* Add User Form */}
                <form onSubmit={handleAddUser} className="flex space-x-2">
                  <input
                    type="email"
                    value={newUserEmail}
                    onChange={(e) => setNewUserEmail(e.target.value)}
                    placeholder="Enter email address"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <select
                    value={newUserPermission}
                    onChange={(e) => setNewUserPermission(e.target.value as 'view' | 'edit')}
                    className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="view">Can view</option>
                    <option value="edit">Can edit</option>
                  </select>
                  <button
                    type="submit"
                    disabled={!newUserEmail.trim() || isSubmitting}
                    className="px-3 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Plus className="h-4 w-4" />
                  </button>
                </form>

                {/* User List */}
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {sharingInfo.permissions.map((permission) => (
                    <div
                      key={permission.user_id}
                      className="flex items-center justify-between p-2 bg-gray-50 rounded-md"
                    >
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-gray-900 truncate">
                          {permission.user.full_name || permission.user.email}
                        </div>
                        {permission.user.full_name && (
                          <div className="text-xs text-gray-500 truncate">
                            {permission.user.email}
                          </div>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`
                          px-2 py-1 rounded-full text-xs font-medium
                          ${getPermissionColor(permission.permission)}
                        `}>
                          {getPermissionLabel(permission.permission)}
                        </span>
                        <button
                          onClick={() => handleRemoveUser(permission.user.email)}
                          disabled={isSubmitting}
                          className="p-1 text-gray-400 hover:text-red-600 focus:outline-none"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                  
                  {sharingInfo.permissions.length === 0 && (
                    <div className="text-center py-4 text-gray-500 text-sm">
                      No users have been granted access yet
                    </div>
                  )}
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
