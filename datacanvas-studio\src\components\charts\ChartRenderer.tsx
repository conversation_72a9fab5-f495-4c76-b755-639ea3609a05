'use client';

import { useMemo } from 'react';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell,
  Scatter<PERSON>hart,
  Scatter,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { ChartConfig, ChartData, DEFAULT_COLORS } from '@/lib/charts/types';

interface ChartRendererProps {
  config: ChartConfig;
  data: ChartData;
  width?: number;
  height?: number;
}

export function ChartRenderer({ config, data, width, height }: ChartRendererProps) {
  const chartData = useMemo(() => {
    if (!data.data || data.data.length === 0) return [];

    // Transform data based on chart type and mapping
    return data.data.map(row => {
      const transformedRow: any = {};
      
      // Map fields according to chart configuration
      if (config.mapping.x) {
        transformedRow.x = row[config.mapping.x];
      }
      if (config.mapping.y) {
        transformedRow.y = row[config.mapping.y];
      }
      if (config.mapping.color) {
        transformedRow.color = row[config.mapping.color];
      }
      if (config.mapping.size) {
        transformedRow.size = row[config.mapping.size];
      }
      if (config.mapping.label) {
        transformedRow.label = row[config.mapping.label];
      }
      if (config.mapping.series) {
        transformedRow.series = row[config.mapping.series];
      }

      // Keep original row data for reference
      transformedRow._original = row;
      
      return transformedRow;
    });
  }, [data, config.mapping]);

  const colors = config.style.colors || DEFAULT_COLORS;
  const theme = config.style.theme || 'light';

  const renderChart = () => {
    const commonProps = {
      data: chartData,
      margin: { top: 20, right: 30, left: 20, bottom: 20 },
    };

    switch (config.type) {
      case 'bar':
        return (
          <BarChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke={theme === 'dark' ? '#374151' : '#f3f4f6'} />
            <XAxis 
              dataKey="x" 
              stroke={theme === 'dark' ? '#9ca3af' : '#6b7280'}
              fontSize={12}
            />
            <YAxis 
              stroke={theme === 'dark' ? '#9ca3af' : '#6b7280'}
              fontSize={12}
            />
            {config.style.showTooltip && <Tooltip />}
            {config.style.showLegend && <Legend />}
            <Bar 
              dataKey="y" 
              fill={colors[0]}
              name={config.mapping.y || 'Value'}
            />
          </BarChart>
        );

      case 'line':
        return (
          <LineChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke={theme === 'dark' ? '#374151' : '#f3f4f6'} />
            <XAxis 
              dataKey="x" 
              stroke={theme === 'dark' ? '#9ca3af' : '#6b7280'}
              fontSize={12}
            />
            <YAxis 
              stroke={theme === 'dark' ? '#9ca3af' : '#6b7280'}
              fontSize={12}
            />
            {config.style.showTooltip && <Tooltip />}
            {config.style.showLegend && <Legend />}
            <Line 
              type="monotone" 
              dataKey="y" 
              stroke={colors[0]}
              strokeWidth={2}
              dot={{ fill: colors[0], strokeWidth: 2, r: 4 }}
              name={config.mapping.y || 'Value'}
            />
          </LineChart>
        );

      case 'area':
        return (
          <AreaChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke={theme === 'dark' ? '#374151' : '#f3f4f6'} />
            <XAxis 
              dataKey="x" 
              stroke={theme === 'dark' ? '#9ca3af' : '#6b7280'}
              fontSize={12}
            />
            <YAxis 
              stroke={theme === 'dark' ? '#9ca3af' : '#6b7280'}
              fontSize={12}
            />
            {config.style.showTooltip && <Tooltip />}
            {config.style.showLegend && <Legend />}
            <Area 
              type="monotone" 
              dataKey="y" 
              stroke={colors[0]}
              fill={colors[0]}
              fillOpacity={0.6}
              name={config.mapping.y || 'Value'}
            />
          </AreaChart>
        );

      case 'pie':
      case 'donut':
        return (
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ label, percent }) => `${label} ${(percent * 100).toFixed(0)}%`}
              outerRadius={config.type === 'donut' ? 80 : 100}
              innerRadius={config.type === 'donut' ? 40 : 0}
              fill="#8884d8"
              dataKey="y"
              nameKey="label"
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
              ))}
            </Pie>
            {config.style.showTooltip && <Tooltip />}
            {config.style.showLegend && <Legend />}
          </PieChart>
        );

      case 'scatter':
      case 'bubble':
        return (
          <ScatterChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke={theme === 'dark' ? '#374151' : '#f3f4f6'} />
            <XAxis 
              type="number"
              dataKey="x" 
              stroke={theme === 'dark' ? '#9ca3af' : '#6b7280'}
              fontSize={12}
              name={config.mapping.x || 'X'}
            />
            <YAxis 
              type="number"
              dataKey="y" 
              stroke={theme === 'dark' ? '#9ca3af' : '#6b7280'}
              fontSize={12}
              name={config.mapping.y || 'Y'}
            />
            {config.style.showTooltip && <Tooltip cursor={{ strokeDasharray: '3 3' }} />}
            {config.style.showLegend && <Legend />}
            <Scatter 
              name="Data Points" 
              data={chartData} 
              fill={colors[0]}
            />
          </ScatterChart>
        );

      case 'table':
        return (
          <div className="overflow-auto max-h-96">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className={theme === 'dark' ? 'bg-gray-800' : 'bg-gray-50'}>
                <tr>
                  {data.fields.slice(0, 10).map((field, index) => (
                    <th
                      key={index}
                      className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                        theme === 'dark' ? 'text-gray-300' : 'text-gray-500'
                      }`}
                    >
                      {field.name}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className={`divide-y ${theme === 'dark' ? 'bg-gray-900 divide-gray-700' : 'bg-white divide-gray-200'}`}>
                {data.data.slice(0, 100).map((row, rowIndex) => (
                  <tr key={rowIndex}>
                    {data.fields.slice(0, 10).map((field, colIndex) => (
                      <td
                        key={colIndex}
                        className={`px-6 py-4 whitespace-nowrap text-sm ${
                          theme === 'dark' ? 'text-gray-100' : 'text-gray-900'
                        }`}
                      >
                        {row[field.name]?.toString() || '-'}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        );

      case 'metric':
        const metricValue = chartData.length > 0 ? chartData[0].y : 0;
        const metricLabel = config.mapping.label ? chartData[0]?.label : config.name;
        
        return (
          <div className="flex flex-col items-center justify-center h-full">
            <div className="text-4xl font-bold text-gray-900 mb-2">
              {typeof metricValue === 'number' ? metricValue.toLocaleString() : metricValue}
            </div>
            {metricLabel && (
              <div className="text-lg text-gray-600">{metricLabel}</div>
            )}
          </div>
        );

      default:
        return (
          <div className="flex items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <div className="text-2xl mb-2">📊</div>
              <div>Chart type "{config.type}" not supported</div>
            </div>
          </div>
        );
    }
  };

  return (
    <div 
      className="w-full h-full"
      style={{ 
        backgroundColor: theme === 'dark' ? '#1f2937' : '#ffffff',
        color: theme === 'dark' ? '#f9fafb' : '#1f2937',
      }}
    >
      {config.style.title && (
        <div className="text-lg font-semibold text-center mb-2 px-4">
          {config.style.title}
        </div>
      )}
      {config.style.subtitle && (
        <div className="text-sm text-gray-600 text-center mb-4 px-4">
          {config.style.subtitle}
        </div>
      )}
      
      <div className="flex-1" style={{ width: width || '100%', height: height || 300 }}>
        {['table', 'metric'].includes(config.type) ? (
          renderChart()
        ) : (
          <ResponsiveContainer width="100%" height="100%">
            {renderChart()}
          </ResponsiveContainer>
        )}
      </div>
    </div>
  );
}
