import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth/auth';
import { DatabaseService } from '@/lib/services/database';
import { DataProcessingService } from '@/lib/services/data-processing';

export async function GET(request: NextRequest) {
  try {
    const user = await requireAuth();
    
    if (!user.organization_id) {
      return NextResponse.json({ error: 'User not associated with an organization' }, { status: 400 });
    }

    const result = await DatabaseService.getDataSources(user.organization_id);
    
    if (result.error) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    return NextResponse.json({ data: result.data });

  } catch (error) {
    console.error('Get data sources error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch data sources' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth();
    
    if (!user.organization_id) {
      return NextResponse.json({ error: 'User not associated with an organization' }, { status: 400 });
    }

    const body = await request.json();
    const { name, type, connection_config } = body;

    // Validate required fields
    if (!name || !type || !connection_config) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Validate data source type
    if (!['csv', 'postgresql', 'mysql', 'api'].includes(type)) {
      return NextResponse.json({ error: 'Invalid data source type' }, { status: 400 });
    }

    // Create data source
    const dataSourceResult = await DatabaseService.createDataSource({
      name,
      type,
      connection_config,
      organization_id: user.organization_id,
      created_by: user.id,
      is_active: true,
    });

    if (dataSourceResult.error) {
      return NextResponse.json({ error: dataSourceResult.error }, { status: 500 });
    }

    // Test connection and create dataset if successful
    try {
      const isValid = await DataProcessingService.validateDataSource(dataSourceResult.data!);
      
      if (!isValid) {
        return NextResponse.json({ error: 'Failed to connect to data source' }, { status: 400 });
      }

      // For database connections, we might want to create a dataset from a specific query
      // For API connections, we create a dataset from the API response
      let processedData;
      
      switch (type) {
        case 'postgresql':
          // For demo purposes, use a simple SELECT * FROM table query
          const pgQuery = connection_config.query || 'SELECT * FROM information_schema.tables LIMIT 10';
          processedData = await DataProcessingService.processPostgreSQLData(connection_config, pgQuery);
          break;
          
        case 'mysql':
          const mysqlQuery = connection_config.query || 'SELECT * FROM information_schema.tables LIMIT 10';
          processedData = await DataProcessingService.processMySQLData(connection_config, mysqlQuery);
          break;
          
        case 'api':
          processedData = await DataProcessingService.processAPIData(connection_config);
          break;
          
        default:
          processedData = { schema: { columns: [] }, sampleData: [], rowCount: 0 };
      }

      // Create dataset
      const datasetResult = await DatabaseService.createDataset({
        name: `${name} Dataset`,
        description: `Dataset from ${name} data source`,
        data_source_id: dataSourceResult.data!.id,
        schema_info: processedData.schema,
        sample_data: processedData.sampleData,
        row_count: processedData.rowCount,
        organization_id: user.organization_id,
        created_by: user.id,
      });

      if (datasetResult.error) {
        console.error('Dataset creation error:', datasetResult.error);
      }

      // Create audit log
      await DatabaseService.createAuditLog({
        action: 'data_source_created',
        resource_type: 'data_source',
        resource_id: dataSourceResult.data!.id,
        user_id: user.id,
        metadata: {
          name,
          type,
          row_count: processedData.rowCount,
        },
      });

      return NextResponse.json({
        data: {
          dataSource: dataSourceResult.data,
          dataset: datasetResult.data,
          preview: processedData.sampleData.slice(0, 10),
        },
        message: 'Data source created successfully',
      });

    } catch (connectionError) {
      console.error('Data source connection error:', connectionError);
      return NextResponse.json({ 
        error: 'Data source created but failed to establish connection or process data' 
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Create data source error:', error);
    return NextResponse.json(
      { error: 'Failed to create data source' },
      { status: 500 }
    );
  }
}
