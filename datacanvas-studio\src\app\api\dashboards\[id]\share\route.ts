import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, canEditDashboard } from '@/lib/auth/auth';
import { DatabaseService } from '@/lib/services/database';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth();
    
    // Get dashboard and check permissions
    const dashboardResult = await DatabaseService.getDashboard(params.id);
    
    if (dashboardResult.error || !dashboardResult.data) {
      return NextResponse.json({ error: 'Dashboard not found' }, { status: 404 });
    }

    const permissions = (dashboardResult.data as any).permissions || [];
    if (!canEditDashboard(user, dashboardResult.data, permissions)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Return sharing information
    return NextResponse.json({
      data: {
        is_public: dashboardResult.data.is_public,
        permissions: permissions,
        share_url: dashboardResult.data.is_public 
          ? `${process.env.NEXT_PUBLIC_APP_URL}/public/dashboard/${params.id}`
          : null,
      },
    });

  } catch (error) {
    console.error('Get sharing info error:', error);
    return NextResponse.json(
      { error: 'Failed to get sharing information' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth();
    
    // Get dashboard and check permissions
    const dashboardResult = await DatabaseService.getDashboard(params.id);
    
    if (dashboardResult.error || !dashboardResult.data) {
      return NextResponse.json({ error: 'Dashboard not found' }, { status: 404 });
    }

    const permissions = (dashboardResult.data as any).permissions || [];
    if (!canEditDashboard(user, dashboardResult.data, permissions)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const body = await request.json();
    const { action, user_email, permission } = body;

    const supabase = await import('@/lib/supabase/server').then(m => m.createSupabaseServerClient());

    if (action === 'add_user') {
      // Add user permission
      if (!user_email || !permission) {
        return NextResponse.json({ error: 'User email and permission are required' }, { status: 400 });
      }

      // Find user by email
      const { data: targetUser, error: userError } = await (await supabase)
        .from('users')
        .select('id, email, full_name')
        .eq('email', user_email)
        .single();

      if (userError || !targetUser) {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }

      // Check if permission already exists
      const { data: existingPermission } = await (await supabase)
        .from('dashboard_permissions')
        .select('id')
        .eq('dashboard_id', params.id)
        .eq('user_id', targetUser.id)
        .single();

      if (existingPermission) {
        // Update existing permission
        const { error: updateError } = await (await supabase)
          .from('dashboard_permissions')
          .update({ permission })
          .eq('dashboard_id', params.id)
          .eq('user_id', targetUser.id);

        if (updateError) {
          return NextResponse.json({ error: updateError.message }, { status: 500 });
        }
      } else {
        // Create new permission
        const { error: insertError } = await (await supabase)
          .from('dashboard_permissions')
          .insert({
            dashboard_id: params.id,
            user_id: targetUser.id,
            permission,
          });

        if (insertError) {
          return NextResponse.json({ error: insertError.message }, { status: 500 });
        }
      }

      // Create audit log
      await DatabaseService.createAuditLog({
        action: 'dashboard_shared',
        resource_type: 'dashboard',
        resource_id: params.id,
        user_id: user.id,
        metadata: {
          target_user_email: user_email,
          permission,
        },
      });

      return NextResponse.json({
        message: 'User access granted successfully',
      });

    } else if (action === 'remove_user') {
      // Remove user permission
      if (!user_email) {
        return NextResponse.json({ error: 'User email is required' }, { status: 400 });
      }

      // Find user by email
      const { data: targetUser, error: userError } = await (await supabase)
        .from('users')
        .select('id')
        .eq('email', user_email)
        .single();

      if (userError || !targetUser) {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }

      // Remove permission
      const { error: deleteError } = await (await supabase)
        .from('dashboard_permissions')
        .delete()
        .eq('dashboard_id', params.id)
        .eq('user_id', targetUser.id);

      if (deleteError) {
        return NextResponse.json({ error: deleteError.message }, { status: 500 });
      }

      // Create audit log
      await DatabaseService.createAuditLog({
        action: 'dashboard_unshared',
        resource_type: 'dashboard',
        resource_id: params.id,
        user_id: user.id,
        metadata: {
          target_user_email: user_email,
        },
      });

      return NextResponse.json({
        message: 'User access removed successfully',
      });

    } else if (action === 'toggle_public') {
      // Toggle public access
      const newPublicStatus = !dashboardResult.data.is_public;

      const updateResult = await DatabaseService.updateDashboard(params.id, {
        is_public: newPublicStatus,
      });

      if (updateResult.error) {
        return NextResponse.json({ error: updateResult.error }, { status: 500 });
      }

      // Create audit log
      await DatabaseService.createAuditLog({
        action: newPublicStatus ? 'dashboard_made_public' : 'dashboard_made_private',
        resource_type: 'dashboard',
        resource_id: params.id,
        user_id: user.id,
        metadata: {
          is_public: newPublicStatus,
        },
      });

      return NextResponse.json({
        data: {
          is_public: newPublicStatus,
          share_url: newPublicStatus 
            ? `${process.env.NEXT_PUBLIC_APP_URL}/public/dashboard/${params.id}`
            : null,
        },
        message: `Dashboard is now ${newPublicStatus ? 'public' : 'private'}`,
      });

    } else {
      return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }

  } catch (error) {
    console.error('Dashboard sharing error:', error);
    return NextResponse.json(
      { error: 'Failed to update sharing settings' },
      { status: 500 }
    );
  }
}
