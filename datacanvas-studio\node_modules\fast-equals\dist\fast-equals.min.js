!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports):"function"==typeof define&&define.amd?define(["exports"],r):r((e="undefined"!=typeof globalThis?globalThis:e||self)["fast-equals"]={})}(this,(function(e){"use strict";function r(e){return function(r,t,n,a,u,o,f){return e(r,t,f)}}function t(e){return function(r,t,n,a){if(!r||!t||"object"!=typeof r||"object"!=typeof t)return e(r,t,n,a);var u=a.get(r),o=a.get(t);if(u&&o)return u===t&&o===r;a.set(r,t),a.set(t,r);var f=e(r,t,n,a);return a.delete(r),a.delete(t),f}}function n(e,r){var t={};for(var n in e)t[n]=e[n];for(var n in r)t[n]=r[n];return t}function a(e){return e.constructor===Object||null==e.constructor}function u(e){return"function"==typeof e.then}function o(e,r){return e===r||e!=e&&r!=r}var f=Object.prototype.toString;function c(e){var r=e.areArraysEqual,t=e.areDatesEqual,n=e.areMapsEqual,c=e.areObjectsEqual,i=e.areRegExpsEqual,l=e.areSetsEqual,s=(0,e.createIsNestedEqual)(E);function E(e,E,p){if(e===E)return!0;if(!e||!E||"object"!=typeof e||"object"!=typeof E)return e!=e&&E!=E;if(a(e)&&a(E))return c(e,E,s,p);var q=Array.isArray(e),v=Array.isArray(E);if(q||v)return q===v&&r(e,E,s,p);var b=f.call(e);return b===f.call(E)&&("[object Date]"===b?t(e,E,s,p):"[object RegExp]"===b?i(e,E,s,p):"[object Map]"===b?n(e,E,s,p):"[object Set]"===b?l(e,E,s,p):"[object Object]"===b||"[object Arguments]"===b?!u(e)&&!u(E)&&c(e,E,s,p):("[object Boolean]"===b||"[object Number]"===b||"[object String]"===b)&&o(e.valueOf(),E.valueOf()))}return E}function i(e,r,t,n){var a=e.length;if(r.length!==a)return!1;for(;a-- >0;)if(!t(e[a],r[a],a,a,e,r,n))return!1;return!0}var l=t(i);function s(e,r){return o(e.valueOf(),r.valueOf())}function E(e,r,t,n){var a=e.size===r.size;if(!a)return!1;if(!e.size)return!0;var u={},o=0;return e.forEach((function(f,c){if(a){var i=!1,l=0;r.forEach((function(a,s){i||u[l]||!(i=t(c,s,o,l,e,r,n)&&t(f,a,c,s,e,r,n))||(u[l]=!0),l++})),o++,a=i}})),a}var p=t(E),q=Object.prototype.hasOwnProperty;function v(e,r,t,n){var a,u=Object.keys(e),o=u.length;if(Object.keys(r).length!==o)return!1;for(;o-- >0;){if("_owner"===(a=u[o])){var f=!!e.$$typeof,c=!!r.$$typeof;if((f||c)&&f!==c)return!1}if(!q.call(r,a)||!t(e[a],r[a],a,a,e,r,n))return!1}return!0}var b=t(v);function j(e,r){return e.source===r.source&&e.flags===r.flags}function y(e,r,t,n){var a=e.size===r.size;if(!a)return!1;if(!e.size)return!0;var u={};return e.forEach((function(o,f){if(a){var c=!1,i=0;r.forEach((function(a,l){c||u[i]||!(c=t(o,a,f,l,e,r,n))||(u[i]=!0),i++})),a=c}})),a}var d=t(y),g=Object.freeze({areArraysEqual:i,areDatesEqual:s,areMapsEqual:E,areObjectsEqual:v,areRegExpsEqual:j,areSetsEqual:y,createIsNestedEqual:r}),O=Object.freeze({areArraysEqual:l,areDatesEqual:s,areMapsEqual:p,areObjectsEqual:b,areRegExpsEqual:j,areSetsEqual:d,createIsNestedEqual:r}),h=c(g),z=c(n(g,{createIsNestedEqual:function(){return o}})),A=c(O),M=c(n(O,{createIsNestedEqual:function(){return o}}));e.circularDeepEqual=function(e,r){return A(e,r,new WeakMap)},e.circularShallowEqual=function(e,r){return M(e,r,new WeakMap)},e.createCustomCircularEqual=function(e){var r=c(n(O,e(O)));return function(e,t,n){return void 0===n&&(n=new WeakMap),r(e,t,n)}},e.createCustomEqual=function(e){return c(n(g,e(g)))},e.deepEqual=function(e,r){return h(e,r,void 0)},e.sameValueZeroEqual=o,e.shallowEqual=function(e,r){return z(e,r,void 0)},Object.defineProperty(e,"__esModule",{value:!0})}));
