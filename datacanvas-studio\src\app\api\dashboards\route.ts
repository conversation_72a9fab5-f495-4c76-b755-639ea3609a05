import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth/auth';
import { DatabaseService } from '@/lib/services/database';

export async function GET(request: NextRequest) {
  try {
    const user = await requireAuth();
    
    if (!user.organization_id) {
      return NextResponse.json({ error: 'User not associated with an organization' }, { status: 400 });
    }

    const result = await DatabaseService.getDashboards(user.organization_id, user.id);
    
    if (result.error) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    return NextResponse.json({ data: result.data });

  } catch (error) {
    console.error('Get dashboards error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboards' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth();
    
    if (!user.organization_id) {
      return NextResponse.json({ error: 'User not associated with an organization' }, { status: 400 });
    }

    const body = await request.json();
    const { name, description, config, layout, is_public } = body;

    // Validate required fields
    if (!name) {
      return NextResponse.json({ error: 'Dashboard name is required' }, { status: 400 });
    }

    // Create dashboard
    const result = await DatabaseService.createDashboard({
      name,
      description: description || '',
      config: config || {},
      layout: layout || [],
      is_public: is_public || false,
      organization_id: user.organization_id,
      created_by: user.id,
    });

    if (result.error) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    // Create audit log
    await DatabaseService.createAuditLog({
      action: 'dashboard_created',
      resource_type: 'dashboard',
      resource_id: result.data!.id,
      user_id: user.id,
      metadata: {
        name,
        is_public: is_public || false,
      },
    });

    return NextResponse.json({
      data: result.data,
      message: 'Dashboard created successfully',
    });

  } catch (error) {
    console.error('Create dashboard error:', error);
    return NextResponse.json(
      { error: 'Failed to create dashboard' },
      { status: 500 }
    );
  }
}
