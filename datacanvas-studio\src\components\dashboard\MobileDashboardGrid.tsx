'use client';

import { useState, useEffect } from 'react';
import { ChartRenderer } from '@/components/charts/ChartRenderer';
import { ChartConfig, ChartData } from '@/lib/charts/types';
import { Dashboard, Chart, Dataset } from '@/lib/types/database';
import { MoreVertical, Maximize2, MessageCircle, Share2 } from 'lucide-react';

interface MobileDashboardGridProps {
  dashboard: Dashboard;
  charts: Chart[];
  datasets: Dataset[];
  onChartClick?: (chart: Chart) => void;
  onCommentClick?: (chart: Chart) => void;
  onShareClick?: () => void;
}

export function MobileDashboardGrid({
  dashboard,
  charts,
  datasets,
  onChartClick,
  onCommentClick,
  onShareClick,
}: MobileDashboardGridProps) {
  const [chartData, setChartData] = useState<{ [key: string]: ChartData }>({});
  const [loading, setLoading] = useState<{ [key: string]: boolean }>({});
  const [expandedChart, setExpandedChart] = useState<string | null>(null);

  // Load data for each chart
  useEffect(() => {
    const loadChartData = async (chart: Chart) => {
      const dataset = datasets.find(d => d.id === chart.dataset_id);
      if (!dataset) return;

      setLoading(prev => ({ ...prev, [chart.id]: true }));

      try {
        const response = await fetch(`/api/datasets/${chart.dataset_id}/data?limit=1000`);
        const result = await response.json();

        if (response.ok) {
          const data: ChartData = {
            fields: dataset.schema_info?.columns?.map(col => ({
              name: col.name,
              type: col.type as any,
              nullable: col.nullable,
            })) || [],
            data: result.data || [],
          };

          setChartData(prev => ({ ...prev, [chart.id]: data }));
        }
      } catch (error) {
        console.error('Failed to load chart data:', error);
        // Use sample data as fallback
        const data: ChartData = {
          fields: dataset.schema_info?.columns?.map(col => ({
            name: col.name,
            type: col.type as any,
            nullable: col.nullable,
          })) || [],
          data: dataset.sample_data || [],
        };
        setChartData(prev => ({ ...prev, [chart.id]: data }));
      } finally {
        setLoading(prev => ({ ...prev, [chart.id]: false }));
      }
    };

    charts.forEach(loadChartData);
  }, [charts, datasets]);

  const renderChart = (chart: Chart) => {
    const data = chartData[chart.id];
    const isLoading = loading[chart.id];

    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-48 bg-gray-50 rounded-lg">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      );
    }

    if (!data) {
      return (
        <div className="flex items-center justify-center h-48 bg-gray-50 rounded-lg">
          <div className="text-gray-500 text-center">
            <div className="text-2xl mb-2">📊</div>
            <div>No data available</div>
          </div>
        </div>
      );
    }

    const chartConfig: ChartConfig = {
      id: chart.id,
      type: chart.type as any,
      name: chart.name,
      datasetId: chart.dataset_id,
      mapping: chart.config.data_mapping || {},
      style: chart.config.styling || {},
      filters: chart.config.filters || [],
      position: chart.position,
    };

    return (
      <div className="h-48 sm:h-64">
        <ChartRenderer config={chartConfig} data={data} />
      </div>
    );
  };

  if (charts.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg mx-4">
        <div className="text-center">
          <div className="text-4xl mb-4">📊</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No charts yet</h3>
          <p className="text-gray-600 text-sm px-4">
            This dashboard doesn't have any charts yet.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="pb-20"> {/* Add padding for bottom navigation */}
      {/* Dashboard Header */}
      <div className="sticky top-0 bg-white border-b border-gray-200 px-4 py-3 z-10">
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <h1 className="text-lg font-semibold text-gray-900 truncate">
              {dashboard.name}
            </h1>
            {dashboard.description && (
              <p className="text-sm text-gray-600 truncate">
                {dashboard.description}
              </p>
            )}
          </div>
          <div className="flex items-center space-x-2 ml-4">
            <button
              onClick={onShareClick}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
              title="Share dashboard"
            >
              <Share2 className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Charts Grid */}
      <div className="px-4 py-4 space-y-6">
        {charts.map((chart) => (
          <div
            key={chart.id}
            className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
          >
            {/* Chart Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
              <h3 className="text-sm font-medium text-gray-900 truncate flex-1">
                {chart.name}
              </h3>
              <div className="flex items-center space-x-1 ml-2">
                <button
                  onClick={() => onCommentClick?.(chart)}
                  className="p-1.5 text-gray-400 hover:text-gray-600 rounded"
                  title="Comments"
                >
                  <MessageCircle className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setExpandedChart(expandedChart === chart.id ? null : chart.id)}
                  className="p-1.5 text-gray-400 hover:text-gray-600 rounded"
                  title="Expand"
                >
                  <Maximize2 className="h-4 w-4" />
                </button>
                <button
                  onClick={() => onChartClick?.(chart)}
                  className="p-1.5 text-gray-400 hover:text-gray-600 rounded"
                  title="More options"
                >
                  <MoreVertical className="h-4 w-4" />
                </button>
              </div>
            </div>

            {/* Chart Content */}
            <div className={`p-4 ${expandedChart === chart.id ? 'h-96' : ''}`}>
              {renderChart(chart)}
            </div>
          </div>
        ))}
      </div>

      {/* Expanded Chart Modal */}
      {expandedChart && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">
                {charts.find(c => c.id === expandedChart)?.name}
              </h3>
              <button
                onClick={() => setExpandedChart(null)}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-md"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-4 h-96 overflow-auto">
              {charts.find(c => c.id === expandedChart) && renderChart(charts.find(c => c.id === expandedChart)!)}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Mobile-optimized chart card component
export function MobileChartCard({ 
  chart, 
  data, 
  isLoading, 
  onExpand, 
  onComment, 
  onOptions 
}: {
  chart: Chart;
  data?: ChartData;
  isLoading?: boolean;
  onExpand?: () => void;
  onComment?: () => void;
  onOptions?: () => void;
}) {
  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  const chartConfig: ChartConfig = {
    id: chart.id,
    type: chart.type as any,
    name: chart.name,
    datasetId: chart.dataset_id,
    mapping: chart.config.data_mapping || {},
    style: chart.config.styling || {},
    filters: chart.config.filters || [],
    position: chart.position,
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50">
        <h3 className="text-sm font-medium text-gray-900 truncate flex-1">
          {chart.name}
        </h3>
        <div className="flex items-center space-x-1">
          <button
            onClick={onComment}
            className="p-1 text-gray-400 hover:text-gray-600 rounded touch-manipulation"
            style={{ minWidth: '32px', minHeight: '32px' }}
          >
            <MessageCircle className="h-4 w-4" />
          </button>
          <button
            onClick={onExpand}
            className="p-1 text-gray-400 hover:text-gray-600 rounded touch-manipulation"
            style={{ minWidth: '32px', minHeight: '32px' }}
          >
            <Maximize2 className="h-4 w-4" />
          </button>
          <button
            onClick={onOptions}
            className="p-1 text-gray-400 hover:text-gray-600 rounded touch-manipulation"
            style={{ minWidth: '32px', minHeight: '32px' }}
          >
            <MoreVertical className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Chart */}
      <div className="p-3">
        <div className="h-40 sm:h-48">
          {data ? (
            <ChartRenderer config={chartConfig} data={data} />
          ) : (
            <div className="flex items-center justify-center h-full bg-gray-50 rounded">
              <div className="text-gray-500 text-center">
                <div className="text-xl mb-1">📊</div>
                <div className="text-xs">No data</div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
