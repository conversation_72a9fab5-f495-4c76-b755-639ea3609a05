'use client';

import { useState } from 'react';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';

interface DataSourceFormProps {
  onSuccess?: (dataSource: any) => void;
  onCancel?: () => void;
}

export function DataSourceForm({ onSuccess, onCancel }: DataSourceFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    type: '',
    connection_config: {},
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [error, setError] = useState('');
  const [preview, setPreview] = useState<any>(null);

  const handleInputChange = (field: string, value: any) => {
    if (field.startsWith('connection_config.')) {
      const configField = field.replace('connection_config.', '');
      setFormData(prev => ({
        ...prev,
        connection_config: {
          ...prev.connection_config,
          [configField]: value,
        },
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value,
      }));
    }
    
    // Reset connection status when form changes
    if (connectionStatus !== 'idle') {
      setConnectionStatus('idle');
      setPreview(null);
    }
  };

  const testConnection = async () => {
    if (!formData.type || Object.keys(formData.connection_config).length === 0) {
      setError('Please fill in the connection details first');
      return;
    }

    setIsTestingConnection(true);
    setError('');
    setConnectionStatus('idle');

    try {
      const response = await fetch('/api/data-sources/test-connection', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: formData.type,
          connection_config: formData.connection_config,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setConnectionStatus('success');
        setPreview(result.preview);
      } else {
        setConnectionStatus('error');
        setError(result.error || 'Connection test failed');
      }
    } catch (err) {
      setConnectionStatus('error');
      setError('Failed to test connection');
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.type) {
      setError('Please fill in all required fields');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('/api/data-sources', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (response.ok) {
        onSuccess?.(result.data);
      } else {
        setError(result.error || 'Failed to create data source');
      }
    } catch (err) {
      setError('Failed to create data source');
    } finally {
      setIsLoading(false);
    }
  };

  const renderConnectionForm = () => {
    switch (formData.type) {
      case 'postgresql':
      case 'mysql':
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="host" className="block text-sm font-medium text-gray-700 mb-1">Host</label>
                <input
                  id="host"
                  type="text"
                  value={(formData.connection_config as any).host || ''}
                  onChange={(e) => handleInputChange('connection_config.host', e.target.value)}
                  placeholder="localhost"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label htmlFor="port" className="block text-sm font-medium text-gray-700 mb-1">Port</label>
                <input
                  id="port"
                  type="number"
                  value={(formData.connection_config as any).port || ''}
                  onChange={(e) => handleInputChange('connection_config.port', parseInt(e.target.value))}
                  placeholder={formData.type === 'postgresql' ? '5432' : '3306'}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            <div>
              <label htmlFor="database" className="block text-sm font-medium text-gray-700 mb-1">Database</label>
              <input
                id="database"
                type="text"
                value={(formData.connection_config as any).database || ''}
                onChange={(e) => handleInputChange('connection_config.database', e.target.value)}
                placeholder="database_name"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1">Username</label>
                <input
                  id="username"
                  type="text"
                  value={(formData.connection_config as any).username || ''}
                  onChange={(e) => handleInputChange('connection_config.username', e.target.value)}
                  placeholder="username"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">Password</label>
                <input
                  id="password"
                  type="password"
                  value={(formData.connection_config as any).password || ''}
                  onChange={(e) => handleInputChange('connection_config.password', e.target.value)}
                  placeholder="password"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            <div>
              <label htmlFor="query" className="block text-sm font-medium text-gray-700 mb-1">Query (Optional)</label>
              <textarea
                id="query"
                value={(formData.connection_config as any).query || ''}
                onChange={(e) => handleInputChange('connection_config.query', e.target.value)}
                placeholder="SELECT * FROM your_table LIMIT 1000"
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        );

      case 'api':
        return (
          <div className="space-y-4">
            <div>
              <label htmlFor="url" className="block text-sm font-medium text-gray-700 mb-1">API URL</label>
              <input
                id="url"
                type="text"
                value={(formData.connection_config as any).url || ''}
                onChange={(e) => handleInputChange('connection_config.url', e.target.value)}
                placeholder="https://api.example.com/data"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label htmlFor="method" className="block text-sm font-medium text-gray-700 mb-1">HTTP Method</label>
              <select
                id="method"
                value={(formData.connection_config as any).method || 'GET'}
                onChange={(e) => handleInputChange('connection_config.method', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="GET">GET</option>
                <option value="POST">POST</option>
              </select>
            </div>
            <div>
              <label htmlFor="headers" className="block text-sm font-medium text-gray-700 mb-1">Headers (JSON)</label>
              <textarea
                id="headers"
                value={JSON.stringify((formData.connection_config as any).headers || {}, null, 2)}
                onChange={(e) => {
                  try {
                    const headers = JSON.parse(e.target.value);
                    handleInputChange('connection_config.headers', headers);
                  } catch {
                    // Invalid JSON, ignore
                  }
                }}
                placeholder='{"Authorization": "Bearer token", "Content-Type": "application/json"}'
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        );

      default:
        return (
          <div className="text-center py-8 text-gray-500">
            Please select a data source type to configure connection settings.
          </div>
        );
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto bg-white rounded-lg shadow-md">
      <div className="p-6 border-b">
        <h2 className="text-xl font-semibold">Add Data Source</h2>
        <p className="text-gray-600 mt-1">
          Connect to your data sources to create datasets for visualization.
        </p>
      </div>
      <div className="p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <XCircle className="h-5 w-5 text-red-400" />
                <div className="ml-3">
                  <p className="text-sm text-red-800">{error}</p>
                </div>
              </div>
            </div>
          )}

          <div className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Data Source Name
              </label>
              <input
                id="name"
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="My Data Source"
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
                Data Source Type
              </label>
              <select
                id="type"
                value={formData.type}
                onChange={(e) => handleInputChange('type', e.target.value)}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select data source type</option>
                <option value="postgresql">PostgreSQL</option>
                <option value="mysql">MySQL</option>
                <option value="api">REST API</option>
              </select>
            </div>
          </div>

          {formData.type && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Connection Settings</h3>
                <button
                  type="button"
                  onClick={testConnection}
                  disabled={isTestingConnection}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {isTestingConnection ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : connectionStatus === 'success' ? (
                    <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                  ) : connectionStatus === 'error' ? (
                    <XCircle className="h-4 w-4 mr-2 text-red-500" />
                  ) : null}
                  Test Connection
                </button>
              </div>

              {renderConnectionForm()}

              {connectionStatus === 'success' && preview && (
                <div className="bg-green-50 border border-green-200 rounded-md p-4">
                  <div className="flex">
                    <CheckCircle className="h-5 w-5 text-green-400" />
                    <div className="ml-3">
                      <p className="text-sm text-green-800">
                        Connection successful! Found {preview.row_count} rows with {preview.schema?.columns?.length} columns.
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          <div className="flex justify-end space-x-2">
            {onCancel && (
              <button
                type="button"
                onClick={onCancel}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Cancel
              </button>
            )}
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : null}
              Create Data Source
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
