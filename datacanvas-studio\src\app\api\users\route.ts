import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth/auth';
import { DatabaseService } from '@/lib/services/database';

export async function GET(request: NextRequest) {
  try {
    const user = await requireAdmin();
    
    if (!user.organization_id) {
      return NextResponse.json({ error: 'User not associated with an organization' }, { status: 400 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const role = searchParams.get('role');

    const supabase = await import('@/lib/supabase/server').then(m => m.createSupabaseServerClient());
    
    let query = (await supabase)
      .from('users')
      .select('*', { count: 'exact' })
      .eq('organization_id', user.organization_id)
      .order('created_at', { ascending: false });

    if (role && ['admin', 'editor', 'viewer'].includes(role)) {
      query = query.eq('role', role);
    }

    const { data, error, count } = await query
      .range((page - 1) * limit, page * limit - 1);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({
      data: data || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        pages: Math.ceil((count || 0) / limit),
      },
    });

  } catch (error) {
    console.error('Get users error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await requireAdmin();
    
    if (!user.organization_id) {
      return NextResponse.json({ error: 'User not associated with an organization' }, { status: 400 });
    }

    const body = await request.json();
    const { email, full_name, role } = body;

    // Validate required fields
    if (!email || !role) {
      return NextResponse.json({ error: 'Email and role are required' }, { status: 400 });
    }

    // Validate role
    if (!['admin', 'editor', 'viewer'].includes(role)) {
      return NextResponse.json({ error: 'Invalid role' }, { status: 400 });
    }

    const supabase = await import('@/lib/supabase/server').then(m => m.createSupabaseServerClient());

    // Check if user already exists
    const { data: existingUser } = await (await supabase)
      .from('users')
      .select('id')
      .eq('email', email)
      .single();

    if (existingUser) {
      return NextResponse.json({ error: 'User already exists' }, { status: 400 });
    }

    // Create user invitation (in a real app, you'd send an invitation email)
    // For now, we'll create a placeholder user record
    const { data: newUser, error: createError } = await (await supabase)
      .from('users')
      .insert({
        email,
        full_name: full_name || null,
        role,
        organization_id: user.organization_id,
      })
      .select()
      .single();

    if (createError) {
      return NextResponse.json({ error: createError.message }, { status: 500 });
    }

    // Create audit log
    await DatabaseService.createAuditLog({
      action: 'user_invited',
      resource_type: 'user',
      resource_id: newUser.id,
      user_id: user.id,
      metadata: {
        email,
        role,
        full_name,
      },
    });

    return NextResponse.json({
      data: newUser,
      message: 'User invited successfully',
    });

  } catch (error) {
    console.error('Create user error:', error);
    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    );
  }
}
