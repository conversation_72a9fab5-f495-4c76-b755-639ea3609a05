import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth/auth';
import { DatabaseService } from '@/lib/services/database';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAdmin();
    
    // Check if user belongs to this organization
    if (user.organization_id !== params.id) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const result = await DatabaseService.getOrganization(params.id);
    
    if (result.error) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    if (!result.data) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    return NextResponse.json({
      data: {
        custom_branding: result.data.custom_branding,
        settings: result.data.settings,
      },
    });

  } catch (error) {
    console.error('Get branding error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch branding settings' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAdmin();
    
    // Check if user belongs to this organization
    if (user.organization_id !== params.id) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const body = await request.json();
    const { custom_branding, settings } = body;

    // Validate branding configuration
    if (custom_branding) {
      const validFields = [
        'primary_color',
        'secondary_color',
        'logo_url',
        'favicon_url',
        'company_name',
        'app_name',
        'theme',
        'custom_css',
        'hide_powered_by',
        'custom_domain',
      ];

      const invalidFields = Object.keys(custom_branding).filter(
        field => !validFields.includes(field)
      );

      if (invalidFields.length > 0) {
        return NextResponse.json({
          error: `Invalid branding fields: ${invalidFields.join(', ')}`
        }, { status: 400 });
      }

      // Validate color formats
      if (custom_branding.primary_color && !/^#[0-9A-F]{6}$/i.test(custom_branding.primary_color)) {
        return NextResponse.json({
          error: 'Primary color must be a valid hex color (e.g., #FF0000)'
        }, { status: 400 });
      }

      if (custom_branding.secondary_color && !/^#[0-9A-F]{6}$/i.test(custom_branding.secondary_color)) {
        return NextResponse.json({
          error: 'Secondary color must be a valid hex color (e.g., #FF0000)'
        }, { status: 400 });
      }

      // Validate URLs
      if (custom_branding.logo_url && !isValidUrl(custom_branding.logo_url)) {
        return NextResponse.json({
          error: 'Logo URL must be a valid URL'
        }, { status: 400 });
      }

      if (custom_branding.favicon_url && !isValidUrl(custom_branding.favicon_url)) {
        return NextResponse.json({
          error: 'Favicon URL must be a valid URL'
        }, { status: 400 });
      }
    }

    // Update organization
    const supabase = await import('@/lib/supabase/server').then(m => m.createSupabaseServerClient());
    const updates: any = {};

    if (custom_branding !== undefined) {
      updates.custom_branding = custom_branding;
    }

    if (settings !== undefined) {
      updates.settings = settings;
    }

    const { data, error } = await (await supabase)
      .from('organizations')
      .update(updates)
      .eq('id', params.id)
      .select()
      .single();

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Create audit log
    await DatabaseService.createAuditLog({
      action: 'branding_updated',
      resource_type: 'organization',
      resource_id: params.id,
      user_id: user.id,
      metadata: {
        updated_fields: Object.keys(updates),
        custom_branding: custom_branding ? Object.keys(custom_branding) : [],
      },
    });

    return NextResponse.json({
      data: {
        custom_branding: data.custom_branding,
        settings: data.settings,
      },
      message: 'Branding settings updated successfully',
    });

  } catch (error) {
    console.error('Update branding error:', error);
    return NextResponse.json(
      { error: 'Failed to update branding settings' },
      { status: 500 }
    );
  }
}

function isValidUrl(string: string): boolean {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}
