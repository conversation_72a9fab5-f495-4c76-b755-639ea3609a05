import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth/auth';
import { DatabaseService } from '@/lib/services/database';
import { DataProcessingService } from '@/lib/services/data-processing';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth();
    const { searchParams } = new URL(request.url);
    
    const limit = parseInt(searchParams.get('limit') || '1000');
    const offset = parseInt(searchParams.get('offset') || '0');
    const filters = searchParams.get('filters') ? JSON.parse(searchParams.get('filters')!) : [];

    // Get dataset info
    const supabase = await import('@/lib/supabase/server').then(m => m.createSupabaseServerClient());
    const { data: dataset, error: datasetError } = await (await supabase)
      .from('datasets')
      .select(`
        *,
        data_source:data_sources(*)
      `)
      .eq('id', params.id)
      .single();

    if (datasetError || !dataset) {
      return NextResponse.json({ error: 'Dataset not found' }, { status: 404 });
    }

    // Check if user has access to this dataset
    if (dataset.organization_id !== user.organization_id) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Get data based on data source type
    let data: any[] = [];
    
    try {
      data = await DataProcessingService.getDatasetData(dataset, filters, limit, offset);
    } catch (error) {
      console.error('Error fetching dataset data:', error);
      // Fallback to sample data if live data fetch fails
      data = dataset.sample_data || [];
    }

    // Apply client-side filters if needed
    if (filters && filters.length > 0) {
      data = data.filter(row => {
        return filters.every((filter: any) => {
          const value = row[filter.column];
          switch (filter.operator) {
            case 'equals':
              return value === filter.value;
            case 'not_equals':
              return value !== filter.value;
            case 'greater_than':
              return Number(value) > Number(filter.value);
            case 'less_than':
              return Number(value) < Number(filter.value);
            case 'contains':
              return String(value).toLowerCase().includes(String(filter.value).toLowerCase());
            default:
              return true;
          }
        });
      });
    }

    // Apply pagination
    const paginatedData = data.slice(offset, offset + limit);

    return NextResponse.json({
      data: paginatedData,
      pagination: {
        limit,
        offset,
        total: data.length,
        has_more: offset + limit < data.length,
      },
      schema: dataset.schema_info,
    });

  } catch (error) {
    console.error('Get dataset data error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dataset data' },
      { status: 500 }
    );
  }
}
