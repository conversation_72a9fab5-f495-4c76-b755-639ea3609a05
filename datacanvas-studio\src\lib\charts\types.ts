// Chart Types and Configuration

export interface ChartField {
  name: string;
  type: 'string' | 'number' | 'date' | 'boolean';
  nullable: boolean;
}

export interface ChartData {
  fields: ChartField[];
  data: Record<string, any>[];
}

export interface ChartMapping {
  x?: string;
  y?: string;
  color?: string;
  size?: string;
  label?: string;
  series?: string;
}

export interface ChartStyle {
  colors?: string[];
  theme?: 'light' | 'dark';
  title?: string;
  subtitle?: string;
  showLegend?: boolean;
  showGrid?: boolean;
  showTooltip?: boolean;
  width?: number;
  height?: number;
}

export interface ChartFilter {
  field: string;
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'in' | 'not_in';
  value: any;
}

export interface ChartConfig {
  id: string;
  type: ChartType;
  name: string;
  datasetId: string;
  mapping: ChartMapping;
  style: ChartStyle;
  filters: ChartFilter[];
  position: {
    x: number;
    y: number;
    w: number;
    h: number;
  };
}

export type ChartType = 
  | 'bar'
  | 'line' 
  | 'area'
  | 'pie'
  | 'donut'
  | 'scatter'
  | 'bubble'
  | 'histogram'
  | 'heatmap'
  | 'table'
  | 'metric'
  | 'gauge';

export interface ChartTypeDefinition {
  type: ChartType;
  name: string;
  description: string;
  icon: string;
  requiredMappings: (keyof ChartMapping)[];
  optionalMappings: (keyof ChartMapping)[];
  supportedDataTypes: {
    [K in keyof ChartMapping]?: ChartField['type'][];
  };
}

export const CHART_TYPES: ChartTypeDefinition[] = [
  {
    type: 'bar',
    name: 'Bar Chart',
    description: 'Compare values across categories',
    icon: '📊',
    requiredMappings: ['x', 'y'],
    optionalMappings: ['color', 'series'],
    supportedDataTypes: {
      x: ['string', 'date'],
      y: ['number'],
      color: ['string'],
      series: ['string'],
    },
  },
  {
    type: 'line',
    name: 'Line Chart',
    description: 'Show trends over time',
    icon: '📈',
    requiredMappings: ['x', 'y'],
    optionalMappings: ['color', 'series'],
    supportedDataTypes: {
      x: ['date', 'number', 'string'],
      y: ['number'],
      color: ['string'],
      series: ['string'],
    },
  },
  {
    type: 'area',
    name: 'Area Chart',
    description: 'Show cumulative values over time',
    icon: '🏔️',
    requiredMappings: ['x', 'y'],
    optionalMappings: ['color', 'series'],
    supportedDataTypes: {
      x: ['date', 'number'],
      y: ['number'],
      color: ['string'],
      series: ['string'],
    },
  },
  {
    type: 'pie',
    name: 'Pie Chart',
    description: 'Show parts of a whole',
    icon: '🥧',
    requiredMappings: ['label', 'y'],
    optionalMappings: ['color'],
    supportedDataTypes: {
      label: ['string'],
      y: ['number'],
      color: ['string'],
    },
  },
  {
    type: 'donut',
    name: 'Donut Chart',
    description: 'Pie chart with center hole',
    icon: '🍩',
    requiredMappings: ['label', 'y'],
    optionalMappings: ['color'],
    supportedDataTypes: {
      label: ['string'],
      y: ['number'],
      color: ['string'],
    },
  },
  {
    type: 'scatter',
    name: 'Scatter Plot',
    description: 'Show correlation between two variables',
    icon: '🔵',
    requiredMappings: ['x', 'y'],
    optionalMappings: ['color', 'size'],
    supportedDataTypes: {
      x: ['number'],
      y: ['number'],
      color: ['string', 'number'],
      size: ['number'],
    },
  },
  {
    type: 'bubble',
    name: 'Bubble Chart',
    description: 'Scatter plot with size dimension',
    icon: '🫧',
    requiredMappings: ['x', 'y', 'size'],
    optionalMappings: ['color'],
    supportedDataTypes: {
      x: ['number'],
      y: ['number'],
      size: ['number'],
      color: ['string', 'number'],
    },
  },
  {
    type: 'histogram',
    name: 'Histogram',
    description: 'Show distribution of values',
    icon: '📊',
    requiredMappings: ['x'],
    optionalMappings: ['color'],
    supportedDataTypes: {
      x: ['number'],
      color: ['string'],
    },
  },
  {
    type: 'table',
    name: 'Data Table',
    description: 'Display raw data in tabular format',
    icon: '📋',
    requiredMappings: [],
    optionalMappings: ['x', 'y', 'color', 'label'],
    supportedDataTypes: {
      x: ['string', 'number', 'date', 'boolean'],
      y: ['string', 'number', 'date', 'boolean'],
      color: ['string', 'number', 'date', 'boolean'],
      label: ['string', 'number', 'date', 'boolean'],
    },
  },
  {
    type: 'metric',
    name: 'Single Metric',
    description: 'Display a single key metric',
    icon: '🔢',
    requiredMappings: ['y'],
    optionalMappings: ['label'],
    supportedDataTypes: {
      y: ['number'],
      label: ['string'],
    },
  },
];

export const DEFAULT_COLORS = [
  '#3B82F6', // blue
  '#EF4444', // red
  '#10B981', // green
  '#F59E0B', // yellow
  '#8B5CF6', // purple
  '#F97316', // orange
  '#06B6D4', // cyan
  '#84CC16', // lime
  '#EC4899', // pink
  '#6B7280', // gray
];

export const CHART_THEMES = {
  light: {
    background: '#ffffff',
    text: '#1f2937',
    grid: '#f3f4f6',
    axis: '#6b7280',
  },
  dark: {
    background: '#1f2937',
    text: '#f9fafb',
    grid: '#374151',
    axis: '#9ca3af',
  },
};

export function getChartTypeDefinition(type: ChartType): ChartTypeDefinition | undefined {
  return CHART_TYPES.find(t => t.type === type);
}

export function isValidMapping(
  chartType: ChartType,
  mapping: ChartMapping,
  fields: ChartField[]
): { valid: boolean; errors: string[] } {
  const definition = getChartTypeDefinition(chartType);
  if (!definition) {
    return { valid: false, errors: ['Invalid chart type'] };
  }

  const errors: string[] = [];

  // Check required mappings
  for (const required of definition.requiredMappings) {
    if (!mapping[required]) {
      errors.push(`${required} mapping is required for ${definition.name}`);
    }
  }

  // Check data type compatibility
  for (const [mappingKey, fieldName] of Object.entries(mapping)) {
    if (!fieldName) continue;

    const field = fields.find(f => f.name === fieldName);
    if (!field) {
      errors.push(`Field "${fieldName}" not found in dataset`);
      continue;
    }

    const supportedTypes = definition.supportedDataTypes[mappingKey as keyof ChartMapping];
    if (supportedTypes && !supportedTypes.includes(field.type)) {
      errors.push(
        `Field "${fieldName}" (${field.type}) is not compatible with ${mappingKey} mapping. ` +
        `Supported types: ${supportedTypes.join(', ')}`
      );
    }
  }

  return { valid: errors.length === 0, errors };
}

export function generateDefaultStyle(chartType: ChartType): ChartStyle {
  return {
    colors: DEFAULT_COLORS,
    theme: 'light',
    showLegend: true,
    showGrid: true,
    showTooltip: true,
    width: 400,
    height: 300,
  };
}

export function generateDefaultPosition(): ChartConfig['position'] {
  return {
    x: 0,
    y: 0,
    w: 6,
    h: 4,
  };
}
