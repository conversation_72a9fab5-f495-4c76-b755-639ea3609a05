import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, canEditDashboard } from '@/lib/auth/auth';
import { DatabaseService } from '@/lib/services/database';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth();
    
    // Get dashboard to check permissions
    const dashboardResult = await DatabaseService.getDashboard(params.id);
    
    if (dashboardResult.error || !dashboardResult.data) {
      return NextResponse.json({ error: 'Dashboard not found' }, { status: 404 });
    }

    // Check if user can edit this dashboard
    const permissions = (dashboardResult.data as any).permissions || [];
    if (!canEditDashboard(user, dashboardResult.data, permissions)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const body = await request.json();
    const { name, type, config, dataset_id, position } = body;

    // Validate required fields
    if (!name || !type || !config || !dataset_id) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Create chart
    const result = await DatabaseService.createChart({
      name,
      type,
      config,
      dataset_id,
      dashboard_id: params.id,
      position: position || { x: 0, y: 0, w: 6, h: 4 },
      created_by: user.id,
    });

    if (result.error) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    // Create audit log
    await DatabaseService.createAuditLog({
      action: 'chart_created',
      resource_type: 'chart',
      resource_id: result.data!.id,
      user_id: user.id,
      metadata: {
        name,
        type,
        dashboard_id: params.id,
      },
    });

    return NextResponse.json({
      data: result.data,
      message: 'Chart created successfully',
    });

  } catch (error) {
    console.error('Create chart error:', error);
    return NextResponse.json(
      { error: 'Failed to create chart' },
      { status: 500 }
    );
  }
}
