import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, canViewDashboard } from '@/lib/auth/auth';
import { DatabaseService } from '@/lib/services/database';
import { ReportGenerator } from '@/lib/services/report-generator';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth();
    
    // Get report
    const supabase = await import('@/lib/supabase/server').then(m => m.createSupabaseServerClient());
    const { data: report, error: reportError } = await (await supabase)
      .from('reports')
      .select(`
        *,
        dashboard:dashboards(*)
      `)
      .eq('id', params.id)
      .single();

    if (reportError || !report) {
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }

    // Check if user can view the dashboard
    const permissions = (report.dashboard as any)?.permissions || [];
    if (!canViewDashboard(user, report.dashboard, permissions)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Get dashboard charts
    const { data: charts, error: chartsError } = await (await supabase)
      .from('charts')
      .select('*')
      .eq('dashboard_id', report.dashboard_id);

    if (chartsError) {
      return NextResponse.json({ error: chartsError.message }, { status: 500 });
    }

    // Get datasets
    const datasetIds = [...new Set(charts?.map(chart => chart.dataset_id) || [])];
    const { data: datasets, error: datasetsError } = await (await supabase)
      .from('datasets')
      .select('*')
      .in('id', datasetIds);

    if (datasetsError) {
      return NextResponse.json({ error: datasetsError.message }, { status: 500 });
    }

    // Parse request body for options
    const body = await request.json().catch(() => ({}));
    const options = {
      format: report.format as 'pdf' | 'excel' | 'csv',
      includeCharts: body.includeCharts !== false,
      includeData: body.includeData !== false,
      dateRange: body.dateRange,
      filters: body.filters || [],
    };

    // Generate report
    const generatedReport = await ReportGenerator.generateDashboardReport(
      report.dashboard,
      charts || [],
      datasets || [],
      options
    );

    // Update last generated timestamp
    await (await supabase)
      .from('reports')
      .update({ last_sent_at: new Date().toISOString() })
      .eq('id', params.id);

    // Create audit log
    await DatabaseService.createAuditLog({
      action: 'report_generated',
      resource_type: 'report',
      resource_id: params.id,
      user_id: user.id,
      metadata: {
        format: options.format,
        file_size: generatedReport.buffer.length,
        charts_count: charts?.length || 0,
      },
    });

    // Return the generated report
    return new NextResponse(generatedReport.buffer, {
      headers: {
        'Content-Type': generatedReport.mimeType,
        'Content-Disposition': `attachment; filename="${generatedReport.filename}"`,
        'Content-Length': generatedReport.buffer.length.toString(),
      },
    });

  } catch (error) {
    console.error('Generate report error:', error);
    return NextResponse.json(
      { error: 'Failed to generate report' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth();
    
    // Get report
    const supabase = await import('@/lib/supabase/server').then(m => m.createSupabaseServerClient());
    const { data: report, error: reportError } = await (await supabase)
      .from('reports')
      .select(`
        *,
        dashboard:dashboards(*)
      `)
      .eq('id', params.id)
      .single();

    if (reportError || !report) {
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }

    // Check if user can view the dashboard
    const permissions = (report.dashboard as any)?.permissions || [];
    if (!canViewDashboard(user, report.dashboard, permissions)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Get dashboard charts
    const { data: charts, error: chartsError } = await (await supabase)
      .from('charts')
      .select('*')
      .eq('dashboard_id', report.dashboard_id);

    if (chartsError) {
      return NextResponse.json({ error: chartsError.message }, { status: 500 });
    }

    // Get datasets
    const datasetIds = [...new Set(charts?.map(chart => chart.dataset_id) || [])];
    const { data: datasets, error: datasetsError } = await (await supabase)
      .from('datasets')
      .select('*')
      .in('id', datasetIds);

    if (datasetsError) {
      return NextResponse.json({ error: datasetsError.message }, { status: 500 });
    }

    // Generate report with default options
    const options = {
      format: report.format as 'pdf' | 'excel' | 'csv',
      includeCharts: true,
      includeData: true,
      filters: [],
    };

    const generatedReport = await ReportGenerator.generateDashboardReport(
      report.dashboard,
      charts || [],
      datasets || [],
      options
    );

    // Update last generated timestamp
    await (await supabase)
      .from('reports')
      .update({ last_sent_at: new Date().toISOString() })
      .eq('id', params.id);

    // Create audit log
    await DatabaseService.createAuditLog({
      action: 'report_downloaded',
      resource_type: 'report',
      resource_id: params.id,
      user_id: user.id,
      metadata: {
        format: options.format,
        file_size: generatedReport.buffer.length,
      },
    });

    // Return the generated report
    return new NextResponse(generatedReport.buffer, {
      headers: {
        'Content-Type': generatedReport.mimeType,
        'Content-Disposition': `attachment; filename="${generatedReport.filename}"`,
        'Content-Length': generatedReport.buffer.length.toString(),
      },
    });

  } catch (error) {
    console.error('Download report error:', error);
    return NextResponse.json(
      { error: 'Failed to download report' },
      { status: 500 }
    );
  }
}
