import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin, requireAuth } from '@/lib/auth/auth';
import { DatabaseService } from '@/lib/services/database';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth();
    
    // Users can view their own profile, admins can view any user in their org
    if (user.id !== params.id && user.role !== 'admin') {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const result = await DatabaseService.getUser(params.id);
    
    if (result.error) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    if (!result.data) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if user is in the same organization (for admin access)
    if (user.role === 'admin' && result.data.organization_id !== user.organization_id) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    return NextResponse.json({ data: result.data });

  } catch (error) {
    console.error('Get user error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth();
    const body = await request.json();
    
    // Users can update their own profile, admins can update any user in their org
    if (user.id !== params.id && user.role !== 'admin') {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Get existing user to check permissions
    const existingResult = await DatabaseService.getUser(params.id);
    
    if (existingResult.error || !existingResult.data) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if user is in the same organization (for admin access)
    if (user.role === 'admin' && existingResult.data.organization_id !== user.organization_id) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const { full_name, role } = body;
    const updates: any = {};

    // Users can update their own name
    if (full_name !== undefined) {
      updates.full_name = full_name;
    }

    // Only admins can change roles, and they can't change their own role
    if (role !== undefined) {
      if (user.role !== 'admin') {
        return NextResponse.json({ error: 'Only admins can change user roles' }, { status: 403 });
      }
      
      if (user.id === params.id) {
        return NextResponse.json({ error: 'You cannot change your own role' }, { status: 400 });
      }

      if (!['admin', 'editor', 'viewer'].includes(role)) {
        return NextResponse.json({ error: 'Invalid role' }, { status: 400 });
      }

      updates.role = role;
    }

    if (Object.keys(updates).length === 0) {
      return NextResponse.json({ error: 'No valid updates provided' }, { status: 400 });
    }

    // Update user
    const result = await DatabaseService.updateUser(params.id, updates);

    if (result.error) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    // Create audit log for role changes
    if (updates.role) {
      await DatabaseService.createAuditLog({
        action: 'user_role_changed',
        resource_type: 'user',
        resource_id: params.id,
        user_id: user.id,
        metadata: {
          old_role: existingResult.data.role,
          new_role: updates.role,
          target_user_email: existingResult.data.email,
        },
      });
    }

    return NextResponse.json({
      data: result.data,
      message: 'User updated successfully',
    });

  } catch (error) {
    console.error('Update user error:', error);
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAdmin();
    
    // Admins cannot delete themselves
    if (user.id === params.id) {
      return NextResponse.json({ error: 'You cannot delete your own account' }, { status: 400 });
    }

    // Get existing user to check permissions
    const existingResult = await DatabaseService.getUser(params.id);
    
    if (existingResult.error || !existingResult.data) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if user is in the same organization
    if (existingResult.data.organization_id !== user.organization_id) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Delete user
    const supabase = await import('@/lib/supabase/server').then(m => m.createSupabaseServerClient());
    const { error } = await (await supabase)
      .from('users')
      .delete()
      .eq('id', params.id);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Create audit log
    await DatabaseService.createAuditLog({
      action: 'user_deleted',
      resource_type: 'user',
      resource_id: params.id,
      user_id: user.id,
      metadata: {
        deleted_user_email: existingResult.data.email,
        deleted_user_role: existingResult.data.role,
      },
    });

    return NextResponse.json({
      message: 'User deleted successfully',
    });

  } catch (error) {
    console.error('Delete user error:', error);
    return NextResponse.json(
      { error: 'Failed to delete user' },
      { status: 500 }
    );
  }
}
