{"name": "DataCanvas Studio", "short_name": "DataCanvas", "description": "Professional data visualization and dashboard platform", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#3b82f6", "orientation": "portrait-primary", "scope": "/", "lang": "en", "categories": ["business", "productivity", "utilities"], "icons": [{"src": "/android-chrome-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "/android-chrome-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}, {"src": "/apple-touch-icon.png", "sizes": "180x180", "type": "image/png", "purpose": "any"}, {"src": "/favicon-32x32.png", "sizes": "32x32", "type": "image/png", "purpose": "any"}, {"src": "/favicon-16x16.png", "sizes": "16x16", "type": "image/png", "purpose": "any"}], "shortcuts": [{"name": "New Dashboard", "short_name": "New Dashboard", "description": "Create a new dashboard", "url": "/dashboards/new", "icons": [{"src": "/icons/shortcut-new-dashboard.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Data Sources", "short_name": "Data Sources", "description": "Manage data sources", "url": "/data-sources", "icons": [{"src": "/icons/shortcut-data-sources.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Reports", "short_name": "Reports", "description": "View and manage reports", "url": "/reports", "icons": [{"src": "/icons/shortcut-reports.png", "sizes": "96x96", "type": "image/png"}]}], "screenshots": [{"src": "/screenshots/desktop-dashboard.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "Dashboard view on desktop"}, {"src": "/screenshots/mobile-dashboard.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "Dashboard view on mobile"}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}, "handle_links": "preferred", "capture_links": "existing-client-navigate"}