!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react"),require("react-dom")):"function"==typeof define&&define.amd?define(["react","react-dom"],t):"object"==typeof exports?exports.ReactResizable=t(require("react"),require("react-dom")):e.ReactResizable=t(e.React,e.ReactDOM)}(window,(function(e,t){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=20)}([function(t,n){t.exports=e},function(e,t,n){e.exports=n(11)()},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.findInArray=function(e,t){for(var n=0,r=e.length;n<r;n++)if(t.apply(t,[e[n],n,e]))return e[n]},t.isFunction=function(e){return"function"==typeof e||"[object Function]"===Object.prototype.toString.call(e)},t.isNum=function(e){return"number"==typeof e&&!isNaN(e)},t.int=function(e){return parseInt(e,10)},t.dontSetMe=function(e,t,n){if(e[t])return new Error("Invalid prop ".concat(t," passed to ").concat(n," - do not set this, set it on the child."))}},function(e,n){e.exports=t},function(e,t,n){"use strict";function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.matchesSelector=f,t.matchesSelectorAndParentsTo=function(e,t,n){var r=e;do{if(f(r,t))return!0;if(r===n)return!1;r=r.parentNode}while(r);return!1},t.addEvent=function(e,t,n,r){if(!e)return;var o=l({capture:!0},r);e.addEventListener?e.addEventListener(t,n,o):e.attachEvent?e.attachEvent("on"+t,n):e["on"+t]=n},t.removeEvent=function(e,t,n,r){if(!e)return;var o=l({capture:!0},r);e.removeEventListener?e.removeEventListener(t,n,o):e.detachEvent?e.detachEvent("on"+t,n):e["on"+t]=null},t.outerHeight=function(e){var t=e.clientHeight,n=e.ownerDocument.defaultView.getComputedStyle(e);return t+=(0,o.int)(n.borderTopWidth),t+=(0,o.int)(n.borderBottomWidth)},t.outerWidth=function(e){var t=e.clientWidth,n=e.ownerDocument.defaultView.getComputedStyle(e);return t+=(0,o.int)(n.borderLeftWidth),t+=(0,o.int)(n.borderRightWidth)},t.innerHeight=function(e){var t=e.clientHeight,n=e.ownerDocument.defaultView.getComputedStyle(e);return t-=(0,o.int)(n.paddingTop),t-=(0,o.int)(n.paddingBottom)},t.innerWidth=function(e){var t=e.clientWidth,n=e.ownerDocument.defaultView.getComputedStyle(e);return t-=(0,o.int)(n.paddingLeft),t-=(0,o.int)(n.paddingRight)},t.offsetXYFromParent=function(e,t,n){var r=t===t.ownerDocument.body?{left:0,top:0}:t.getBoundingClientRect(),o=(e.clientX+t.scrollLeft-r.left)/n,a=(e.clientY+t.scrollTop-r.top)/n;return{x:o,y:a}},t.createCSSTransform=function(e,t){var n=p(e,t,"px");return c({},(0,a.browserPrefixToKey)("transform",a.default),n)},t.createSVGTransform=function(e,t){return p(e,t,"")},t.getTranslation=p,t.getTouch=function(e,t){return e.targetTouches&&(0,o.findInArray)(e.targetTouches,(function(e){return t===e.identifier}))||e.changedTouches&&(0,o.findInArray)(e.changedTouches,(function(e){return t===e.identifier}))},t.getTouchIdentifier=function(e){if(e.targetTouches&&e.targetTouches[0])return e.targetTouches[0].identifier;if(e.changedTouches&&e.changedTouches[0])return e.changedTouches[0].identifier},t.addUserSelectStyles=function(e){if(!e)return;var t=e.getElementById("react-draggable-style-el");t||((t=e.createElement("style")).type="text/css",t.id="react-draggable-style-el",t.innerHTML=".react-draggable-transparent-selection *::-moz-selection {all: inherit;}\n",t.innerHTML+=".react-draggable-transparent-selection *::selection {all: inherit;}\n",e.getElementsByTagName("head")[0].appendChild(t));e.body&&d(e.body,"react-draggable-transparent-selection")},t.removeUserSelectStyles=function(e){if(!e)return;try{if(e.body&&h(e.body,"react-draggable-transparent-selection"),e.selection)e.selection.empty();else{var t=(e.defaultView||window).getSelection();t&&"Caret"!==t.type&&t.removeAllRanges()}}catch(e){}},t.addClassName=d,t.removeClassName=h;var o=n(2),a=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!==r(e)&&"function"!=typeof e)return{default:e};var t=i();if(t&&t.has(e))return t.get(e);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){var s=o?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(n,a,s):n[a]=e[a]}n.default=e,t&&t.set(e,n);return n}(n(14));function i(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return i=function(){return e},e}function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){c(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function c(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var u="";function f(e,t){return u||(u=(0,o.findInArray)(["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"],(function(t){return(0,o.isFunction)(e[t])}))),!!(0,o.isFunction)(e[u])&&e[u](t)}function p(e,t,n){var r=e.x,o=e.y,a="translate(".concat(r).concat(n,",").concat(o).concat(n,")");if(t){var i="".concat("string"==typeof t.x?t.x:t.x+n),s="".concat("string"==typeof t.y?t.y:t.y+n);a="translate(".concat(i,", ").concat(s,")")+a}return a}function d(e,t){e.classList?e.classList.add(t):e.className.match(new RegExp("(?:^|\\s)".concat(t,"(?!\\S)")))||(e.className+=" ".concat(t))}function h(e,t){e.classList?e.classList.remove(t):e.className=e.className.replace(new RegExp("(?:^|\\s)".concat(t,"(?!\\S)"),"g"),"")}},function(e,t,n){"use strict";var r=n(10),o=r.default,a=r.DraggableCore;e.exports=o,e.exports.default=o,e.exports.DraggableCore=a},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getBoundPosition=function(e,t,n){if(!e.props.bounds)return[t,n];var i=e.props.bounds;i="string"==typeof i?i:function(e){return{left:e.left,top:e.top,right:e.right,bottom:e.bottom}}(i);var s=a(e);if("string"==typeof i){var l,c=s.ownerDocument,u=c.defaultView;if(!((l="parent"===i?s.parentNode:c.querySelector(i))instanceof u.HTMLElement))throw new Error('Bounds selector "'+i+'" could not find an element.');var f=u.getComputedStyle(s),p=u.getComputedStyle(l);i={left:-s.offsetLeft+(0,r.int)(p.paddingLeft)+(0,r.int)(f.marginLeft),top:-s.offsetTop+(0,r.int)(p.paddingTop)+(0,r.int)(f.marginTop),right:(0,o.innerWidth)(l)-(0,o.outerWidth)(s)-s.offsetLeft+(0,r.int)(p.paddingRight)-(0,r.int)(f.marginRight),bottom:(0,o.innerHeight)(l)-(0,o.outerHeight)(s)-s.offsetTop+(0,r.int)(p.paddingBottom)-(0,r.int)(f.marginBottom)}}(0,r.isNum)(i.right)&&(t=Math.min(t,i.right));(0,r.isNum)(i.bottom)&&(n=Math.min(n,i.bottom));(0,r.isNum)(i.left)&&(t=Math.max(t,i.left));(0,r.isNum)(i.top)&&(n=Math.max(n,i.top));return[t,n]},t.snapToGrid=function(e,t,n){var r=Math.round(t/e[0])*e[0],o=Math.round(n/e[1])*e[1];return[r,o]},t.canDragX=function(e){return"both"===e.props.axis||"x"===e.props.axis},t.canDragY=function(e){return"both"===e.props.axis||"y"===e.props.axis},t.getControlPosition=function(e,t,n){var r="number"==typeof t?(0,o.getTouch)(e,t):null;if("number"==typeof t&&!r)return null;var i=a(n),s=n.props.offsetParent||i.offsetParent||i.ownerDocument.body;return(0,o.offsetXYFromParent)(r||e,s,n.props.scale)},t.createCoreData=function(e,t,n){var o=e.state,i=!(0,r.isNum)(o.lastX),s=a(e);return i?{node:s,deltaX:0,deltaY:0,lastX:t,lastY:n,x:t,y:n}:{node:s,deltaX:t-o.lastX,deltaY:n-o.lastY,lastX:o.lastX,lastY:o.lastY,x:t,y:n}},t.createDraggableData=function(e,t){var n=e.props.scale;return{node:t.node,x:e.state.x+t.deltaX/n,y:e.state.y+t.deltaY/n,deltaX:t.deltaX/n,deltaY:t.deltaY/n,lastX:e.state.x,lastY:e.state.y}};var r=n(2),o=n(4);function a(e){var t=e.findDOMNode();if(!t)throw new Error("<DraggableCore>: Unmounted during event!");return t}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){void 0}},function(e,t,n){"use strict";var r,o=function(){return void 0===r&&(r=Boolean(window&&document&&document.all&&!window.atob)),r},a=function(){var e={};return function(t){if(void 0===e[t]){var n=document.querySelector(t);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}e[t]=n}return e[t]}}(),i=[];function s(e){for(var t=-1,n=0;n<i.length;n++)if(i[n].identifier===e){t=n;break}return t}function l(e,t){for(var n={},r=[],o=0;o<e.length;o++){var a=e[o],l=t.base?a[0]+t.base:a[0],c=n[l]||0,u="".concat(l," ").concat(c);n[l]=c+1;var f=s(u),p={css:a[1],media:a[2],sourceMap:a[3]};-1!==f?(i[f].references++,i[f].updater(p)):i.push({identifier:u,updater:m(p,t),references:1}),r.push(u)}return r}function c(e){var t=document.createElement("style"),r=e.attributes||{};if(void 0===r.nonce){var o=n.nc;o&&(r.nonce=o)}if(Object.keys(r).forEach((function(e){t.setAttribute(e,r[e])})),"function"==typeof e.insert)e.insert(t);else{var i=a(e.insert||"head");if(!i)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");i.appendChild(t)}return t}var u,f=(u=[],function(e,t){return u[e]=t,u.filter(Boolean).join("\n")});function p(e,t,n,r){var o=n?"":r.media?"@media ".concat(r.media," {").concat(r.css,"}"):r.css;if(e.styleSheet)e.styleSheet.cssText=f(t,o);else{var a=document.createTextNode(o),i=e.childNodes;i[t]&&e.removeChild(i[t]),i.length?e.insertBefore(a,i[t]):e.appendChild(a)}}function d(e,t,n){var r=n.css,o=n.media,a=n.sourceMap;if(o?e.setAttribute("media",o):e.removeAttribute("media"),a&&btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(a))))," */")),e.styleSheet)e.styleSheet.cssText=r;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(r))}}var h=null,g=0;function m(e,t){var n,r,o;if(t.singleton){var a=g++;n=h||(h=c(t)),r=p.bind(null,n,a,!1),o=p.bind(null,n,a,!0)}else n=c(t),r=d.bind(null,n,t),o=function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(n)};return r(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;r(e=t)}else o()}}e.exports=function(e,t){(t=t||{}).singleton||"boolean"==typeof t.singleton||(t.singleton=o());var n=l(e=e||[],t);return function(e){if(e=e||[],"[object Array]"===Object.prototype.toString.call(e)){for(var r=0;r<n.length;r++){var o=s(n[r]);i[o].references--}for(var a=l(e,t),c=0;c<n.length;c++){var u=s(n[c]);0===i[u].references&&(i[u].updater(),i.splice(u,1))}n=a}}}},function(e,t,n){"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n=function(e,t){var n=e[1]||"",r=e[3];if(!r)return n;if(t&&"function"==typeof btoa){var o=(i=r,s=btoa(unescape(encodeURIComponent(JSON.stringify(i)))),l="sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(s),"/*# ".concat(l," */")),a=r.sources.map((function(e){return"/*# sourceURL=".concat(r.sourceRoot||"").concat(e," */")}));return[n].concat(a).concat([o]).join("\n")}var i,s,l;return[n].join("\n")}(t,e);return t[2]?"@media ".concat(t[2]," {").concat(n,"}"):n})).join("")},t.i=function(e,n,r){"string"==typeof e&&(e=[[null,e,""]]);var o={};if(r)for(var a=0;a<this.length;a++){var i=this[a][0];null!=i&&(o[i]=!0)}for(var s=0;s<e.length;s++){var l=[].concat(e[s]);r&&o[l[0]]||(n&&(l[2]?l[2]="".concat(n," and ").concat(l[2]):l[2]=n),t.push(l))}},t}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DraggableCore",{enumerable:!0,get:function(){return u.default}}),t.default=void 0;var r=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!==h(e)&&"function"!=typeof e)return{default:e};var t=d();if(t&&t.has(e))return t.get(e);var n={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var a=r?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}n.default=e,t&&t.set(e,n);return n}(n(0)),o=p(n(1)),a=p(n(3)),i=p(n(13)),s=n(4),l=n(6),c=n(2),u=p(n(15)),f=p(n(7));function p(e){return e&&e.__esModule?e:{default:e}}function d(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return d=function(){return e},e}function h(e){return(h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function g(){return(g=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function m(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function b(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var n=[],r=!0,o=!1,a=void 0;try{for(var i,s=e[Symbol.iterator]();!(r=(i=s.next()).done)&&(n.push(i.value),!t||n.length!==t);r=!0);}catch(e){o=!0,a=e}finally{try{r||null==s.return||s.return()}finally{if(o)throw a}}return n}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return y(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return y(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function w(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){N(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function x(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function S(e,t,n){return t&&x(e.prototype,t),n&&x(e,n),e}function O(e,t){return(O=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function D(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=P(e);if(t){var o=P(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return j(this,n)}}function j(e,t){return!t||"object"!==h(t)&&"function"!=typeof t?E(e):t}function E(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function P(e){return(P=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function N(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var R=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&O(e,t)}(n,e);var t=D(n);function n(e){var r;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n),N(E(r=t.call(this,e)),"onDragStart",(function(e,t){if((0,f.default)("Draggable: onDragStart: %j",t),!1===r.props.onStart(e,(0,l.createDraggableData)(E(r),t)))return!1;r.setState({dragging:!0,dragged:!0})})),N(E(r),"onDrag",(function(e,t){if(!r.state.dragging)return!1;(0,f.default)("Draggable: onDrag: %j",t);var n=(0,l.createDraggableData)(E(r),t),o={x:n.x,y:n.y};if(r.props.bounds){var a=o.x,i=o.y;o.x+=r.state.slackX,o.y+=r.state.slackY;var s=b((0,l.getBoundPosition)(E(r),o.x,o.y),2),c=s[0],u=s[1];o.x=c,o.y=u,o.slackX=r.state.slackX+(a-o.x),o.slackY=r.state.slackY+(i-o.y),n.x=o.x,n.y=o.y,n.deltaX=o.x-r.state.x,n.deltaY=o.y-r.state.y}if(!1===r.props.onDrag(e,n))return!1;r.setState(o)})),N(E(r),"onDragStop",(function(e,t){if(!r.state.dragging)return!1;if(!1===r.props.onStop(e,(0,l.createDraggableData)(E(r),t)))return!1;(0,f.default)("Draggable: onDragStop: %j",t);var n={dragging:!1,slackX:0,slackY:0};if(Boolean(r.props.position)){var o=r.props.position,a=o.x,i=o.y;n.x=a,n.y=i}r.setState(n)})),r.state={dragging:!1,dragged:!1,x:e.position?e.position.x:e.defaultPosition.x,y:e.position?e.position.y:e.defaultPosition.y,prevPropsPosition:w({},e.position),slackX:0,slackY:0,isElementSVG:!1},!e.position||e.onDrag||e.onStop||console.warn("A `position` was applied to this <Draggable>, without drag handlers. This will make this component effectively undraggable. Please attach `onDrag` or `onStop` handlers so you can adjust the `position` of this element."),r}return S(n,null,[{key:"getDerivedStateFromProps",value:function(e,t){var n=e.position,r=t.prevPropsPosition;return!n||r&&n.x===r.x&&n.y===r.y?null:((0,f.default)("Draggable: getDerivedStateFromProps %j",{position:n,prevPropsPosition:r}),{x:n.x,y:n.y,prevPropsPosition:w({},n)})}}]),S(n,[{key:"componentDidMount",value:function(){void 0!==window.SVGElement&&this.findDOMNode()instanceof window.SVGElement&&this.setState({isElementSVG:!0})}},{key:"componentWillUnmount",value:function(){this.setState({dragging:!1})}},{key:"findDOMNode",value:function(){return this.props.nodeRef?this.props.nodeRef.current:a.default.findDOMNode(this)}},{key:"render",value:function(){var e,t=this.props,n=(t.axis,t.bounds,t.children),o=t.defaultPosition,a=t.defaultClassName,c=t.defaultClassNameDragging,f=t.defaultClassNameDragged,p=t.position,d=t.positionOffset,h=(t.scale,m(t,["axis","bounds","children","defaultPosition","defaultClassName","defaultClassNameDragging","defaultClassNameDragged","position","positionOffset","scale"])),b={},y=null,v=!Boolean(p)||this.state.dragging,x=p||o,S={x:(0,l.canDragX)(this)&&v?this.state.x:x.x,y:(0,l.canDragY)(this)&&v?this.state.y:x.y};this.state.isElementSVG?y=(0,s.createSVGTransform)(S,d):b=(0,s.createCSSTransform)(S,d);var O=(0,i.default)(n.props.className||"",a,(N(e={},c,this.state.dragging),N(e,f,this.state.dragged),e));return r.createElement(u.default,g({},h,{onStart:this.onDragStart,onDrag:this.onDrag,onStop:this.onDragStop}),r.cloneElement(r.Children.only(n),{className:O,style:w(w({},n.props.style),b),transform:y}))}}]),n}(r.Component);t.default=R,N(R,"displayName","Draggable"),N(R,"propTypes",w(w({},u.default.propTypes),{},{axis:o.default.oneOf(["both","x","y","none"]),bounds:o.default.oneOfType([o.default.shape({left:o.default.number,right:o.default.number,top:o.default.number,bottom:o.default.number}),o.default.string,o.default.oneOf([!1])]),defaultClassName:o.default.string,defaultClassNameDragging:o.default.string,defaultClassNameDragged:o.default.string,defaultPosition:o.default.shape({x:o.default.number,y:o.default.number}),positionOffset:o.default.shape({x:o.default.oneOfType([o.default.number,o.default.string]),y:o.default.oneOfType([o.default.number,o.default.string])}),position:o.default.shape({x:o.default.number,y:o.default.number}),className:c.dontSetMe,style:c.dontSetMe,transform:c.dontSetMe})),N(R,"defaultProps",w(w({},u.default.defaultProps),{},{axis:"both",bounds:!1,defaultClassName:"react-draggable",defaultClassNameDragging:"react-draggable-dragging",defaultClassNameDragged:"react-draggable-dragged",defaultPosition:{x:0,y:0},position:null,scale:1}))},function(e,t,n){"use strict";var r=n(12);function o(){}function a(){}a.resetWarningCache=o,e.exports=function(){function e(e,t,n,o,a,i){if(i!==r){var s=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function t(){return e}e.isRequired=e;var n={array:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:a,resetWarningCache:o};return n.PropTypes=n,n}},function(e,t,n){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},function(e,t,n){var r;
/*!
  Copyright (c) 2017 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/!function(){"use strict";var n={}.hasOwnProperty;function o(){for(var e=[],t=0;t<arguments.length;t++){var r=arguments[t];if(r){var a=typeof r;if("string"===a||"number"===a)e.push(r);else if(Array.isArray(r)&&r.length){var i=o.apply(null,r);i&&e.push(i)}else if("object"===a)for(var s in r)n.call(r,s)&&r[s]&&e.push(s)}}return e.join(" ")}e.exports?(o.default=o,e.exports=o):void 0===(r=function(){return o}.apply(t,[]))||(e.exports=r)}()},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getPrefix=o,t.browserPrefixToKey=a,t.browserPrefixToStyle=function(e,t){return t?"-".concat(t.toLowerCase(),"-").concat(e):e},t.default=void 0;var r=["Moz","Webkit","O","ms"];function o(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"transform";if("undefined"==typeof window||void 0===window.document)return"";var t=window.document.documentElement.style;if(e in t)return"";for(var n=0;n<r.length;n++)if(a(e,r[n])in t)return r[n];return""}function a(e,t){return t?"".concat(t).concat(function(e){for(var t="",n=!0,r=0;r<e.length;r++)n?(t+=e[r].toUpperCase(),n=!1):"-"===e[r]?n=!0:t+=e[r];return t}(e)):e}var i=o();t.default=i},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!==p(e)&&"function"!=typeof e)return{default:e};var t=f();if(t&&t.has(e))return t.get(e);var n={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var a=r?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}n.default=e,t&&t.set(e,n);return n}(n(0)),o=u(n(1)),a=u(n(3)),i=n(4),s=n(6),l=n(2),c=u(n(7));function u(e){return e&&e.__esModule?e:{default:e}}function f(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return f=function(){return e},e}function p(e){return(p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function d(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var n=[],r=!0,o=!1,a=void 0;try{for(var i,s=e[Symbol.iterator]();!(r=(i=s.next()).done)&&(n.push(i.value),!t||n.length!==t);r=!0);}catch(e){o=!0,a=e}finally{try{r||null==s.return||s.return()}finally{if(o)throw a}}return n}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return h(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return h(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function g(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function m(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function b(e,t){return(b=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function y(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=x(e);if(t){var o=x(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return v(this,n)}}function v(e,t){return!t||"object"!==p(t)&&"function"!=typeof t?w(e):t}function w(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function x(e){return(x=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function S(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var O={start:"touchstart",move:"touchmove",stop:"touchend"},D={start:"mousedown",move:"mousemove",stop:"mouseup"},j=D,E=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&b(e,t)}(u,e);var t,n,o,l=y(u);function u(){var e;g(this,u);for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return S(w(e=l.call.apply(l,[this].concat(n))),"state",{dragging:!1,lastX:NaN,lastY:NaN,touchIdentifier:null}),S(w(e),"mounted",!1),S(w(e),"handleDragStart",(function(t){if(e.props.onMouseDown(t),!e.props.allowAnyClick&&"number"==typeof t.button&&0!==t.button)return!1;var n=e.findDOMNode();if(!n||!n.ownerDocument||!n.ownerDocument.body)throw new Error("<DraggableCore> not mounted on DragStart!");var r=n.ownerDocument;if(!(e.props.disabled||!(t.target instanceof r.defaultView.Node)||e.props.handle&&!(0,i.matchesSelectorAndParentsTo)(t.target,e.props.handle,n)||e.props.cancel&&(0,i.matchesSelectorAndParentsTo)(t.target,e.props.cancel,n))){"touchstart"===t.type&&t.preventDefault();var o=(0,i.getTouchIdentifier)(t);e.setState({touchIdentifier:o});var a=(0,s.getControlPosition)(t,o,w(e));if(null!=a){var l=a.x,u=a.y,f=(0,s.createCoreData)(w(e),l,u);(0,c.default)("DraggableCore: handleDragStart: %j",f),(0,c.default)("calling",e.props.onStart),!1!==e.props.onStart(t,f)&&!1!==e.mounted&&(e.props.enableUserSelectHack&&(0,i.addUserSelectStyles)(r),e.setState({dragging:!0,lastX:l,lastY:u}),(0,i.addEvent)(r,j.move,e.handleDrag),(0,i.addEvent)(r,j.stop,e.handleDragStop))}}})),S(w(e),"handleDrag",(function(t){var n=(0,s.getControlPosition)(t,e.state.touchIdentifier,w(e));if(null!=n){var r=n.x,o=n.y;if(Array.isArray(e.props.grid)){var a=r-e.state.lastX,i=o-e.state.lastY,l=d((0,s.snapToGrid)(e.props.grid,a,i),2);if(a=l[0],i=l[1],!a&&!i)return;r=e.state.lastX+a,o=e.state.lastY+i}var u=(0,s.createCoreData)(w(e),r,o);if((0,c.default)("DraggableCore: handleDrag: %j",u),!1!==e.props.onDrag(t,u)&&!1!==e.mounted)e.setState({lastX:r,lastY:o});else try{e.handleDragStop(new MouseEvent("mouseup"))}catch(t){var f=document.createEvent("MouseEvents");f.initMouseEvent("mouseup",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),e.handleDragStop(f)}}})),S(w(e),"handleDragStop",(function(t){if(e.state.dragging){var n=(0,s.getControlPosition)(t,e.state.touchIdentifier,w(e));if(null!=n){var r=n.x,o=n.y,a=(0,s.createCoreData)(w(e),r,o);if(!1===e.props.onStop(t,a)||!1===e.mounted)return!1;var l=e.findDOMNode();l&&e.props.enableUserSelectHack&&(0,i.removeUserSelectStyles)(l.ownerDocument),(0,c.default)("DraggableCore: handleDragStop: %j",a),e.setState({dragging:!1,lastX:NaN,lastY:NaN}),l&&((0,c.default)("DraggableCore: Removing handlers"),(0,i.removeEvent)(l.ownerDocument,j.move,e.handleDrag),(0,i.removeEvent)(l.ownerDocument,j.stop,e.handleDragStop))}}})),S(w(e),"onMouseDown",(function(t){return j=D,e.handleDragStart(t)})),S(w(e),"onMouseUp",(function(t){return j=D,e.handleDragStop(t)})),S(w(e),"onTouchStart",(function(t){return j=O,e.handleDragStart(t)})),S(w(e),"onTouchEnd",(function(t){return j=O,e.handleDragStop(t)})),e}return t=u,(n=[{key:"componentDidMount",value:function(){this.mounted=!0;var e=this.findDOMNode();e&&(0,i.addEvent)(e,O.start,this.onTouchStart,{passive:!1})}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var e=this.findDOMNode();if(e){var t=e.ownerDocument;(0,i.removeEvent)(t,D.move,this.handleDrag),(0,i.removeEvent)(t,O.move,this.handleDrag),(0,i.removeEvent)(t,D.stop,this.handleDragStop),(0,i.removeEvent)(t,O.stop,this.handleDragStop),(0,i.removeEvent)(e,O.start,this.onTouchStart,{passive:!1}),this.props.enableUserSelectHack&&(0,i.removeUserSelectStyles)(t)}}},{key:"findDOMNode",value:function(){return this.props.nodeRef?this.props.nodeRef.current:a.default.findDOMNode(this)}},{key:"render",value:function(){return r.cloneElement(r.Children.only(this.props.children),{onMouseDown:this.onMouseDown,onMouseUp:this.onMouseUp,onTouchEnd:this.onTouchEnd})}}])&&m(t.prototype,n),o&&m(t,o),u}(r.Component);t.default=E,S(E,"displayName","DraggableCore"),S(E,"propTypes",{allowAnyClick:o.default.bool,disabled:o.default.bool,enableUserSelectHack:o.default.bool,offsetParent:function(e,t){if(e[t]&&1!==e[t].nodeType)throw new Error("Draggable's offsetParent must be a DOM Node.")},grid:o.default.arrayOf(o.default.number),handle:o.default.string,cancel:o.default.string,nodeRef:o.default.object,onStart:o.default.func,onDrag:o.default.func,onStop:o.default.func,onMouseDown:o.default.func,scale:o.default.number,className:l.dontSetMe,style:l.dontSetMe,transform:l.dontSetMe}),S(E,"defaultProps",{allowAnyClick:!1,cancel:null,disabled:!1,enableUserSelectHack:!0,offsetParent:null,handle:null,grid:null,transform:null,onStart:function(){},onDrag:function(){},onStop:function(){},onMouseDown:function(){},scale:1})},function(e,t,n){var r=n(8),o=n(17);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.i,o,""]]);var a={insert:"head",singleton:!1};r(o,a);e.exports=o.locals||{}},function(e,t,n){(t=n(9)(!1)).push([e.i,".react-resizable {\n  position: relative;\n}\n.react-resizable-handle {\n  position: absolute;\n  width: 20px;\n  height: 20px;\n  background-repeat: no-repeat;\n  background-origin: content-box;\n  box-sizing: border-box;\n  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA2IDYiIHN0eWxlPSJiYWNrZ3JvdW5kLWNvbG9yOiNmZmZmZmYwMCIgeD0iMHB4IiB5PSIwcHgiIHdpZHRoPSI2cHgiIGhlaWdodD0iNnB4Ij48ZyBvcGFjaXR5PSIwLjMwMiI+PHBhdGggZD0iTSA2IDYgTCAwIDYgTCAwIDQuMiBMIDQgNC4yIEwgNC4yIDQuMiBMIDQuMiAwIEwgNiAwIEwgNiA2IEwgNiA2IFoiIGZpbGw9IiMwMDAwMDAiLz48L2c+PC9zdmc+');\n  background-position: bottom right;\n  padding: 0 3px 3px 0;\n}\n.react-resizable-handle-sw {\n  bottom: 0;\n  left: 0;\n  cursor: sw-resize;\n  transform: rotate(90deg);\n}\n.react-resizable-handle-se {\n  bottom: 0;\n  right: 0;\n  cursor: se-resize;\n}\n.react-resizable-handle-nw {\n  top: 0;\n  left: 0;\n  cursor: nw-resize;\n  transform: rotate(180deg);\n}\n.react-resizable-handle-ne {\n  top: 0;\n  right: 0;\n  cursor: ne-resize;\n  transform: rotate(270deg);\n}\n.react-resizable-handle-w,\n.react-resizable-handle-e {\n  top: 50%;\n  margin-top: -10px;\n  cursor: ew-resize;\n}\n.react-resizable-handle-w {\n  left: 0;\n  transform: rotate(135deg);\n}\n.react-resizable-handle-e {\n  right: 0;\n  transform: rotate(315deg);\n}\n.react-resizable-handle-n,\n.react-resizable-handle-s {\n  left: 50%;\n  margin-left: -10px;\n  cursor: ns-resize;\n}\n.react-resizable-handle-n {\n  top: 0;\n  transform: rotate(225deg);\n}\n.react-resizable-handle-s {\n  bottom: 0;\n  transform: rotate(45deg);\n}",""]),e.exports=t},function(e,t,n){var r=n(8),o=n(19);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.i,o,""]]);var a={insert:"head",singleton:!1};r(o,a);e.exports=o.locals||{}},function(e,t,n){(t=n(9)(!1)).push([e.i,".custom-box {\n  overflow: visible;\n}\n.custom-handle {\n  position: absolute;\n  width: 8px;\n  height: 8px;\n  background-color: #1153aa;\n  opacity: 0.75;\n  border-radius: 4px;\n}\n.custom-handle-sw {\n  bottom: -4px;\n  left: -4px;\n  cursor: sw-resize;\n}\n.custom-handle-se {\n  bottom: -4px;\n  right: -4px;\n  cursor: se-resize;\n}\n.custom-handle-nw {\n  top: -4px;\n  left: -4px;\n  cursor: nw-resize;\n}\n.custom-handle-ne {\n  top: -4px;\n  right: -4px;\n  cursor: ne-resize;\n}\n.custom-handle-w,\n.custom-handle-e {\n  top: 50%;\n  margin-top: -4px;\n  cursor: ew-resize;\n}\n.custom-handle-w {\n  left: -4px;\n}\n.custom-handle-e {\n  right: -4px;\n}\n.custom-handle-n,\n.custom-handle-s {\n  left: 50%;\n  margin-left: -4px;\n  cursor: ns-resize;\n}\n.custom-handle-n {\n  top: -4px;\n}\n.custom-handle-s {\n  bottom: -4px;\n}",""]),e.exports=t},function(e,t,n){"use strict";n.r(t);var r=n(0),o=n.n(r),a=n(3),i=n.n(a),s=n(5);function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){u(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function u(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var f=n(1),p=n.n(f),d={axis:p.a.oneOf(["both","x","y","none"]),className:p.a.string,children:p.a.element.isRequired,draggableOpts:p.a.shape({allowAnyClick:p.a.bool,cancel:p.a.string,children:p.a.node,disabled:p.a.bool,enableUserSelectHack:p.a.bool,offsetParent:p.a.node,grid:p.a.arrayOf(p.a.number),handle:p.a.string,nodeRef:p.a.object,onStart:p.a.func,onDrag:p.a.func,onStop:p.a.func,onMouseDown:p.a.func,scale:p.a.number}),height:p.a.number.isRequired,handle:p.a.oneOfType([p.a.node,p.a.func]),handleSize:p.a.arrayOf(p.a.number),lockAspectRatio:p.a.bool,maxConstraints:p.a.arrayOf(p.a.number),minConstraints:p.a.arrayOf(p.a.number),onResizeStop:p.a.func,onResizeStart:p.a.func,onResize:p.a.func,resizeHandles:p.a.arrayOf(p.a.oneOf(["s","w","e","n","sw","nw","se","ne"])),transformScale:p.a.number,width:p.a.number.isRequired};function h(){return(h=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function g(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function m(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var b=function(e){var t,n;function r(){for(var t,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return m(g(t=e.call.apply(e,[this].concat(r))||this),"state",{slackW:0,slackH:0}),m(g(t),"lastHandleRect",null),m(g(t),"draggingNode",null),t}n=e,(t=r).prototype=Object.create(n.prototype),t.prototype.constructor=t,t.__proto__=n;var a=r.prototype;return a.lockAspectRatio=function(e,t,n){return[e=(t=e/n)*n,t]},a.runConstraints=function(e,t){var n=[this.props.minConstraints,this.props.maxConstraints],r=n[0],o=n[1];if(!r&&!o)return[e,t];if(this.props.lockAspectRatio)if(t===this.props.height){var a=this.props.width/this.props.height;e=(t=e/a)*a}else{var i=this.props.height/this.props.width;t=(e=t/i)*i}var s=e,l=t,c=this.state,u=c.slackW,f=c.slackH;return e+=u,t+=f,r&&(e=Math.max(r[0],e),t=Math.max(r[1],t)),o&&(e=Math.min(o[0],e),t=Math.min(o[1],t)),f+=l-t,(u+=s-e)===this.state.slackW&&f===this.state.slackH||this.setState({slackW:u,slackH:f}),[e,t]},a.resizeHandler=function(e,t){var n=this;return function(r,o){var a=o.node,i=o.deltaX,s=o.deltaY;i/=n.props.transformScale,s/=n.props.transformScale;var l=("both"===n.props.axis||"x"===n.props.axis)&&-1===["n","s"].indexOf(t),c=("both"===n.props.axis||"y"===n.props.axis)&&-1===["e","w"].indexOf(t);if(null==n.draggingNode&&r.target instanceof HTMLElement&&(n.draggingNode=r.target),n.draggingNode instanceof HTMLElement){var u=n.draggingNode.getBoundingClientRect();if(null!=n.lastHandleRect){var f=u.left-n.lastHandleRect.left,p=u.top-n.lastHandleRect.top;l&&"w"===t[t.length-1]&&(i+=f/n.props.transformScale),c&&"n"===t[0]&&(s+=p/n.props.transformScale)}n.lastHandleRect={top:u.top,left:u.left}}l&&"w"===t[t.length-1]&&(i=-i),c&&"n"===t[0]&&(s=-s);var d=n.props.width+(l?i:0),h=n.props.height+(c?s:0),g=d!==n.props.width,m=h!==n.props.height;if("onResize"!==e||g||m){var b=n.runConstraints(d,h);d=b[0],h=b[1];var y={};if("onResizeStart"===e);else if("onResizeStop"===e)y.slackW=y.slackH=0,n.lastHandleRect=n.draggingNode=null;else if(d===n.props.width&&h===n.props.height)return;"function"==typeof n.props[e]?("function"==typeof r.persist&&r.persist(),n.setState(y,(function(){return n.props[e](r,{node:a,size:{width:d,height:h},handle:t})}))):n.setState(y)}}},a.renderResizeHandle=function(e){var t=this.props.handle;return t?"function"==typeof t?t(e):t:o.a.createElement("span",{className:"react-resizable-handle react-resizable-handle-"+e})},a.render=function(){var e,t,n=this,r=this.props,a=r.children,i=r.draggableOpts,l=r.resizeHandles,u=r.className;return e=a,(t={className:(u?u+" ":"")+"react-resizable",children:[].concat(a.props.children,l.map((function(e){return o.a.createElement(s.DraggableCore,h({},i,{key:"resizableHandle-"+e,onStop:n.resizeHandler("onResizeStop",e),onStart:n.resizeHandler("onResizeStart",e),onDrag:n.resizeHandler("onResize",e)}),n.renderResizeHandle(e))})))}).style&&e.props.style&&(t.style=c(c({},e.props.style),t.style)),t.className&&e.props.className&&(t.className=e.props.className+" "+t.className),o.a.cloneElement(e,t)},r}(o.a.Component);function y(){return(y=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function v(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function w(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}m(b,"propTypes",d),m(b,"defaultProps",{handleSize:[20,20],lockAspectRatio:!1,axis:"both",minConstraints:[20,20],maxConstraints:[1/0,1/0],resizeHandles:["se"],transformScale:1});var x=function(e){var t,n;function o(){for(var t,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return w(v(t=e.call.apply(e,[this].concat(r))||this),"state",{width:t.props.width,height:t.props.height,propsWidth:t.props.width,propsHeight:t.props.height}),w(v(t),"onResize",(function(e,n){var r=n.size;t.props.onResize?(e.persist&&e.persist(),t.setState(r,(function(){return t.props.onResize&&t.props.onResize(e,n)}))):t.setState(r)})),t}return n=e,(t=o).prototype=Object.create(n.prototype),t.prototype.constructor=t,t.__proto__=n,o.getDerivedStateFromProps=function(e,t){return t.propsWidth!==e.width||t.propsHeight!==e.height?{width:e.width,height:e.height,propsWidth:e.width,propsHeight:e.height}:null},o.prototype.render=function(){var e=this.props,t=e.handle,n=e.handleSize,o=(e.onResize,e.onResizeStart),a=e.onResizeStop,i=e.draggableOpts,s=e.minConstraints,l=e.maxConstraints,c=e.lockAspectRatio,u=e.axis,f=(e.width,e.height,e.resizeHandles),p=e.transformScale,d=function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,["handle","handleSize","onResize","onResizeStart","onResizeStop","draggableOpts","minConstraints","maxConstraints","lockAspectRatio","axis","width","height","resizeHandles","transformScale"]);return r.createElement(b,{axis:u,draggableOpts:i,handle:t,handleSize:n,height:this.state.height,lockAspectRatio:c,maxConstraints:l,minConstraints:s,onResizeStart:o,onResize:this.onResize,onResizeStop:a,resizeHandles:f,transformScale:p,width:this.state.width},r.createElement("div",y({style:{width:this.state.width+"px",height:this.state.height+"px"}},d)))},o}(r.Component);w(x,"propTypes",d);n(16),n(18);function S(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function O(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var D=function(e){var t,n;function r(){for(var t,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return O(S(t=e.call.apply(e,[this].concat(r))||this),"state",{width:200,height:200,absoluteWidth:200,absoluteHeight:200,absoluteLeft:0,absoluteTop:0}),O(S(t),"onClick",(function(){t.setState({width:200,height:200,absoluteWidth:200,absoluteHeight:200})})),O(S(t),"onResize",(function(e,n){n.element;var r=n.size;n.handle;t.setState({width:r.width,height:r.height})})),O(S(t),"onResizeAbsolute",(function(e,n){n.element;var r=n.size,o=n.handle;t.setState((function(e){var t=e.absoluteLeft,n=e.absoluteTop,a=r.height-e.absoluteHeight,i=r.width-e.absoluteWidth;return"n"===o[0]?n-=a/2:"s"===o[0]&&(n+=a/2),"w"===o[o.length-1]?t-=i/2:"e"===o[o.length-1]&&(t+=i/2),{absoluteWidth:r.width,absoluteHeight:r.height,absoluteLeft:t,absoluteTop:n}}))})),t}return n=e,(t=r).prototype=Object.create(n.prototype),t.prototype.constructor=t,t.__proto__=n,r.prototype.render=function(){return o.a.createElement("div",null,o.a.createElement("button",{onClick:this.onClick,style:{marginBottom:"10px"}},"Reset first element's width/height"),o.a.createElement("div",{className:"layoutRoot"},o.a.createElement(b,{className:"box",height:this.state.height,width:this.state.width,onResize:this.onResize,resizeHandles:["sw","se","nw","ne","w","e","n","s"]},o.a.createElement("div",{className:"box",style:{width:this.state.width+"px",height:this.state.height+"px"}},o.a.createElement("span",{className:"text"},"Raw use of <Resizable> element. 200x200, all Resize Handles."))),o.a.createElement(x,{className:"box",width:200,height:200},o.a.createElement("span",{className:"text"},"<ResizableBox>")),o.a.createElement(x,{className:"custom-box box",width:200,height:200,handle:o.a.createElement("span",{className:"custom-handle custom-handle-se"}),handleSize:[8,8]},o.a.createElement("span",{className:"text"},"<ResizableBox> with custom handle in SE corner.")),o.a.createElement(x,{className:"custom-box box",width:200,height:200,handle:function(e){return o.a.createElement("span",{className:"custom-handle custom-handle-"+e})},handleSize:[8,8],resizeHandles:["sw","se","nw","ne","w","e","n","s"]},o.a.createElement("span",{className:"text"},"<ResizableBox> with custom handles in all locations.")),o.a.createElement(x,{className:"box",width:200,height:200,draggableOpts:{grid:[25,25]}},o.a.createElement("span",{className:"text"},"Resizable box that snaps to even intervals of 25px.")),o.a.createElement(x,{className:"box",width:200,height:200,minConstraints:[150,150],maxConstraints:[500,300]},o.a.createElement("span",{className:"text"},"Resizable box, starting at 200x200. Min size is 150x150, max is 500x300.")),o.a.createElement(x,{className:"box box3",width:200,height:200,minConstraints:[150,150],maxConstraints:[500,300]},o.a.createElement("span",{className:"text"},"Resizable box with a handle that only appears on hover.")),o.a.createElement(x,{className:"box",width:200,height:200,lockAspectRatio:!0},o.a.createElement("span",{className:"text"},"Resizable square with a locked aspect ratio.")),o.a.createElement(x,{className:"box",width:200,height:120,lockAspectRatio:!0},o.a.createElement("span",{className:"text"},"Resizable rectangle with a locked aspect ratio.")),o.a.createElement(x,{className:"box",width:200,height:200,axis:"x"},o.a.createElement("span",{className:"text"},'Only resizable by "x" axis.')),o.a.createElement(x,{className:"box",width:200,height:200,axis:"y"},o.a.createElement("span",{className:"text"},'Only resizable by "y" axis.')),o.a.createElement(x,{className:"box",width:200,height:200,axis:"both"},o.a.createElement("span",{className:"text"},'Resizable ("both" axis).')),o.a.createElement(x,{className:"box",width:200,height:200,axis:"none"},o.a.createElement("span",{className:"text"},'Not resizable ("none" axis).'))),o.a.createElement("div",{className:"layoutRoot absoluteLayout"},o.a.createElement(x,{className:"box absolutely-positioned top-aligned left-aligned",height:200,width:200,resizeHandles:["se","e","s"]},o.a.createElement("span",{className:"text"},"Top-left Aligned")),o.a.createElement(x,{className:"box absolutely-positioned bottom-aligned left-aligned",height:200,width:200,resizeHandles:["ne","e","n"]},o.a.createElement("span",{className:"text"},"Bottom-left Aligned")),o.a.createElement(b,{className:"box absolutely-positioned center-aligned",height:this.state.absoluteHeight,width:this.state.absoluteWidth,onResize:this.onResizeAbsolute,resizeHandles:["sw","se","nw","ne","w","e","n","s"]},o.a.createElement("div",{className:"box",style:{width:this.state.absoluteWidth,height:this.state.absoluteHeight,margin:this.state.absoluteTop+" 0 0 "+this.state.absoluteLeft}},o.a.createElement("span",{className:"text"},"Raw use of <Resizable> element with controlled position. Resize and reposition in all directions"))),o.a.createElement(x,{className:"box absolutely-positioned top-aligned right-aligned",height:200,width:200,resizeHandles:["sw","w","s"]},o.a.createElement("span",{className:"text"},"Top-right Aligned")),o.a.createElement(x,{className:"box absolutely-positioned bottom-aligned right-aligned",height:200,width:200,resizeHandles:["nw","w","n"]},o.a.createElement("span",{className:"text"},"Bottom-right Aligned"))))},r}(o.a.Component);document.addEventListener("DOMContentLoaded",(function(e){var t=document.getElementById("content");i.a.render(o.a.createElement(D),t)}))}])}));