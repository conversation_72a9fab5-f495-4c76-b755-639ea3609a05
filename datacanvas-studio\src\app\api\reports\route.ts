import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, canViewDashboard } from '@/lib/auth/auth';
import { DatabaseService } from '@/lib/services/database';

export async function GET(request: NextRequest) {
  try {
    const user = await requireAuth();
    
    if (!user.organization_id) {
      return NextResponse.json({ error: 'User not associated with an organization' }, { status: 400 });
    }

    const { searchParams } = new URL(request.url);
    const dashboardId = searchParams.get('dashboard_id');

    const supabase = await import('@/lib/supabase/server').then(m => m.createSupabaseServerClient());
    
    let query = (await supabase)
      .from('reports')
      .select(`
        *,
        dashboard:dashboards(id, name, organization_id)
      `)
      .order('created_at', { ascending: false });

    if (dashboardId) {
      query = query.eq('dashboard_id', dashboardId);
    }

    const { data: reports, error } = await query;

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Filter reports based on dashboard access
    const accessibleReports = [];
    for (const report of reports || []) {
      if (report.dashboard && report.dashboard.organization_id === user.organization_id) {
        // Check if user can view the dashboard
        if (canViewDashboard(user, report.dashboard, [])) {
          accessibleReports.push(report);
        }
      }
    }

    return NextResponse.json({ data: accessibleReports });

  } catch (error) {
    console.error('Get reports error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch reports' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth();
    
    const body = await request.json();
    const { 
      name, 
      dashboard_id, 
      schedule_config, 
      recipients, 
      format = 'pdf',
      is_active = true 
    } = body;

    // Validate required fields
    if (!name || !dashboard_id || !schedule_config || !recipients || !Array.isArray(recipients)) {
      return NextResponse.json({ 
        error: 'Name, dashboard_id, schedule_config, and recipients are required' 
      }, { status: 400 });
    }

    // Validate format
    if (!['pdf', 'excel', 'csv'].includes(format)) {
      return NextResponse.json({ error: 'Invalid format' }, { status: 400 });
    }

    // Validate recipients
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const invalidEmails = recipients.filter((email: string) => !emailRegex.test(email));
    if (invalidEmails.length > 0) {
      return NextResponse.json({ 
        error: `Invalid email addresses: ${invalidEmails.join(', ')}` 
      }, { status: 400 });
    }

    // Check if user can view the dashboard
    const dashboardResult = await DatabaseService.getDashboard(dashboard_id);
    if (dashboardResult.error || !dashboardResult.data) {
      return NextResponse.json({ error: 'Dashboard not found' }, { status: 404 });
    }

    const permissions = (dashboardResult.data as any).permissions || [];
    if (!canViewDashboard(user, dashboardResult.data, permissions)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Validate schedule configuration
    const validFrequencies = ['daily', 'weekly', 'monthly'];
    if (!validFrequencies.includes(schedule_config.frequency)) {
      return NextResponse.json({ 
        error: 'Invalid frequency. Must be daily, weekly, or monthly' 
      }, { status: 400 });
    }

    // Create report
    const supabase = await import('@/lib/supabase/server').then(m => m.createSupabaseServerClient());
    const { data: report, error: createError } = await (await supabase)
      .from('reports')
      .insert({
        name,
        dashboard_id,
        schedule_config,
        recipients,
        format,
        is_active,
        created_by: user.id,
      })
      .select()
      .single();

    if (createError) {
      return NextResponse.json({ error: createError.message }, { status: 500 });
    }

    // Create audit log
    await DatabaseService.createAuditLog({
      action: 'report_created',
      resource_type: 'report',
      resource_id: report.id,
      user_id: user.id,
      metadata: {
        name,
        dashboard_id,
        format,
        frequency: schedule_config.frequency,
        recipients_count: recipients.length,
      },
    });

    return NextResponse.json({
      data: report,
      message: 'Report created successfully',
    });

  } catch (error) {
    console.error('Create report error:', error);
    return NextResponse.json(
      { error: 'Failed to create report' },
      { status: 500 }
    );
  }
}
